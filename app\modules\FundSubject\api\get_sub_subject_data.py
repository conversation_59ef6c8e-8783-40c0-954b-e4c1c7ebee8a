import asyncio
import concurrent
import json
from concurrent.futures import ThreadPoolExecutor

import requests
from PyQt6.QtCore import QThread, pyqtSignal
class get_sub_subject_data(QThread):
    finished = pyqtSignal(list,list,list,int)#增长率，排名，表数据，总页数
    def __init__(self,bk_code,page):
        super(). __init__()
        self.bk_code=bk_code
        self.len_data=1
        self.page=page
        self.rate_list=[]
        self.rank_list=[]
        self.fund_data=[]
        self.headers={
              "Host": "api.fund.eastmoney.com",
              "Connection": "keep-alive",
              "sec-ch-ua-platform": "\"Windows\"",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "sec-ch-ua": "\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
              "sec-ch-ua-mobile": "?0",
              "Accept": "*/*",
              "Sec-Fetch-Site": "same-site",
              "Sec-Fetch-Mode": "no-cors",
              "Sec-Fetch-Dest": "script",
              "Referer": "https://fund.eastmoney.com/",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; st_si=60258197461486; st_asi=delete; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=2; st_psi=20250626185100247-112200312942-5453917833"
            }
        self.info_url=f"https://api.fund.eastmoney.com/ZTJJ/GetBKDetailInfoNew?callback=jQuery183048379438313213396_1750943916886&tp={self.bk_code}&_=1750943916984"
        self.fund_url=f"https://api.fund.eastmoney.com/ZTJJ/GetBKRelTopicFundNew?callback=jQuery183003420881405741594_1747038772390&sort=undefined&sorttype=DESC&pageindex={self.page}&pagesize=20&tp={self.bk_code}&isbuy=1&_=1747038772718"

    def format_value(self,value):
        if value=="":
            return "--"
        else:
            return "{:.2f}%".format(float(value))

    def format_value_2(self,value):
        if value == "":
            return "--"
        else:
            return f"{value}%"

    def get_top_data(self):
        try:
            response = requests.get(self.info_url, headers=self.headers, verify=False)
            response_text = json.loads(response.text.strip("jQuery18305820809459464454_1747037513257(").replace(")", ""))
            data = response_text["Data"]
            self.rate_list = [data["D"], data["W"], data["M"], data["Q"],data["Y"],data["SY"]]
            # rank_list=extend拼接日涨幅排
            self.rank_list = ["--",
                         f"{int(data["RANKW"])}/{int(data["WSC"])}",
                         f"{int(data["RANKM"])}/{int(data["MSC"])}",
                         f"{int(data["RANKQ"])}/{int(data["QSC"])}",
                         f"{int(data["RANKY"])}/{int(data["YSC"])}",
                         f"{int(data["RANKSY"])}/{int(data["SYSC"])}", ]
        except Exception as e:
            print(f"exception get_top_data :{e}")

    def get_sub_table_data(self):
        try:
            response = requests.get(self.fund_url, headers=self.headers, verify=False)
            response_text = json.loads(response.text.strip("jQuery183003420881405741594_1747038772390(").replace(")", ""))

            for i in response_text["Data"]:
                t = [
                    i["FCODE"],  # 基金代码
                    i["SHORTNAME"],  # 基金名称
                    i["FTYPE"],  # 基金类型
                    i["SYRQ"],  # 日期
                    i["DWJZ"],  # 最新净值
                    self.format_value(i["RELATION"]),  # 持仓占比,
                    self.format_value_2(i["RZDF"]),  # 日增长率
                    self.format_value_2(i["SYL_Z"]),
                    self.format_value_2(i["SYL_Y"]),
                    self.format_value_2(i["SYL_3Y"]),
                    self.format_value_2(i["SYL_6Y"]),
                    self.format_value_2(i["SYL_JN"]),
                    self.format_value_2(i["SYL_1N"]),
                    self.format_value_2(i["SYL_2N"]),
                    self.format_value_2(i["SYL_3N"]),
                    self.format_value_2(i["SYL_LN"]),  # 成立来
                ]
                self.fund_data.append(t)
                self.len_data = response_text["TotalCount"]
                print(self.len_data)
        except Exception as e:
                print(f"exception get_sub_table_data :{e}")

    def run(self):
        try:
            with ThreadPoolExecutor(max_workers=2) as executor:
                future_info= executor.submit(self.get_top_data)
                future_fund = executor.submit(self.get_sub_table_data)
                concurrent.futures.wait([future_info, future_fund])
            self.finished.emit(self.rate_list, self.rank_list, self.fund_data,self.len_data )
        except Exception as e:
            print(f"Error in run get_sub_subject_data method: {e}")






