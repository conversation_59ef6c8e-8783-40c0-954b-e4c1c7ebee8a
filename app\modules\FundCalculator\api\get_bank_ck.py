import asyncio
import json

import aiohttp
from PyQt6.QtCore import QThread, pyqtSignal
import requests
from bs4 import BeautifulSoup


class get_bank_ck_data():
    def __init__(self, search_name,page):
        self.search_name=search_name
        self.results = []
        self.page=page
        self.header = {
            "User-Agent": " Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0",
            "referer": "https://www.csai.cn/bankrate/",
            "Cookie":"_sid_=b480137c68b2abde81f32d89102e3a32; EGG_SESSION_ID=RgLE9MzppEKOkeK1iGdVf1IbYx7r-R4yhjNrzHFFgTphD-bJhzk3_3YuWPnwIxmb5BppSDl1FQdKa9rEfz71-gCBytOwa3FgUSEd3WEZD3mE7JQeSXofgYdjDFZmyLwBKUJU6uQwMNs2BrgPJW1hgA=="
        }
        self.url_list=[]
        for page in range(1, page + 1):
            self.url_list.append(
                f"https://www.csai.cn/bankrate/newBankView.do?searchName={self.search_name}&page={page}&banksort=&fieldName=&scrollId=dingqiDeposit")
        print( self.url_list)

    async def fetch_url_content(self, session, url):
        try:
            async with session.get(url) as response:
                data = await response.text()
                soup = BeautifulSoup(data, 'html.parser')
                bank_rows = soup.find_all('div', class_='tables_tr')
                bank_ck_list = []
                # 遍历每个银行行
                for row in bank_rows:
                    bank_name = [row.find('a').get_text(strip=True)]
                    bank_tds = row.find_all('span', class_='bank_td')
                    numbers = [td.get_text(strip=True) for td in bank_tds]
                    if len(numbers) == 6:
                        bank_name.extend(numbers)
                        bank_ck_list.append(bank_name)

                # for i in bank_ck_dict:
                #     print(i, bank_ck_dict[i])
                # print(bank_ck_list)
                return bank_ck_list
        except requests.exceptions.RequestException as e:
            return f"请求失败: {e}"

    async def main(self):
        async with aiohttp.ClientSession() as session:
            tasks = [self.fetch_url_content(session, url) for url in self.url_list]
            results = await asyncio.gather(*tasks)
            print(results)
            print("**")
            for i in results:
                self.results.extend(i)
        return self.results

get_bank_ck_data=get_bank_ck_data



