import json
from bs4 import BeautifulSoup
import requests
import time
header={
    "Referer":"https://so.eastmoney.com/",
    "Cookie":"qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=63307939966789; fund_registerAd_1=1; fullscreengg=1; fullscreengg2=1; emsiderzk=close; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=37; st_psi=20250323193223400-118000300903-9169591590; st_asi=delete",
    "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
}
url="https://recommend.eastmoney.com/soapi/api/articlesearch/search?cb=jQuery35103633732065296349_1742729395&count=81&Userid=&id=50d74afee65419b05e9120f0df53c69f&_=1742729395"
data=requests.get(url,verify=False).text
data=data.strip("jQuery35103633732065296349_1742708625183([{").replace("}])","").split("},{")
l=["{"+i+"}" for i in data]
url_l,img_url_l,title_l=[],[],[]
print(l)
for i in l:
    ll=json.loads(i)
    title_l.append(ll["Title"])
    img_url_l.append(ll["ImageUrl"])
    url_l.append(ll["Url"])
print(url_l)

# url="https://finance.eastmoney.com/a/202503233353284383.html"#3
# # url="https://finance.eastmoney.com/a/202503233353241947.html"
# data=requests.get(url,headers=header,verify=False).text
# soup = BeautifulSoup(data, 'html.parser')
#
# # 查找所有class为"item"的div标签
# items = soup.find_all('div', class_='item')
# print(len(items))
# # 提取日期和来源
# if len(items) == 5:
#     date = items[0].text.strip()  # 第一个item是日期
#     source = items[1].text.replace("来源：", "").strip()  # 第二个item是来源，去掉"来源："前缀
#     print("事件日期:", date)
#     print("来源:", source)
# elif len(items)==6:
#     date = items[0].text.strip()  # 第一个item是日期
#     source = items[2].text.replace("来源：", "").strip()  # 第二个item是来源，去掉"来源："前缀
#     print("事件日期:", date)
#     print("来源:", source)
# else:
#     print("未找到日期和来源信息")
#


