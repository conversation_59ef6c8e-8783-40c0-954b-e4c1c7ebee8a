import os
from datetime import datetime, timedelta

from dateutil.relativedelta import relativedelta

from app.modules.FundCompare.api.get_compare_ import get_compare_data


class DataHandle():
    def __init__(self):
        self.select_fund_file_main_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSelection\src\select_"

    def get_past_dates(self):
        today = datetime.today()
        one_week_ago = today - timedelta(weeks=1)
        one_month_ago = today - relativedelta(months=1)
        three_months_ago = today - relativedelta(months=3)
        six_months_ago = today - relativedelta(months=6)
        one_year_ago = today - relativedelta(years=1)
        two_years_ago = today - relativedelta(years=2)
        three_years_ago = today - relativedelta(years=3)
        formatted_dates = {
            "近1周": one_week_ago.strftime('%Y-%m-%d'),
            "近1月": one_month_ago.strftime('%Y-%m-%d'),
            "近3月": three_months_ago.strftime('%Y-%m-%d'),
            "近6月": six_months_ago.strftime('%Y-%m-%d'),
            "近1年": one_year_ago.strftime('%Y-%m-%d'),
            "近2年": two_years_ago.strftime('%Y-%m-%d'),
            "近3年": three_years_ago.strftime('%Y-%m-%d')
        }
        return formatted_dates

    def get_today_date(self):
        today = datetime.today()
        return today.strftime('%Y-%m-%d')

    def get_fund_group_data(self,):
        # folder_path_default = f"{self.select_fund_file_main_path}\\default"
        folder_path_other = f"{self.select_fund_file_main_path}\\other"
        # files_default = os.listdir(folder_path_default)
        files_other= os.listdir(folder_path_other)
        files=["默认"]
        # files_default = [f for f in files_default if os.path.isfile(os.path.join(folder_path_default, f))]
        files_other = [f.replace(".txt", "") for f in files_other if os.path.isfile(os.path.join(folder_path_other, f))]
        files.extend(files_other)  # 输出文件名列表
        # print(files)
        return files



DataHandle=DataHandle()

