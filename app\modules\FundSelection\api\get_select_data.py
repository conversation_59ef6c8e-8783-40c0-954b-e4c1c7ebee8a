import asyncio
import concurrent.futures
import json
import time
from concurrent.futures import ThreadPoolExecutor

from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy

from app.modules.FundSelection.api.get_select_date_SYL import get_select_date_SYL


class get_select_data(QThread):
    finished = pyqtSignal(list, list )
    def __init__(self,code_list,date_list):
        super().__init__()
        self.header={
        "Host": "api.fund.eastmoney.com",
        "Connection": "keep-alive",
        "Content-Length": "103",
        "sec-ch-ua-platform": "\"Windows\"",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "sec-ch-ua-mobile": "?0",
        "Origin": "https://favor.fund.eastmoney.com",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": "https://favor.fund.eastmoney.com/",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
        "Cookie":"qgqp_b_id=50d74afee65419b05e9120f0df53c69f; IsHaveToNewFavor=0; AUTH_FUND.EASTMONEY.COM_GSJZ=AUTH*TTJJ*TOKEN; HAList=ty-90-BK0481-%u6C7D%u8F66%u96F6%u90E8%u4EF6%2Cty-0-159876-%u6709%u8272%u9F99%u5934ETF%2Cty-90-BK1037-%u6D88%u8D39%u7535%u5B50%2Cty-1-600489-%u4E2D%u91D1%u9EC4%u91D1; st_si=81646649172854; st_asi=delete; ap_0_13a7d068=1; ap_1_68c1f65b=1; EMFUND0=04-29%2022%3A38%3A12@%23%24%u5E7F%u53D1%u5148%u8FDB%u5236%u9020%u80A1%u7968%u53D1%u8D77%u5F0FC@%23%24014192; EMFUND1=04-30%2016%3A01%3A06@%23%24%u6052%u8D8A%u5320%u5FC3%u4F18%u9009%u4E00%u5E74%u6301%u6709%u6DF7%u5408A@%23%24015150; EMFUND2=04-30%2015%3A26%3A00@%23%24%u8BFA%u5B89%u6052%u946B%u6DF7%u5408@%23%24006429; EMFUND3=04-30%2015%3A26%3A01@%23%24%u5357%u65B9%u745E%u7965%u4E00%u5E74%u6DF7%u5408A@%23%24005810; EMFUND4=04-30%2013%3A09%3A09@%23%24%u4E07%u5BB6%u5168%u7403%u6210%u957F%u4E00%u5E74%u6301%u6709%u671F%u6DF7%u5408%28QDII%29A@%23%24012535; EMFUND5=04-30%2015%3A35%3A11@%23%24%u6052%u8D8A%u5320%u5FC3%u4F18%u9009%u4E00%u5E74%u6301%u6709%u6DF7%u5408C@%23%24015151; EMFUND6=04-30%2015%3A37%3A09@%23%24%u534E%u5B9D%u6709%u8272%u91D1%u5C5EETF@%23%24159876; EMFUND7=04-30%2016%3A17%3A46@%23%24%u5E7F%u53D1%u6539%u9769%u6DF7%u5408@%23%24001468; EMFUND8=05-01%2018%3A15%3A30@%23%24%u534E%u590F%u6210%u957F%u6DF7%u5408@%23%24000001; EMFUND9=05-01 18:16:04@#$%u5BCC%u8363%u533B%u836F%u5065%u5EB7%u6DF7%u5408%u53D1%u8D77C@%23%24015656; Eastmoney_Fund=000001_002230_000331_000655_161725_011103_005669_166301_012768_320007_015150; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=44; st_psi=20250501200212824-119146300572-4993512930"
    }
        self.code_list = code_list
        self.date_list = date_list
        self.result_JZLB_list=[]
        self.result_ZFPH_list=[]
        self.params = {
            "fcodes": ",".join(map(str, code_list))
        }
        print(self.params)
        self.JZLB_url = "https://api.fund.eastmoney.com/favor/GetFundsInfo"
        self.ZFPH_url ="https://api.fund.eastmoney.com/favor/ranknew"

    def data_format(self, s):
        if s == "":
            return "--"
        else:
            return "{}%".format(s)

    def check_two(self,code_list,res_l):
        if "000001" not in code_list and "000011" not in code_list:
            return res_l[:-2]
        elif "000001" in code_list :
            for i in range(len(res_l)):
                if res_l[i]["FCODE"]=="000011":
                    return res_l[:i] + res_l[i+1:]
        elif "000011" in code_list :
            for i in range(len(res_l)):
                if res_l[i]["FCODE"]=="000001":
                    return res_l[:i] + res_l[i+1:]

    def return_JZLB_data(self):
        try:
            response = requests.post(self.JZLB_url, headers=self.header, data=self.params, verify=False)
            res_l = json.loads(response.text)["Data"]
            ll=self.check_two(self.code_list,res_l["KFS"])
            # res_l["HBX"] + res_l["LCX"] + res_l["CN"] + res_l["HK"]
            # for i in ll:
            #     print(i)
            for i in ll:
                l = [
                    i["FCODE"],  # 代码
                    i["SHORTNAME"],  # 名称
                    i["FTYPE"],  # 类型
                    i["FSRQ"],  # 最新日期
                    i["DWJZ"],  # 单位净值
                    i["LJJZ"],  # 累计净值
                    i["RZDE"],  # 日增长值
                    self.data_format(i["RZDF"]),  # 日增长率
                    self.data_format(i["SYL_LN"])  # 成立来
                ]
                self.result_JZLB_list.append(l)
            print( self.result_JZLB_list)
        except Exception as e:
            print(e)

    def return_ZFPH_data(self):
        try:
            response = requests.post(self.ZFPH_url, headers=self.header, data=self.params, verify=False)
            res_l = json.loads(response.text)["Data"]
            for i in res_l:
                l = [
                    i["FCODE"],  # 代码
                    i["SHORTNAME"],  # 名称
                    i["PDATE"],  # 日期
                    # 添加自选日期，
                    # 添加以来收益率
                    self.data_format(i["SYL_Z"]),  # 近一周
                    self.data_format(i["SYL_Y"]),  # 近一月
                    self.data_format(i["SYL_3Y"]),  # 近三月
                    self.data_format(i["SYL_6Y"]),  # 近六月
                    self.data_format(i["SYL_JN"]),  # 今年来
                    self.data_format(i["SYL_1N"]),  # 近一年
                    self.data_format(i["SYL_2N"]),  # 近两年
                    self.data_format(i["SYL_3N"]),  # 近三年
                    self.data_format(i["SYL_5N"]),  # 近五年
                ]
                self.result_ZFPH_list.append(l)

        except Exception as e:
            print(e)

    def add_data_to_ZFPH(self,add_date,add_data,ori_data):
        for i in range(len(ori_data)):
            ori_data[i].insert(3,add_date[i])
            ori_data[i].insert(4,add_data[i])
        return ori_data

    def run(self):
        try:

            if  self.code_list:
                with ThreadPoolExecutor(max_workers=2) as executor:
                    future_JZLB = executor.submit(self.return_JZLB_data)
                    future_ZFPH = executor.submit(self.return_ZFPH_data)
                    concurrent.futures.wait([future_ZFPH, future_JZLB])
                s = time.time()
                data=asyncio.run(get_select_date_SYL(self.code_list,self.date_list).main())
                e = time.time()
                print(e - s)
                self.result_ZFPH_list=self.add_data_to_ZFPH(self.date_list,data,self.result_ZFPH_list)
                self.finished.emit(self.result_JZLB_list, self.result_ZFPH_list)
            else:
                self.finished.emit([], [])

        except Exception as e:
            print(f"Error in run method: {e}")


