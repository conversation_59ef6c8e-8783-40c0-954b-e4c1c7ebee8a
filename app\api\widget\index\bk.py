# coding=utf-8
import requests
import json
import pandas as pd
from datetime import datetime


def get_zijinliu_dfcfw(days='1d', kind='行业'):
    # 获取资金流。
    if kind == '行业':
        url = "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery1123041371509725124067_1667389985005&fid=f62&po=1&pz=500&pn=1&np=1&fltt=2&invt=2&ut=b2884a393a59ad64002292a3e90d46a5&fs=m%3A90+t%3A2&fields=f12%2Cf14%2Cf2%2Cf3%2Cf62%2Cf184%2Cf66%2Cf69%2Cf72%2Cf75%2Cf78%2Cf81%2Cf84%2Cf87%2Cf204%2Cf205%2Cf124%2Cf1%2Cf13"
    if kind == '概念':
        url = "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery1123041371509725124067_1667389985011&fid=f62&po=1&pz=500&pn=1&np=1&fltt=2&invt=2&ut=b2884a393a59ad64002292a3e90d46a5&fs=m%3A90+t%3A3&fields=f12%2Cf14%2Cf2%2Cf3%2Cf62%2Cf184%2Cf66%2Cf69%2Cf72%2Cf75%2Cf78%2Cf81%2Cf84%2Cf87%2Cf204%2Cf205%2Cf124%2Cf1%2Cf13"
    if kind == '地域':
        url = "https://push2.eastmoney.com/api/qt/clist/get?cb=jQuery1123041371509725124067_1667389985005&fid=f62&po=1&pz=50&pn=1&np=1&fltt=2&invt=2&ut=b2884a393a59ad64002292a3e90d46a5&fs=m%3A90+t%3A1&fields=f12%2Cf14%2Cf2%2Cf3%2Cf62%2Cf184%2Cf66%2Cf69%2Cf72%2Cf75%2Cf78%2Cf81%2Cf84%2Cf87%2Cf204%2Cf205%2Cf124%2Cf1%2Cf13"

    cols = ['f1', 'f12', 'f13', 'f14', 'f2', 'f3', 'f62', 'f184', 'f66', 'f69', 'f72', 'f75', 'f78', 'f81', 'f84',
            'f87', 'f204', 'f205']
    cols_c = {'f1': '2', 'f12': '板块代码', 'f13': '类型', 'f14': '版块名称', 'f2': '最新价', 'f3': '今日涨跌幅',
              'f62': '今日主力净流入净额', 'f184': '今日主力净流入净占比', 'f66': '今日超大单净流入净额',
              'f69': '今日超大单净流入净占比', 'f72': '今日大单净流入净额', 'f75': '今日大单净流入净占比',
              'f78': '今日中单净流入净额', 'f81': '今日中单净流入净占比', 'f84': '今日小单净流入净额',
              'f87': '今日小单净流入净占比', 'f204': '今日流入最大股', 'f205': '今日code'}
    url_5d = '%2Cf160%2Cf174%2Cf175%2Cf176%2Cf177%2Cf178%2Cf179%2Cf180%2Cf181%2Cf182%2Cf183%2Cf260%2Cf261'
    cols_5d = ['f109', 'f164', 'f165', 'f166', 'f167', 'f168', 'f169', 'f170', 'f171', 'f172', 'f173', 'f257', 'f258']
    cols_5d_c = {'f109': '5日涨跌幅', 'f164': '5日主力净流入净额', 'f165': '5日主力净流入净占比',
                 'f166': '5日超大单净流入净额', 'f167': '5日超大单净流入净占比', 'f168': '5日大单净流入净额',
                 'f169': '5日大单净流入净占比', 'f170': '5日中单净流入净额', 'f171': '5日中单净流入净占比',
                 'f172': '5日小单净流入净额', 'f173': '5日小单净流入净占比', 'f257': '5日流入最大股', 'f258': '5日code'}
    url_10d = '%2Cf109%2Cf164%2Cf165%2Cf166%2Cf167%2Cf168%2Cf169%2Cf170%2Cf171%2Cf172%2Cf173%2Cf257%2Cf258'
    cols_10d = ['f160', 'f174', 'f175', 'f176', 'f177', 'f178', 'f179', 'f180', 'f181', 'f182', 'f183', 'f260', 'f261']
    cols_10d_c = {'f160': '10日涨跌幅', 'f174': '10日主力净流入净额', 'f175': '10日主力净流入净占比',
                  'f176': '10日超大单净流入净额', 'f177': '10日超大单净流入净占比', 'f178': '10日大单净流入净额',
                  'f179': '10日大单净流入净占比', 'f180': '10日中单净流入净额', 'f181': '10日中单净流入净占比',
                  'f182': '10日小单净流入净额', 'f183': '10日小单净流入净占比', 'f260': '10日流入最大股',
                  'f261': '10日code'}
    if '5d' in days:
        url = url + url_5d
        cols = cols + cols_5d
        cols_c = dict(cols_c, **cols_5d_c)
    if '10d' in days:
        url = url + url_10d
        cols = cols + cols_10d
        cols_c = dict(cols_c, **cols_10d_c)

    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/67.0.3396.79 Safari/537.36'
    }

    # 循环执行，直至获取成功
    loop = True
    while loop == True:
        try:
            response = requests.get(url, headers=headers, timeout=3)
            response.encoding = 'utf-8'
        except Exception as e:
            loop = True
        else:
            if response.content:
                a = response.text.find('(', 1) + 1  # 从第一个左括号+1位置开始
                b = response.text.rfind(')') - len(response.text)  # 结束于右括号
                json_str = response.text[a:b]  # 截取字符串
                # print(json_str)
                if len(json_str) > 50:  # 获取行情信息长度如果小于50字符，则失败
                    kdata = json.loads(json_str)
                    if kdata['data']:
                        # print(kdata['data'])
                        df_data = pd.DataFrame(kdata['data']['diff'])
                        df_data = df_data[cols]
                        df_data = df_data.replace('-', 0)
                        # 中文字段名
                        df_data = df_data[cols].rename(index=str, columns=cols_c)
                        # 增加交易时间，方便后期显示
                        df_data['trade_time'] = str(datetime.now().strftime('%Y-%m-%d'))
                        df_data.index = pd.DatetimeIndex(df_data['trade_time'])
                        loop == False
                        return {
                            "success": True,
                            "df_data": df_data.round(2)
                        }
                    else:
                        return {
                            "success": False,
                            "msg": '获取行情信息结果为None'
                        }
            else:
                return {
                    "success": False,
                    "msg": '获取行情信息失败'
                }


# ===============表格美化输出===============
def df_table(df, index):
    import prettytable as pt
    # 利用prettytable对输出结果进行美化,index为索引列名:df_table(df,'market')
    tb = pt.PrettyTable()
    df = df.reset_index(drop=True)
    tb.add_column(index, df.index)
    for col in df.columns.values:  # df.columns.values的意思是获取列的名称
        tb.add_column(col, df[col])
    print(tb)


if __name__ == '__main__':
    '''使用此函数可以快速获取主力资金流向，通过观察今日、5日内、10日内资金流向分析行业、概念、地域变化'''
    # from KTstock import get_zijinliu_dfcfw # 将文件保存为KTstock，同目录引用方法取消注释即可
    import time

    # 计时开始
    time1 = time.time()

    if 1:
        days = ['1d', '5d', '10d']  # 默认提取1d，也即当天的，当然有人想一把提取当天、5天、10天的自己变化，我们的函数也是支持的。
        kind = '行业'  # 默认按行业进行划分提取，还可设置为 '概念'或'地域'
        ret = get_zijinliu_dfcfw(days, kind)
        cols = ['板块代码', '类型', '版块名称', '最新价', '今日涨跌幅', '今日主力净流入净额', '今日主力净流入净占比',
                '今日超大单净流入净额', '今日超大单净流入净占比', '今日大单净流入净额', '今日大单净流入净占比',
                '今日中单净流入净额', '今日中单净流入净占比', '今日小单净流入净额', '今日小单净流入净占比',
                '今日流入最大股', '今日code']
        cols_5d = ['5日涨跌幅', '5日主力净流入净额', '5日主力净流入净占比', '5日超大单净流入净额',
                   '5日超大单净流入净占比', '5日大单净流入净额', '5日大单净流入净占比', '5日中单净流入净额',
                   '5日中单净流入净占比', '5日小单净流入净额', '5日小单净流入净占比', '5日流入最大股', '5日code']
        cols_10d = ['10日涨跌幅', '10日主力净流入净额', '10日主力净流入净占比', '10日超大单净流入净额',
                    '10日超大单净流入净占比', '10日大单净流入净额', '10日大单净流入净占比', '10日中单净流入净额',
                    '10日中单净流入净占比', '10日小单净流入净额', '10日小单净流入净占比', '10日流入最大股', '10日code']
        if '5d' in days:
            cols = cols + cols_5d
        if '10d' in days:
            cols = cols + cols_10d
        if ret['success']:
            df_data = ret['df_data']
            df_data = df_data.sort_values(by='今日主力净流入净额', ascending=False)
            print(df_data.head(10))
            df_table(df_data[cols], 'df_data')
            df_data.to_excel('get_zijinliu_dfcfw.xlsx', index=False)
            # df.to_csv('c:\\get_zijinliu_dfcfw.csv',index = False)
    # 计时结束
    time_end = time.time()
    print("Python读取lmdb总耗时:", time_end - time1, '秒')

