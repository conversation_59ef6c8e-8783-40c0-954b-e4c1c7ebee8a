import sys

from PyQt6.QtGui import QAction
from PyQt6.QtWidgets import QApplication, QMainWindow, QTabWidget, QWidget, QVBoxLayout, QLabel, QPushButton, QLineEdit, \
    QDialog, QPushButton, QLabel, QMenu
from PyQt6.QtCore import Qt


class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle('Tab Widget with Add Group Icon')
        self.setGeometry(100, 100, 800, 600)

        # 创建 QTabWidget
        self.tab_widget = QTabWidget(self)
        self.setCentralWidget(self.tab_widget)

        # 初始标签页列表
        self.tabs = ["默认", "组选"]

        # 添加初始标签页
        for tab_name in self.tabs:
            self.add_tab(tab_name)

        # 设置标签页的上下文菜单策略
        self.tab_widget.setContextMenuPolicy(Qt.ContextMenuPolicy.CustomContextMenu)
        self.tab_widget.customContextMenuRequested.connect(self.show_context_menu)

    def add_tab(self, name):
        # 创建一个新的标签页
        new_tab = QWidget()
        layout = QVBoxLayout(new_tab)
        label = QLabel(f"这是 {name} 标签页")
        layout.addWidget(label)

        # 添加一个添加分组的按钮到标签页
        add_group_button = QPushButton("添加分组")
        add_group_button.clicked.connect(self.add_new_tab)
        layout.addWidget(add_group_button)

        # 将新标签页添加到 QTabWidget
        index = self.tab_widget.addTab(new_tab, name)
        self.tab_widget.setCurrentIndex(index)

        # 更新标签页的背景颜色
        self.update_tab_colors()

    def show_context_menu(self, position):
        menu = QMenu(self.tab_widget)

        add_action = QAction("添加标签页", self)
        add_action.triggered.connect(self.add_new_tab)

        menu.addAction(add_action)

        # 显示菜单
        menu.exec(self.tab_widget.viewport().mapToGlobal(position))

    def add_new_tab(self):
        # 弹出输入框让用户输入新的标签页名称
        dialog = QDialog(self)
        dialog.setWindowTitle("添加新的标签页")
        layout = QVBoxLayout(dialog)

        label = QLabel("请输入新的标签页名称:")
        layout.addWidget(label)

        line_edit = QLineEdit()
        layout.addWidget(line_edit)

        button_box = QPushButton("确定")
        button_box.clicked.connect(lambda: self.add_tab_and_close_dialog(line_edit.text(), dialog))
        layout.addWidget(button_box)

        dialog.exec()

    def add_tab_and_close_dialog(self, name, dialog):
        if name:
            self.tabs.append(name)
            self.add_tab(name)
        dialog.close()

    def update_tab_colors(self):
        # 设置每个标签页的背景颜色
        stylesheet = ""
        colors = ["#FFC0CB", "#ADD8E6", "#90EE90", "#FFFF00", "#FFA07A"]
        for i in range(self.tab_widget.count()):
            color = colors[i % len(colors)]
            stylesheet += f"QTabBar::tab:nth-child({i + 1}) {{ background-color: {color}; }}"

        # 应用样式表
        self.tab_widget.setStyleSheet(stylesheet)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())