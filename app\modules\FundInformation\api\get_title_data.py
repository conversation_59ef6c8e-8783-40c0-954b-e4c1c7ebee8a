import json

import requests
import time

from PyQt6.QtCore import QThread, pyqtSignal


class get_title(QThread):
    finished = pyqtSignal(list)
    def __init__(self):
        super().__init__()
        self.url_l, self.img_url_l, self.title_l = [], [], []
        self.header = {
            "Referer": "https://so.eastmoney.com/",
            "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=63307939966789; fund_registerAd_1=1; fullscreengg=1; fullscreengg2=1; emsiderzk=close; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=37; st_psi=20250323193223400-118000300903-9169591590; st_asi=delete",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********"
        }

    def run(self,):
        t=time.time()
        url = "https://recommend.eastmoney.com/soapi/api/articlesearch/search?cb=jQuery35103633732065296349_1742729395&count=60&Userid=&id=50d74afee65419b05e9120f0df53c69f&_=1742729395"
        data = requests.get(url,headers=self.header ,verify=False).text
        data = data.strip("jQuery35103633732065296349_1742708625183([{").replace("}])", "").split("},{")
        l = ["{" + i + "}" for i in data]
        for i in l:
            ll = json.loads(i)
            self.title_l.append(ll["Title"])
            self.img_url_l.append(ll["ImageUrl"])
            self.url_l.append(ll["Url"])
        s=time.time()
        print(s-t)
        # return [self.title_l,self.img_url_l,self.url_l]
        self.finished.emit([self.title_l,self.img_url_l,self.url_l])


# info_data=get_title()
