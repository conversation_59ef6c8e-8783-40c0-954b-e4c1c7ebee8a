from functools import partial

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QLineEdit, QMessageBox, QWidget, QHBoxLayout, QPushButton, QTableWidgetItem, QHeaderView, \
    QAbstractItemView

from app.modules.FundCompare.DataHandle import DataHandle
from app.modules.FundCompare.api.return_match_input import match_fund_code
from app.modules.FundSelection.DataHandle_sel import DataHandle_sel
from app.modules.FundSelection.api.get_select_data import get_select_data
from app.modules.FundSelection.src import style


class FundSelection:
    def __init__(self, ui):
        self.ui = ui
        self.init_setup()

    def init_setup(self):
        self.zdy_group_name="默认"
        self.show_jj_sel_status = False
        self.jj_sel_listWidget_status = False
        self.manager_group_status = False
        self.select_default_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSelection\src\select_\default\默认.txt"
        self.select_main_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSelection\src\select_"
        self.group_name="默认"
        self.top_group_index = 1
        self.sub_type_index=1
        self.ui.jj_sel_widget.hide()
        self.ui.jj_sel_search_listWidget.hide()
        self.ui.jj_sel_manage_widget.hide()
        self.ui.jj_sel_delete_p.hide()
        self.ui.jj_sel_tableWidget_1.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.ui.jj_sel_tableWidget_1.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.ui.jj_sel_tableWidget_2.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.ui.jj_sel_tableWidget_2.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)
        self.init_jj_sel_sub_widget()
        self.ui.top_img_4.mousePressEvent = self.show_jj_sel_widget
        self.ui.top_img_label_4.mousePressEvent = self.show_jj_sel_widget
        self.ui.jj_sel_return_lb.mousePressEvent = self.toggle_jj_sel_index
        self.jj_sel_top_group_style(self.top_group_index)
        self.ui.jj_sel_lineEdit.mousePressEvent=self.custom_mouse_press_jj_sel
        self.ui.jj_sel_search_listWidget.itemClicked.connect(self.jj_sel_search_listWidget_select)
        self.ui.jj_sel_clear.mousePressEvent = self.clear_jj_sel_lineEdit
        self.ui.jj_sel_add_btn.mousePressEvent=self.add_fund_code
        self.load_select_fund_group()

        # self.get_fund_group_data()
        self.update_fund_groups_len()
        self.load_fund_selection(self.all_fund_code,self.all_fund_date)
        self.horizontal_selection_init()
        self.ui.jj_sel_delete_p.mousePressEvent = self.delete_selected_rows
        self.ui.jj_sel_add_group_btn.mousePressEvent = self.add_group
        self.ui.jj_sel_del_group_btn.mousePressEvent = self.del_group
        self.ui.jj_sel_manager_btn.mousePressEvent = self.toggle_group_

    def toggle_group_(self,event):
        if  self.manager_group_status:
            self.ui.jj_sel_manage_widget.hide()
            self.manager_group_status = False
            self.ui.jj_sel_manager_btn.setText("分组管理")
            self.show_group_tip("")
            self.lineEdit_setText("jj_sel_add_grouplineEdit","")
            self.load_select_delete_fund_group()
        else:
            self.ui.jj_sel_manage_widget.show()
            self.ui.jj_sel_manager_btn.setText("关闭分组管理")
            self.load_select_delete_fund_group()
            self.manager_group_status = True

    #分组管理
    def add_group(self,event):
        group_name=self.ui.jj_sel_add_grouplineEdit.text()
        info=DataHandle_sel.check_group_name(group_name)
        # self.show_group_tip(info)
        if "创建失败" in info:
            self.show_group_tip(info)
        else:
            self.show_group_tip(info)
            self.load_select_delete_fund_group()
            self.load_select_fund_group()
            self.update_fund_groups_len()
            self.update_zdy_group_len()
            self.horizontal_selection_init()

    def load_select_delete_fund_group(self):
        try:
            self.select_delete_fund_group=DataHandle.get_fund_group_data()
            self.ui.jj_sel_del_group.clear()
            self.ui.jj_sel_del_group.addItems(self.select_delete_fund_group)
            self.ui.jj_sel_del_group.currentTextChanged.connect(self.select_delete_fund_group_name)
        except Exception as e:
            print(f"load_select_fund_group:{e}")

    def select_delete_fund_group_name(self,text):
        self.select_delete_fund_group_name=text

    def show_group_tip(self,text):
        self.ui.jj_sel_group_tip.setText(text)

    def del_group(self,event):
        info=DataHandle_sel.delete_group(self.select_delete_fund_group_name)
        if "删除失败" in info:
            self.show_group_tip(info)
        else:
            self.show_group_tip(info)
            self.load_select_fund_group()
            self.update_fund_groups_len()
            self.update_zdy_group_len()
            self.horizontal_selection_init()


    def delete_selected_rows(self,event):
        try:
            # 获取选中的行
            selected_rows = set()

            for index in self.ui.jj_sel_tableWidget_1.selectedIndexes():
                selected_rows.add(index.row())

            # 按降序排序以便从下往上删除
            for row in sorted(selected_rows, reverse=True):
                self.ui.jj_sel_tableWidget_1.removeRow(row)
                self.ui.jj_sel_tableWidget_2.removeRow(row)
            DataHandle_sel.delete_multiple_rows(self.zdy_group_name, sorted(selected_rows))
            self.update_fund_groups_len()
            self.update_zdy_group_len()
            self.ui.jj_sel_sub_widget_3.destroy()
            self.horizontal_selection_init()
        except Exception as e:
            print(e)

    def mul_del_show_hide(self,index):
        if index in [1,2,3]:
            self.ui.jj_sel_delete_p.hide()
        else:
            self.ui.jj_sel_delete_p.show()

    def get_fund_group_data(self):
        try:
            #全部分组下
            self.all_fund_code, self.all_fund_date = DataHandle_sel.get_all_fund_code()
            #持仓分组下
            self.buy_fund_code_temp, self.buy_fund_date_temp = DataHandle_sel.get_buy_fund_data()
            self.buy_fund_code, self.buy_fund_date = DataHandle_sel.check_code_type(self.buy_fund_code_temp, self.buy_fund_date_temp)
            print(self.buy_fund_code, self.buy_fund_date)
            #智能分组下
            self.smart_fund_code, self.smart_fund_date = DataHandle_sel.check_code_type(self.all_fund_code, self.all_fund_date)
            print(self.smart_fund_code, self.smart_fund_date)
            # self.sub_hh_type=
            #自定义分组下
        except Exception as e:
            print(e)

    def update_fund_groups_len(self):
        self.get_fund_group_data()
        self.ui.jj_sel_top_group_1.setText(f"全部（{len(self.all_fund_code)}）")
        self.ui.jj_sel_top_group_2.setText(f"当前持仓（{len(self.buy_fund_code_temp)}）")
        self.ui.jj_sel_top_group_3.setText(f"智能分组（{len(self.smart_fund_code)}）")
        self.ui.jj_sel_top_group_4.setText(f"自定义分组（{len(self.select_fund_group)}）")
        if self.top_group_index==2:
            self.update_sub_type_len(self.buy_fund_code)
            print(f"混合型（{len(self.buy_fund_code["混合型"])}）")
        elif self.top_group_index==3:
            self.update_sub_type_len(self.smart_fund_code)

    def update_sub_type_len(self,code_dict):
        self.ui.sel_sub_type_1.setText(f"混合型（{len(code_dict["混合型"])}）")
        self.ui.sel_sub_type_2.setText(f"股票型（{len(code_dict["股票型"])}）")
        self.ui.sel_sub_type_3.setText(f"指数型（{len(code_dict["指数型"])}）")
        self.ui.sel_sub_type_4.setText(f"债券型（{len(code_dict["债券型"])}）")
        self.ui.sel_sub_type_5.setText(f"QDII（{len(code_dict["QDII"])}）")
        self.ui.sel_sub_type_6.setText(f"FOF（{len(code_dict["FOF"])}）")
        self.ui.sel_sub_type_7.setText(f"商品（{len(code_dict["商品"])}）")

    def load_fund_selection(self,code_list,date_list):
        try:
            self.worker_thread = get_select_data(code_list=code_list,date_list=date_list)
            self.worker_thread.finished.connect(self.task_finished_jj_sel)
            self.worker_thread.start()
        except Exception as e:
            print(e)

    def task_finished_jj_sel(self,JZLB_list,ZFPH_list):
        try:
            match (self.top_group_index):
                case 1:
                    self.load_JZLB_table(data=JZLB_list)
                    self.load_ZFPH_table(data=ZFPH_list)
                case 2:
                    self.load_JZLB_table(data=JZLB_list)
                    self.load_ZFPH_table_2(data=ZFPH_list)
                case 3:
                    self.load_JZLB_table(data=JZLB_list)
                    self.load_ZFPH_table(data=ZFPH_list)
                case 4:
                    self.load_JZLB_table_2(data=JZLB_list)
                    self.ZFPH_data_temp = ZFPH_list
                    self.load_ZFPH_table(data=ZFPH_list)
        except Exception as e:
            print(e)

    def load_table_mb_1(self, table_name, headers, data, color_start, color_end, color_status, ):
        try:
            getattr(self.ui, f"{table_name}").setColumnCount(len(headers))
            getattr(self.ui, f"{table_name}").setHorizontalHeaderLabels(headers)
            getattr(self.ui, f"{table_name}").horizontalHeader().setSectionResizeMode(
                QHeaderView.ResizeMode.Stretch)
            # getattr(self.ui, f"{table_name}").horizontalHeader().setStyleSheet(style.QTableWidget_JBXX_style)
            getattr(self.ui, f"{table_name}").setRowCount(len(data))
            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    if col_idx in [0, 1, ]:
                        item.setForeground(QColor("#294EBC"))
                    if col_idx in range(color_start,  color_end+1):  # "#009900","#FF0018",
                        if str(cell_data) != "--":
                            if float(str(cell_data).strip("%")) > 0:
                                item.setForeground(QColor("#FF0018"))
                            elif float(str(cell_data).strip("%")) < 0:
                                item.setForeground(QColor("#009900"))
                            else:
                                item.setForeground(QColor("black"))
                    getattr(self.ui, f"{table_name}").setItem(row_idx, col_idx, item)
        except Exception as e:
            print(e)

    def load_JZLB_table(self,data):
        try:
            headers= ["基金代码", "基金名称", "基金类型", "最新日期", "单位净值", "累计净值", "日增长值", "日增长率","成立来"]
            self.load_table_mb_1(table_name="jj_sel_tableWidget_1", headers=headers, data=data, color_start=6,
                                  color_end=8, color_status=True)
        except Exception as e:
            print(e)

    def load_JZLB_table_2(self,data):
        try:
            self.JZLB_data_temp=data
            headers = ["基金代码", "基金名称", "基金类型", "最新日期", "单位净值", "累计净值", "日增长值", "日增长率","成立来","操作","其他"]
            self.load_table_mb_1(table_name="jj_sel_tableWidget_1", headers=headers, data=data, color_start=6,
                                  color_end=8, color_status=True)
            self.ui.jj_sel_tableWidget_1.setColumnCount(len(headers))
            self.ui.jj_sel_tableWidget_1.setHorizontalHeaderLabels(headers)
            self.ui.jj_sel_tableWidget_1.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            # self.ui.jj_sel_tableWidget_1.horizontalHeader().setStyleSheet(style.QHeaderView_style)
            self.ui.jj_sel_tableWidget_1.setRowCount(len(data))
            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    if col_idx in [0, 1, ]:
                        item.setForeground(QColor("#294EBC"))
                    if col_idx in range(6,9) and cell_data != "--":  # "#009900","#FF0018",
                        if float(str(cell_data).strip("%")) > 0:
                            item.setForeground(QColor("#FF0018"))
                        elif float(str(cell_data).strip("%")) < 0:
                            item.setForeground(QColor("#009900"))
                        else:
                            item.setForeground(QColor("black"))
                    self.ui.jj_sel_tableWidget_1.setItem(row_idx, col_idx, item)
                # 操作列按钮容器
                btn_container = QWidget()
                btn_layout = QHBoxLayout()
                btn_layout.setContentsMargins(0, 0, 0, 0)
                btn_layout.setSpacing(5)

                btn_container_2 = QWidget()
                btn_layout_2 = QHBoxLayout()
                btn_layout_2.setContentsMargins(0, 0, 0, 0)
                btn_layout_2.setSpacing(5)

                # add_btn = QPushButton("z")
                # add_btn.setProperty("row", row_idx)
                # # add_btn.clicked.connect(self.move_up)
                # btn_layout_2.addWidget(add_btn)

                buy_btn = QPushButton("购买")
                buy_btn.setProperty("row", row_idx)
                # buy_btn.clicked.connect(self.move_up)
                btn_layout_2.addWidget(buy_btn)

                # 置顶按钮（第一行禁用）
                up_btn = QPushButton("↑")
                up_btn.clicked.connect(partial(self.move_up, row=row_idx))
                up_btn.setEnabled(row_idx > 0)
                btn_layout.addWidget(up_btn)

                # 删除按钮
                del_btn = QPushButton("×")
                del_btn.setProperty("row", row_idx)
                del_btn.clicked.connect(partial(self.delete_row, row=row_idx))
                del_btn.setStyleSheet("color: red;")  # 红色删除按钮
                btn_layout.addWidget(del_btn)

                btn_container.setLayout(btn_layout)
                btn_container_2.setLayout(btn_layout_2)
                self.ui.jj_sel_tableWidget_1.setCellWidget(row_idx, len(row_data), btn_container, )
                self.ui.jj_sel_tableWidget_1.setCellWidget(row_idx, len(row_data) + 1, btn_container_2, )
        except Exception as e:
            print(e)

    def move_up(self,row):
        try:
            if row > 0:
                self.JZLB_data_temp[row],  self.JZLB_data_temp[0] = self.JZLB_data_temp[0], self.JZLB_data_temp[row]
                self.ZFPH_data_temp[row],  self.ZFPH_data_temp[0] = self.ZFPH_data_temp[0], self.ZFPH_data_temp[row]
                self.load_JZLB_table_2(self.JZLB_data_temp)
                self.load_ZFPH_table(self.ZFPH_data_temp)
                DataHandle_sel.chang_group_row_up(self.zdy_group_name,row)
        except Exception as e:
            print(e)

    def delete_row(self,row):
        try:
            self.JZLB_data_temp.pop(row)
            self.ZFPH_data_temp.pop(row)
            self.load_JZLB_table_2(self.JZLB_data_temp)
            self.load_ZFPH_table(self.ZFPH_data_temp)
            DataHandle_sel.delete_group_row(self.zdy_group_name,row)
            self.update_fund_groups_len()
            self.update_zdy_group_len()
            self.ui.jj_sel_sub_widget_3.destroy()
            self.horizontal_selection_init()
        except Exception as e:
            print(e)

    def load_ZFPH_table(self,data):
        try:
            headers = ["基金代码", "基金名称", "最新日期","添加自选日期","添加以来收益率" ,"近1周","近1月","近3月","近6月","今年来","近1年","近2年","近3年","近5年"]
            self.load_table_mb_1(table_name="jj_sel_tableWidget_2", headers=headers, data=data, color_start=4,
                                  color_end=12, color_status=True)
        except Exception as e:
            print(e)

    def load_ZFPH_table_2(self,data):
        headers = ["基金代码", "基金名称", "最新日期", "持仓日期", "持仓以来收益率", "近1周", "近1月", "近3月",
                   "近6月", "今年来", "近1年", "近2年", "近3年", "近5年"]
        self.load_table_mb_1(table_name="jj_sel_tableWidget_2", headers=headers, data=data, color_start=4,
                             color_end=12, color_status=True)


    def add_fund_code(self,event):
        try:
            if self.temp_code_name=="":
                QMessageBox.warning(self.ui.jj_sel_widget, "提示", "请输入基金代码")
                return
            self.repeat_code_name(temp_code_name=self.temp_code_name,group_name=self.group_name)
            if self.group_name=="默认":
                DataHandle_sel.add_fund_code_to_group(code=self.temp_code_name,select_="default",group_name=self.group_name)
            else:
                DataHandle_sel.add_fund_code_to_group(code=self.temp_code_name, select_="other", group_name=self.group_name)
            self.load_select_fund_group()
            self.load_select_delete_fund_group()
            self.temp_code_name=""
            self.lineEdit_setText(self.temp_code_name)
            self.update_fund_groups_len()

            self.load_group_index_change(self.top_group_index)
            self.ui.jj_sel_sub_widget_3.destroy()
            self.horizontal_selection_init()
            # if self.top_group_index==1:
            #     self.load_fund_selection(code_list=self.all_fund_code,date_list=self.all_fund_date)
            # else:
            #     print(f"index:{self.top_group_index}")
        except Exception as e:
            print(e)

    def repeat_code_name(self,temp_code_name,group_name):
        try:
            if self.group_name=="默认":
                if not DataHandle_sel.check_fund_code_repeat(code=temp_code_name, select_="default",
                                                             group_name=group_name):
                    QMessageBox.warning(self.ui.jj_sel_widget, "提示", "该基金已存在")
                    return
            else:
                if not DataHandle_sel.check_fund_code_repeat(code=temp_code_name, select_="other",
                                                             group_name=group_name):
                    QMessageBox.warning(self.ui.jj_sel_widget, "提示", "该基金已存在")
                    return
        except Exception as e:
            print(e)



    def jj_sel_search_listWidget_select(self,item):
        try:
            self.temp_code_name = item.text().replace(" ","")
            self.lineEdit_setText("jj_sel_lineEdit",self.temp_code_name)
            self.ui.jj_sel_search_listWidget.hide()
            self.temp_code_name = self.ui.jj_sel_lineEdit.text()
            print(self.temp_code_name)
            self.jj_sel_listWidget_status = False
        except Exception as e:
            print(e)

    def lineEdit_setText(self,lineEdit,text):
        getattr(self.ui,f"{lineEdit}").blockSignals(True)
        getattr(self.ui,f"{lineEdit}").setText(text)
        getattr(self.ui,f"{lineEdit}").blockSignals(False)

    def clear_jj_sel_lineEdit(self,event):
        try:
            self.temp_code_name=""
            self.lineEdit_setText(self.temp_code_name)
            self.ui.jj_sel_search_listWidget.hide()
            self.jj_sel_listWidget_status = False
        except Exception as e:
            print(e)



    def load_select_fund_group(self):
        try:
            self.select_fund_group=DataHandle.get_fund_group_data()
            self.ui.jj_sel_groups.clear()
            self.ui.jj_sel_groups.addItems(self.select_fund_group)
            self.ui.jj_sel_groups.currentTextChanged.connect(self.select_fund_group_name)
        except Exception as e:
            print(f"load_select_fund_group:{e}")

    def select_fund_group_name(self, text):
        self.group_name = text
        print(self.group_name)


    def custom_mouse_press_jj_sel(self,event):
        try:
            super(QLineEdit,self.ui.jj_sel_lineEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
            self.show_jj_sel_search_listWidget()
        except Exception as e:
            print(e)

    def show_jj_sel_search_listWidget(self):
        try:
            if not self.jj_sel_listWidget_status:
                self.jj_sel_listWidget_status = True
            self.ui.jj_sel_lineEdit.textChanged.connect(self.get_fund_code_name)
            self.ui.jj_sel_search_listWidget.show()
            self.lineEdit_setText("")
            self.get_fund_code_name("")
        except Exception as e:
            print(e)

    def get_fund_code_name(self,text):
        try:
            self.ui.jj_sel_search_listWidget.clear()
            data_list =match_fund_code.return_match_list(input_str=text)
            for i in data_list:
                self.ui.jj_sel_search_listWidget.addItem(i)
        except Exception as e:
            print(e)

    def jj_sel_top_group_style(self, index):
        for i in range(1, 5):
            getattr(self.ui, f'jj_sel_top_group_{i}').setStyleSheet(style.Qstyle_top_group_normal)
            getattr(self.ui, f'jj_sel_top_group_{i}').mousePressEvent = partial(self.change_top_group_index, index=i)
        for i in range(1, 5):
            if i == index:
                getattr(self.ui, f'jj_sel_top_group_{index}').setStyleSheet(style.Qstyle_top_group_selected)
            else:
                getattr(self.ui, f'jj_sel_top_group_{i}').setStyleSheet(style.Qstyle_top_group_normal)

    def jj_sel_sub_type_style(self,index):
        for i in range(1, 8):
            getattr(self.ui, f'sel_sub_type_{i}').setStyleSheet(style.Qstyle_sub_type_normal)
            getattr(self.ui, f'sel_sub_type_{i}').mousePressEvent = partial(self.change_sub_type_index, index=i)
        for i in range(1, 8):
            if i == index:
                getattr(self.ui, f'sel_sub_type_{index}').setStyleSheet(style.Qstyle_sub_type_selected)
            else:
                getattr(self.ui, f'sel_sub_type_{i}').setStyleSheet(style.Qstyle_sub_type_normal)

    def init_jj_sel_sub_widget(self):
        for i in range(1, 4):
            getattr(self.ui, f'jj_sel_sub_widget_{i}').hide()
        self.ui.jj_sel_sub_widget_1.show()
        self.jj_sel_top_group_style(self.top_group_index)

    def change_top_group_index(self, event, index):
        self.top_group_index = index
        for i in range(1, 4):
            getattr(self.ui, f'jj_sel_sub_widget_{i}').hide()
        self.load_group_index_change(index)
        self.jj_sel_top_group_style(self.top_group_index)

    def change_sub_type_index(self,event,index):
        self.sub_type_index = index
        for i in range(1, 8):
            getattr(self.ui, f'sel_sub_type_{i}').setStyleSheet(style.Qstyle_sub_type_normal)
        if self.top_group_index==2:
            self.load_sub_type_index_change(self.buy_fund_code,self.buy_fund_date,index)
        elif self.top_group_index==3:
            self.load_sub_type_index_change(self.smart_fund_code,self.smart_fund_date,index)
        self.jj_sel_sub_type_style(self.sub_type_index)

    def load_group_index_change(self,index):
        match (index):
            case 1:
                self.ui.jj_sel_sub_widget_1.show()
                self.load_fund_selection(self.all_fund_code, self.all_fund_date)
            case 2:
                self.ui.jj_sel_sub_widget_2.show()
                self.load_fund_selection(self.buy_fund_code["混合型"], self.buy_fund_date["混合型"])
                self.jj_sel_sub_type_style(self.sub_type_index)
                self.update_fund_groups_len()
            case 3:
                self.ui.jj_sel_sub_widget_2.show()
                # self.load_fund_selection(self.smart_fund_code, self.smart_fund_date)
                self.load_fund_selection(self.smart_fund_code["混合型"], self.smart_fund_date["混合型"])
                self.jj_sel_sub_type_style(self.sub_type_index)
                self.update_fund_groups_len()
            case 4:
                self.ui.jj_sel_sub_widget_3.show()
                self.zdy_group_name="默认"
                self.zdy_group_fund_code,self.zdy_group_fund_date=DataHandle_sel.get_single_file_fund_code(group_name=self.zdy_group_name)
                self.load_fund_selection(self.zdy_group_fund_code, self.zdy_group_fund_date)
        self.mul_del_show_hide(index=index)

    def load_sub_type_index_change(self,code_dict,date_dict,index):
        match (index):
            case 1:
                self.load_fund_selection(code_dict["混合型"], date_dict["混合型"])
            case 2:
                self.load_fund_selection(code_dict["股票型"], date_dict["股票型"])
            case 3:
                self.load_fund_selection(code_dict["指数型"], date_dict["指数型"])
            case 4:
                self.load_fund_selection(code_dict["债券型"], date_dict["债券型"])
            case 5:
                self.load_fund_selection(code_dict["QDII"],date_dict["QDII"])
            case 6:
                self.load_fund_selection(code_dict["FOF"], date_dict["FOF"])
            case 7:
                self.load_fund_selection(code_dict["商品"], date_dict["商品"])
    def show_jj_sel_widget(self, event):
        self.ui.jj_sel_widget.show()
        self.show_jj_sel_status = True

    def toggle_jj_sel_index(self, event):
        if self.show_jj_sel_status:
            self.ui.sel_jj_widget.hide()
            self.show_jj_sel_status = False
        else:
            self.ui.sel_jj_widget.show()
            self.show_jj_sel_status = True

    def update_zdy_group_len(self):
        self.zdy_group_item_list=DataHandle.get_fund_group_data()
        items = []
        for i in self.zdy_group_item_list:
            code, date = DataHandle_sel.get_single_file_fund_code(i)
            items.append(f"{i}（{len(code)}）")
        return items
    def horizontal_selection_init(self,):
        self.ui.jj_sel_right.mousePressEvent = self.scroll_right
        self.ui.jj_sel_left.mousePressEvent = self.scroll_left
        self.scroll_content = QWidget()
        self.scroll_layout = QHBoxLayout(self.scroll_content)
        self.scroll_layout.setSpacing(6)
        self.scroll_layout.setContentsMargins(10, 4, 5, 4)
        self.button_map = {}
        items = self.update_zdy_group_len()
        self.create_buttons(items)
        self.ui.jj_sel_scrollArea.setWidget(self.scroll_content)
        self.update_arrows()

    def create_buttons(self, items):
        """根据提供的列表创建水平按钮"""
        for item in items:
            button = QPushButton(item)
            button.setFixedHeight(32)  # 设置固定高度
            button.setStyleSheet(style.QButton_style_normal)
            button.clicked.connect(lambda checked, text=item: self.on_button_click(text))
            self.scroll_layout.addWidget(button)
            self.button_map[item] = button
            #默认文件的基金数量
            data,_=DataHandle_sel.get_single_file_fund_code(self.zdy_group_name)
            self.highlight_button(f"{self.zdy_group_name}（{len(data)}）")

    def on_button_click(self, item_text):
        self.highlight_button(item_text)
        item_text=item_text.split("（")[0]
        self.zdy_group_name=item_text
        self.zdy_group_fund_code,self.zdy_group_fund_date=DataHandle_sel.get_single_file_fund_code(group_name=item_text)
        self.load_fund_selection(self.zdy_group_fund_code, self.zdy_group_fund_date)

    def highlight_button(self, item_text):
        """高亮显示被点击的按钮"""
        for text, button in self.button_map.items():
            if text == item_text:
                button.setStyleSheet(style.QButton_style_selected)
            else:
                button.setStyleSheet(style.QButton_style_normal)

    def scroll_left(self,event):
        """向左滚动"""
        current_scroll = self.ui.jj_sel_scrollArea.horizontalScrollBar().value()
        new_scroll = max(0, current_scroll - 100)  # 每次滚动100像素
        self.ui.jj_sel_scrollArea.horizontalScrollBar().setValue(new_scroll)
        self.update_arrows()

    def scroll_right(self,event):
        """向右滚动"""
        current_scroll = self.ui.jj_sel_scrollArea.horizontalScrollBar().value()
        max_scroll = self.ui.jj_sel_scrollArea.horizontalScrollBar().maximum()
        new_scroll = min(max_scroll, current_scroll + 100)  # 每次滚动100像素
        self.ui.jj_sel_scrollArea.horizontalScrollBar().setValue(new_scroll)
        self.update_arrows()

    def update_arrows(self):
        """更新左右箭头的可用状态"""
        current_scroll = self.ui.jj_sel_scrollArea.horizontalScrollBar().value()
        max_scroll = self.ui.jj_sel_scrollArea.horizontalScrollBar().maximum()
        self.ui.jj_sel_left.setEnabled(current_scroll > 0)
        self.ui.jj_sel_right.setEnabled(max_scroll > 0 and current_scroll < max_scroll)


