import concurrent
import json
from concurrent.futures import ThreadPoolExecutor

import requests
from PyQt6.QtCore import QThread, pyqtSignal


class get_data(QThread):
    finished = pyqtSignal(list, list,int)

    def __init__(self,page,status):
        super().__init__()
        self.update_status=status
        self.headers={
              "Host": "fund.eastmoney.com",
              "Connection": "keep-alive",
              "sec-ch-ua-platform": "\"Windows\"",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
              "sec-ch-ua-mobile": "?0",
              "Accept": "*/*",
              "Sec-Fetch-Site": "same-origin",
              "Sec-Fetch-Mode": "no-cors",
              "Sec-Fetch-Dest": "script",
              "Referer": "https://fund.eastmoney.com/data/xinfound.html",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=35324403182615; st_asi=delete; ap_0_13a7d068=1; ASP.NET_SessionId=nvr3t0bytqzhdkz1nhhlhkwc; _adsame_fullscreen_20308=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=10; st_psi=20250511183534956-112200312946-3328705501"
            }
        self.zs_url="https://fund.eastmoney.com/data/FundNewIssue.aspx?t=zs&sort=jzrgq,desc&page=1,1000&isbuy=1&v=0.3229192471325548"#在售基金
        self.xcl_url=f"https://fund.eastmoney.com/data/FundNewIssue.aspx?t=xcln&sort=jzrgq,desc&y=&page={page},50&isbuy=1&v=0.6705815753202203"#新成立基金
        self.zs_result_list=[]
        self.xcl_result_list=[]
        self.all_pages=0

    def re_data(self,data):
        s = data.replace('datas:', '"datas":').replace('record:', '"record":').replace('pages:', '"pages":').replace(
            'curpage:', '"curpage":')
        result = json.loads(s)
        return result
    def request_data(self,url):
        try:
            response = requests.get(url, headers=self.headers, verify=False)
            data = response.text.strip("var newfunddata=")
            res = self.re_data(data)
            return res
        except Exception as e:
            print(e)

    def get_zs_data(self):
        try:
            datas=self.request_data(self.zs_url)["datas"]
            for i in datas:
                t = [
                    i[0],  # 基金代码
                    i[1],  # 基金名称
                    i[2],  # 基金公司
                    i[4],  # 基金类型
                    i[5],  # 集中认购期
                    i[6],  # 最高认购费率
                    i[7],  # 基金经理
                    i[8],  # 基金状态,认购期
                ]
                self.zs_result_list.append(t)
        except Exception as e:
            print(e)

    def get_xcl_data(self):
        try:
            datas = self.request_data(self.xcl_url)["datas"]
            all_pages=self.request_data(self.xcl_url)["record"]
            self.all_pages=all_pages//50+1
            for i in datas:
                t = [
                    i[0],  # 基金代码
                    i[1],  # 基金名称
                    i[2],  # 基金公司
                    i[4],  # 基金类型
                    i[10],  # 集中认购期
                    i[5],  # 募集份额(亿份)
                    i[6],  # 成立日期
                    i[8],  # 基金经理
                    i[9],  # 基金状态,认购期
                    i[-1],  # 优惠费率
                ]
                self.xcl_result_list.append(t)
        except Exception as e:
            print(e)

    def run(self):
        try:
            if self.update_status=="2":
                with ThreadPoolExecutor(max_workers=2) as executor:
                    future_zs=executor.submit(self.get_zs_data)
                    future_xcl=executor.submit(self.get_xcl_data)
                    concurrent.futures.wait([future_zs, future_xcl])
                self.finished.emit(self.zs_result_list,self.xcl_result_list,self.all_pages)
            else:
                self.get_xcl_data()
                self.finished.emit(self.zs_result_list,self.xcl_result_list,self.all_pages)
        except Exception as e:
            print(e)


