from datetime import datetime

from PyQt6.QtWidgets import QMessageBox

from main_w import Ui_MainWindow


class common_:
    def __init__(self, ui:Ui_MainWindow):
        self.ui = ui
        self.init_setup()

    def init_setup(self):
        pass

    def check_calculator_num(self,input_str:str):
        try:
            if len(input_str)==0:
                QMessageBox.information(self.ui.calculator_widget, "数据异常", "数字不能为空")
                return False
            if float(input_str)>0:
                return True
            else:
                QMessageBox.information(self.ui.calculator_widget, "数据异常", "数字必须是正数")
                return False
        except Exception as e:
            QMessageBox.information(self.ui.calculator_widget, "数据异常", "数字不合法")
            return False

    def sub_type_widget_(self, type_list, item_name,sub_listWidget_list):
        for i in range(len(type_list)):
            if i == sub_listWidget_list.index(item_name):
                getattr(self.ui, f"calculator_{type_list[i]}_widget").show()
            else:
                getattr(self.ui, f"calculator_{type_list[i]}_widget").hide()

    def clear_result(self,p_l):
        for i in range(1, 4):
            getattr(self.ui, f"ck_result_{i}").setText("")
            getattr(self.ui, f"ck_result_p_{i}").setText(p_l[i-1])

    def update_result_lb(self,result_lb_list:list):
        for i in range(1, len(result_lb_list) + 1):
            getattr(self.ui, f"ck_result_lb_{i}").setText(result_lb_list[i - 1])

    def check_calculator_cal_(self,start_date:str,end_date:str,mode):
        print(mode,start_date,end_date)
        if mode=="1":
            try:
                if start_date == "" or end_date == "":
                    QMessageBox.information(self.ui.calculator_widget, "日期异常", "日期不能为空")
                    return False
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                end_date = datetime.strptime(end_date, "%Y-%m-%d")
                if end_date > start_date:
                    return int(str((end_date-start_date).days))
                else:
                    QMessageBox.information(self.ui.calculator_widget, "日期异常", "结束日期必须大于开始日期")
                    return False
            except ValueError:
                QMessageBox.information(self.ui.calculator_widget, "日期异常", "日期格式不合法")
                return False
        else:
            try:
                if start_date == "":
                    QMessageBox.information(self.ui.calculator_widget, "单日期异常", "日期不能为空")
                    return False
                start_date = datetime.strptime(start_date, "%Y-%m-%d")
                return str(start_date)
            except ValueError:
                QMessageBox.information(self.ui.calculator_widget, "单日期异常", "日期格式不合法")
                return False
    def hide_cal(self):
        self.ui.ck_hq_calendarWidget_1.hide()
        self.ui.ck_hq_calendarWidget_2.hide()
        self.ui.ck_zczq_calendarWidget_1.hide()
        self.ui.ck_lczq_calendarWidget_1.hide()
        self.ui.ck_zclq_calendarWidget_1.hide()
        self.ui.ck_cbqx_calendarWidget_1.hide()
        self.ui.ck_dhlb_calendarWidget_1.hide()
        self.ui.ck_dhlb_calendarWidget_2.hide()
        self.ui.ck_tzck_calendarWidget_1.hide()
        self.ui.ck_tzck_calendarWidget_2.hide()
        self.ui.ck_jycx_calendarWidget_1.hide()
        self.ui.jj_jjcyq_calendarWidget_1.hide()
        self.ui.jj_jjcyq_calendarWidget_2.hide()


