from app.modules.FundCalculator.api.exchange import get_exchange
from app.modules.FundCalculator.api.get_exchange_html import get_exchange_html
from app.modules.FundCalculator.common_ui import common_
from app.modules.FundCalculator.src import style
from main_w import Ui_MainWindow
import asyncio
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit

class Calculator_wh:
    def __init__(self, ui:Ui_MainWindow,):
        self.ui = ui
        self.common_ui = common_(self.ui)
        self.init_setup()

    def init_setup(self):
        self.ui.calculator_type_lb.setText("外汇类计算器")
        self.sub_listWidget_list = ["外汇兑换计算器"]

    def load_wh_web(self, source: str, target: str, length, unit):
        try:
            self.worker_thread_wh = get_exchange_html(source=source, target=target, length=length, unit=unit)
            self.worker_thread_wh.finished.connect(self.task_finished_wh)
            self.worker_thread_wh.start()
            self.ui.wh_tip_text.setText("正在获取最新汇率")
            self.ui.wh_webEngineView.hide()
        except Exception as e:
            print(e)


    def task_finished_wh(self, web_data, new_value):
        # self.wh_new_value=0.1
        self.ui.wh_webEngineView.setHtml(web_data)
        self.wh_new_value = new_value
        self.ui.wh_tip_text.setText(
            f"1 {get_exchange.extraction_code(self.wh_code_1).upper()} = {self.wh_new_value} {get_exchange.extraction_code(self.wh_code_2).upper()}")
        self.ui.wh_webEngineView.show()


    def custom_mouse_press_whdh(self, event):
        super(QLineEdit, self.ui.wh_whdh_input_2).mousePressEvent(event)  # 调用父类方法，确保正常行为
        self.ui.whdh_widget.show()
        self.show_whdh_listWidget("", "whdh_listWidget", "wh_whdh_input_2")

    def custom_mouse_press_whdh_2(self, event):
        super(QLineEdit, self.ui.wh_whdh_input_3).mousePressEvent(event)  # 调用父类方法，确保正常行为
        self.ui.whdh_widget_3.show()
        self.show_whdh_listWidget("", "whdh_listWidget_3", "wh_whdh_input_3")


    def show_wh_widget(self):
        self.wh_type =[ "whdh",]
        self.ui.wh_total_widget.show()
        # 绑定左侧列表
        # self.calculator_type_listWidget.itemClicked.connect(self.bind_wh_sub_)
        self.show_wh_whdh_widget()
        self.common_ui.clear_result(["元", "", ""])
        self.common_ui.sub_type_widget_(self.wh_type, self.sub_listWidget_list[0],self.sub_listWidget_list)


    def show_whdh_listWidget(self, query, listWidget_name, input_name):
        try:
            getattr(self.ui, f"{listWidget_name}").clear()
            if query == "":
                match_list = get_exchange.match_code_letter(query)
                for i in match_list:
                    getattr(self.ui, f"{listWidget_name}").addItem(i)
            elif query in get_exchange.get_currency_list():
                pass
            else:
                if get_exchange.input_type(query) == "code":
                    match_list = get_exchange.match_code_letter(query)
                    for i in match_list:
                        getattr(self.ui, f"{listWidget_name}").addItem(i)
                elif get_exchange.input_type(query) == "text":
                    match_list = get_exchange.match_code_text(query)
                    for i in match_list:
                        getattr(self.ui, f"{listWidget_name}").addItem(i)
                else:
                    print(query)
                    QMessageBox.information(self.ui.calculator_widget, "输入有误", "输入只能是字母或汉字")
                    getattr(self.ui, f"{input_name}").setText("")
        except Exception as e:
            print(e)


    def hide_whdh_widget(self, widget_name):
        getattr(self.ui, f"{widget_name}").hide()


    def return_result_wh_whdh(self, event):
        money = self.ui.wh_whdh_input_1.text()
        if self.common_ui.check_calculator_num(money):
            self.ui.ck_result_1.setText("{:.2f}".format(float(money) * self.wh_new_value))



    def show_wh_whdh_widget(self):
        try:
            # self.wh_new_value=0
            self.ui.wh_webEngineView.hide()
            self.ui.wh_time_widget.show()
            self.hide_whdh_widget("whdh_widget")
            self.hide_whdh_widget("whdh_widget_3")
            self.wh_type_1 = "美元"
            self.wh_type_2 = "人民币"
            self.wh_code_1 = "usd"
            self.wh_code_2 = "cny"
            self.length_ = 2
            self.unit_ = "day"
            self.ui.wh_whdh_input_2.setText("USD - 美元")
            self.ui.wh_whdh_input_3.setText("CNY - 人民币")
            self.common_ui.update_result_lb(["兑换回的货币数量：", "", ""])
            self.ui.wh_img_1.load(QUrl("http://tool.huiruisoft.com/country/images/48x48/us.png"))
            self.ui.wh_img_2.load(QUrl("http://tool.huiruisoft.com/country/images/48x48/cn.png"))
            self.common_ui.clear_result(["元", "", ""])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_wh_whdh
            self.ui.wh_whdh_input_2.mousePressEvent = self.custom_mouse_press_whdh  # 点
            self.ui.wh_whdh_input_2.textChanged.connect(
                partial(self.show_whdh_listWidget, listWidget_name="whdh_listWidget",
                        input_name="wh_whdh_input_2"))  # 点
            self.ui.wh_whdh_input_3.mousePressEvent = self.custom_mouse_press_whdh_2  # 点
            self.ui.wh_whdh_input_3.textChanged.connect(
                partial(self.show_whdh_listWidget, listWidget_name="whdh_listWidget_3",
                        input_name="wh_whdh_input_3"))  # 点
            self.ui.whdh_listWidget.itemClicked.connect(
                partial(self.insert_input, widget_name="whdh_widget", input_name="wh_whdh_input_2", img_name="wh_img_1",
                        type_name="wh_type_1", wh_code="wh_code_1"))
            self.ui.whdh_listWidget_3.itemClicked.connect(
                partial(self.insert_input, widget_name="whdh_widget_3", input_name="wh_whdh_input_3",
                        img_name="wh_img_2", type_name="wh_type_2", wh_code="wh_code_2"))
            self.load_wh_web(source=self.wh_code_1,
                             target=self.wh_code_2, length=self.length_, unit=self.unit_)

            def clear_wh_whdh(event):
                # self.show_wh_whdh_widget()
                self.ui.wh_whdh_input_1.setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_wh_whdh
            self.length_unit_lsit = ["2-day", "7-day", "30-day", "6-month", "1-year", "5-year"]
            for i in range(1, 7):
                getattr(self.ui, f"wh_time_step_lb_{i}").setStyleSheet(style.default_wh_time_step_style)
            self.ui.wh_time_step_lb_1.setStyleSheet(style.selected_wh_time_step_style)
            for i in range(1, 7):
                getattr(self.ui, f"wh_time_step_lb_{i}").mousePressEvent = partial(self.select_time_step, index=i - 1)
        except Exception as e:
            print(e)


    def select_time_step(self, event, index):
        for i in range(1, 7):
            getattr(self.ui, f"wh_time_step_lb_{i}").setStyleSheet(style.default_wh_time_step_style)
        self.length_ = int(self.length_unit_lsit[index].split("-")[0])
        self.unit_ = self.length_unit_lsit[index].split("-")[1]
        getattr(self.ui, f"wh_time_step_lb_{index + 1}").setStyleSheet(style.selected_wh_time_step_style)
        self.load_wh_web(source=get_exchange.extraction_code(self.wh_code_1),
                         target=get_exchange.extraction_code(self.wh_code_2),
                         length=self.length_, unit=self.unit_)

    def insert_input(self, item: QListWidgetItem, widget_name: QWidget, input_name: str, img_name: str, type_name,wh_code: str):
        item_name = item.text()
        if "1" in img_name:
            self.wh_code_1=item_name
        elif "2" in img_name:
            self.wh_code_2 = item_name
        getattr(self.ui, f"{input_name}").setText(item_name)
        setattr(self.ui, f"{wh_code}", item_name)
        self.hide_whdh_widget(widget_name)
        url = "http://tool.huiruisoft.com/country/images/48x48/" + get_exchange.extraction_code(item_name)[:2] + ".png"
        getattr(self.ui, f"{img_name}").load(QUrl(url))
        setattr(self, f"{type_name}", get_exchange.extraction_name(item_name))
        self.ui.wh_tip_lb.setText(f"当前 {self.wh_type_1} 兑换 {self.wh_type_2} 汇率:")
        self.load_wh_web(source=get_exchange.extraction_code(self.wh_code_1),
                         target=get_exchange.extraction_code(self.wh_code_2),
                         length=self.length_, unit=self.unit_)