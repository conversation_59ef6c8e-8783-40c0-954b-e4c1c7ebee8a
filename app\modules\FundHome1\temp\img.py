from PIL import Image, ImageDraw

# 图片尺寸
size = 20  # 宽高均为 200px
radius = size // 2  # 圆形半径

# 创建一个透明背景的图片（RGBA模式：红、绿、蓝、透明度）
image = Image.new("RGBA", (size, size), (0, 0, 0, 0))
draw = ImageDraw.Draw(image)

# 画一个红色圆形
red = (255, 0, 0, 255)  # 红色，不透明
circle_center = (radius, radius)  # 圆心
circle_bbox = (
    circle_center[0] - radius,  # left
    circle_center[1] - radius,  # top
    circle_center[0] + radius,  # right
    circle_center[1] + radius   # bottom
)
draw.ellipse(circle_bbox, fill=red)

# 保存图片
image.save("red_circle.png")
print("红色圆形图片已生成：red_circle.png")