QLabel_ft_selected ="""
border-radius:7px;
background-color:#C1FFC1;
color:black;
"""
QLabel_ft_normal="""
border-radius:7px;
background-color:none;
border:2px solid gray;
color:gray
"""
QLabel_ft_selected_hover="""
                        border-radius:7px;
                        background-color:#C1FFC1;
                        color:black;
                        """
QLabel_cp_selected ="""
border-radius:7px;
background-color:	#B0C4DE;
color:black;
"""
QButton_cp_normal="""
                    QPushButton {
                        padding: 6px 12px;
                        margin: 2px;
                        min-width: 80px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                    }
                    QPushButton:checked {
                        background-color: #1890ff;
                        color: white;
                        border-color: #1890ff;
                    }
                  """
QButton_cp_selected="""
                    QPushButton {
                        padding: 6px 12px;
                        margin: 2px;
                        min-width: 80px;
                        border-radius: 4px;
                        background-color: #1890ff;
                        color: white;
                        border-color: #1890ff;
                    }
                    QPushButton:checked {
                        border: 1px solid #ddd;
                        background-color: none;
                        color:black;
                    }
                    """
QHeaderView_style="""QHeaderView::section {
                    background-color:#E6E6FA;  /* 背景色 */
                    color: black;              /* 文字颜色 */
                    padding: 5px;              /* 内边距 */
                     /* 边框 */
                }"""
QPushButton_page_no="""
color:gray;
border:0;
"""
QPushButton_page_yes="""
QPushButton{
    color:black;
	border:0;
}

QPushButton::hover {
    background-color:none;
    color:#FF6600;
}
"""