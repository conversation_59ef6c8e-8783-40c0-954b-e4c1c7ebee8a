import os
from datetime import datetime

from app.common.api.code_name import FundCodeName


class DataHandle_sel():
    def __init__(self):
        self.select_main_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSelection\src\select_"

    def get_nowdate(self,):
        now = datetime.now()
        return now.strftime("%Y-%m-%d")

    def add_fund_code_to_group(self,code,select_,group_name,):
        try:
            if self.check_fund_code_repeat(code,select_,group_name,):
                date=self.get_nowdate()
                with open(f"{self.select_main_path}\\{select_}\\{group_name}.txt", "a", encoding="utf-8") as f:
                    f.write(f"{code}|{date}\n")
        except Exception as e:
            print(e)

    def check_fund_code_repeat(self,code,select_,group_name,):
        with open(f"{self.select_main_path}\\{select_}\\{group_name}.txt", "r", encoding="utf-8") as f:
            code_name_list = f.readlines()
        code_name_list = [i.split("|")[0].strip() for i in code_name_list]
        print()
        if code not in code_name_list:
            return True
        return False

    def get_file_fund_code(self,select_,group_name,):
        with open(f"{self.select_main_path}\\{select_}\\{group_name}.txt", "r", encoding="utf-8") as f:
            code_item = f.readlines()
        code_name_list = [i.strip().split("-")[0] for i in code_item]
        code_date_list = [i.strip().split("|")[1] for i in code_item]
        print(code_name_list)
        return code_name_list,  code_date_list

    #自定义分组下获取指定文件的基金列表和基金日期。
    def get_single_file_fund_code(self,group_name):
        if group_name=="默认":
            with open(f"{self.select_main_path}\\default\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        else:
            with open(f"{self.select_main_path}\\other\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        code_name_list = [i.strip().split("-")[0] for i in code_item]
        code_date_list = [i.strip().split("|")[1] for i in code_item]
        return code_name_list, code_date_list

    def get_all_fund_code(self,):
        default_code_list,default_date_list=self.get_file_fund_code("default","默认")
        folder_path_other = f"{self.select_main_path}\\other"
        other_code_list=[]
        other_date_list=[]
        files_other = [i.strip(".txt") for i in os.listdir(folder_path_other)]
        for i in files_other:
            other_code_list.extend(self.get_file_fund_code("other",i)[0])
            other_date_list.extend(self.get_file_fund_code("other",i)[1])
        code_data,date_data=self.no_repeat_code(default_code_list+other_code_list,default_date_list+other_date_list)
        return code_data,date_data

    def no_repeat_code(self,code_list,date_list):
        code_set=set(code_list)
        date_set=set(date_list)
        temp_index=[]
        date_list_r=[]
        for i in code_set:
            temp_index.append(code_list.index(i))
        for u in temp_index:
            date_list_r.append(date_list[u])
        # print(list(code_set))
        # print(date_list_r)
        return list(code_set),date_list_r

    def get_buy_fund_data(self):
        buy_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSelection\src\select_\buy.txt"
        with open(buy_path, "r", encoding="utf-8") as f:
            code_item = f.readlines()
        code_name_list = [i.strip().split("-")[0] for i in code_item]
        code_date_list = [i.strip().split("|")[1] for i in code_item]
        return code_name_list, code_date_list

    def check_code_type(self, code_list,date_list):
        code_dict = FundCodeName.generate_fund_code_type_dict()
        fund_types = {"混合型": [],"股票型": [],"指数型": [],"债券型": [],"QDII": [],"FOF": [],"商品": []}
        fund_date = {"混合型": [],"股票型": [],"指数型": [],"债券型": [],"QDII": [],"FOF": [],"商品": []}
        for i in range(len(code_list)):
            fund_types[code_dict.get(code_list[i])].append(code_list[i])
            fund_date[code_dict.get(code_list[i])].append(date_list[i])
        # print(fund_types)
        # print(fund_date)
        return fund_types,fund_date

    def chang_group_row_up(self,group_name,row):
        if group_name=="默认":
            with open(f"{self.select_main_path}\\default\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        else:
            with open(f"{self.select_main_path}\\other\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        code_item[row],  code_item[0] = code_item[0], code_item[row]
        if group_name=="默认":
            with open(f"{self.select_main_path}\\default\\{group_name}.txt", "w", encoding="utf-8") as f:
                f.writelines(code_item)
        else:
            with open(f"{self.select_main_path}\\other\\{group_name}.txt", "w", encoding="utf-8") as f:
                f.writelines(code_item)

    def delete_group_row(self,group_name,row):
        if group_name=="默认":
            with open(f"{self.select_main_path}\\default\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        else:
            with open(f"{self.select_main_path}\\other\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        code_item.pop(row)
        if group_name=="默认":
            with open(f"{self.select_main_path}\\default\\{group_name}.txt", "w", encoding="utf-8") as f:
                f.writelines(code_item)
        else:
            with open(f"{self.select_main_path}\\other\\{group_name}.txt", "w", encoding="utf-8") as f:
                f.writelines(code_item)

    def delete_multiple_rows(self,group_name,rows):
        if group_name=="默认":
            with open(f"{self.select_main_path}\\default\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        else:
            with open(f"{self.select_main_path}\\other\\{group_name}.txt", "r", encoding="utf-8") as f:
                code_item = f.readlines()
        print(rows)
        # 将索引排序并反转，以便从后往前删除
        for i in sorted(rows, reverse=True):
            del code_item[i]
        if group_name=="默认":
            with open(f"{self.select_main_path}\\default\\{group_name}.txt", "w", encoding="utf-8") as f:
                f.writelines(code_item)
        else:
            with open(f"{self.select_main_path}\\other\\{group_name}.txt", "w", encoding="utf-8") as f:
                f.writelines(code_item)

    #获取自定义的分组列表
    def get_fund_group_data(self,):
        folder_path_other = f"{self.select_main_path}\\other"
        files_other= os.listdir(folder_path_other)
        files=["默认"]
        files_other = [f.replace(".txt", "") for f in files_other if os.path.isfile(os.path.join(folder_path_other, f))]
        files.extend(files_other)  # 输出文件名列表
        return files

    def create_group_file(self,group_name):
        with open(f"{self.select_main_path}\\other\\{group_name}.txt", "w", encoding="utf-8") as f:
            f.write("")

    def check_group_name(self,group_name):
        if group_name=="":
            return "创建失败：分组名称不能为空！"
        if group_name in self.get_fund_group_data():
            return "创建失败：分组名称已存在！"
        if len(group_name)>10:
            return "创建失败：分组名称长度不能超过10个字符！"
        self.create_group_file(group_name)
        return f"{group_name}创建成功！"

    def delete_group(self,group_name):
        if group_name=="默认":
            return "删除失败：默认分组不能删除！"
        os.remove(f"{self.select_main_path}\\other\\{group_name}.txt")
        return f"{group_name}删除成功！"









DataHandle_sel=DataHandle_sel()
# DataHandle_sel.check_code_type(["560010","000001","005218","166301","320007","000004","015150","002230","000331","161725","011103","012768"])
