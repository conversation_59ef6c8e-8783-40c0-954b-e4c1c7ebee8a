import sys
import json
from datetime import datetime, timedelta
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtCore import QUrl, QFile, QIODevice


class EnhancedChartWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        # 配置参数
        self.chart_config = {
            "y_min": -20,  # Y轴最小值
            "y_max": 20,  # Y轴最大值
            "show_x_labels": False,  # 是否显示X轴标签
            "x_labels": self.generate_date_labels(15),  # 自定义日期标签
            "legends": ["销售数据", "成本数据", "利润数据", "库存量", "客流量",
                        "转化率", "毛利率", "退货率", "满意度", "市场份额"]  # 图例名称
        }

        # 自定义10条线段数据（每条线段10个点）
        self.line_data = [
            {"color": "#FF0000",
             "points": [0.0, 0.42, -0.95, -0.53, -0.84, -0.56, -1.07, -0.24, 0.02, -4.37, -2.84, -3.63, -3.03, -2.8,
                        -2.24, -0.83, 0.15, 0.22, 1.1, -0.1, 0.54, 0.06, 1.17]},  # 红色线段
            {"color": "#00FF00",
             "points": [0.0, -2.89, -2.59, -1.05, -2.56, -1.43, -1.54, -1.71, -3.33, -13.5, -15.0, -11.54, -11.23, -5.4,
                        -3.36, -3.28, -2.74, -0.07, -1.56, -1.17, -1.71, -1.95, -3.4]},  # 绿色
            {"color": "#0000FF",
             "points": [0.0, 0.39, -3.64, -0.96, 1.59, 0.86, -0.67, 1.59, 0.38, -1.06, -15.95, -12.57, -10.18, -8.21,
                        -7.04, -3.97, -2.97, -5.04, -3.65, -3.71, -3.72, -2.75, -1.69]},  # 蓝色
            {"color": "#FF00FF",
             "points": [0.0, -0.33, -0.48, -0.26, -0.93, -1.3, -1.06, -1.24, -2.37, -9.23, -8.69, -7.53, -5.96, -5.48,
                        -5.25, -5.63, -5.78, -5.79, -5.9, -4.82, -4.83, -4.53, -5.12]},  # 紫色
            {"color": "#FFFF00",
             "points": [0.0, -0.12, 0.12, -0.19, -0.81, -1.24, -0.75, -0.62, -1.24, -6.59, -5.9, -4.04, -2.55, -2.18,
                        -1.8, -2.49, -3.48, -3.42, -3.29, -1.86, -2.24, -1.37, -2.3]},  # 黄色
            {"color": "#00FFFF",
             "points": [0.0, 0.42, 0.93, 0.68, 0.26, -0.91, -0.2, -0.03, -0.66, -6.65, -5.63, -4.08, -2.68, -3.18,
                        -2.86, -3.73, -4.72, -4.36, -4.33, -3.78, -3.42, -2.93, -3.78]},  # 青色
            {"color": "#FFA500",
             "points": [0.0, 0.09, 0.54, 0.63, 0.36, -0.09, 0.09, 0.36, -0.09, -4.57, -3.95, -2.87, -2.06, -1.79, -1.43,
                        -1.88, -2.42, -2.15, -2.24, -1.43, -1.26, -0.63, -1.26]},  # 橙色
            {"color": "#A52A2A",
             "points": [0.0, 0.09, 0.45, 0.54, 0.27, -0.18, 0.09, 0.27, -0.18, -4.61, -3.97, -2.89, -2.08, -1.81, -1.54,
                        -1.9, -2.44, -2.26, -2.35, -1.45, -1.26, -0.72, -1.26]},  # 棕色
            {"color": "#808080",
             "points": [0.0, 0.57, 0.84, 0.85, 0.42, -0.33, 0.31, 0.29, -0.17, -4.34, -3.92, -2.85, -1.7, -2.3, -2.14,
                        -2.71, -3.63, -3.37, -3.2, -2.76, -2.48, -2.14, -2.49]},  # 灰色
            {"color": "#008000",
             "points": [0.0, -0.19, 0.25, 0.46, 2.07, 3.4, 4.09, 3.68, 4.54, 1.1, 1.55, 3.1, 4.82, 6.96, 7.71, 7.77,
                        10.37, 11.03, 11.31, 13.43, 16.28, 11.68, 12.14]}  # 深绿
        ]

        self.initUI()

    def generate_date_labels(self, count):
        """生成日期标签列表"""
        base_date = datetime(2023, 1, 1)
        return [(base_date + timedelta(days=i)).strftime("%Y-%m-%d") for i in range(count)]

    def initUI(self):
        container = QWidget()
        layout = QVBoxLayout()

        self.browser = QWebEngineView()

        # 添加控制按钮（演示动态更新）
        btn_update = QPushButton("更新数据", self)
        btn_update.clicked.connect(self.update_chart_data)

        layout.addWidget(btn_update)
        layout.addWidget(self.browser)
        container.setLayout(layout)
        self.setCentralWidget(container)

        self.load_chart()

        self.setGeometry(100, 100, 1000, 700)
        self.setWindowTitle('增强型线段图表')

    def load_chart(self):
        """加载图表模板并注入数据"""
        html_file = QFile("chart.html")
        if html_file.open(QIODevice.OpenModeFlag.ReadOnly | QIODevice.OpenModeFlag.Text):
            html_content = html_file.readAll().data().decode('utf-8')
            html_file.close()

            # 注入所有配置和数据
            html_content = html_content.replace(
                '/*CONFIG_PLACEHOLDER*/',
                f'const chartConfig = {json.dumps(self.chart_config)};'
            ).replace(
                '/*DATA_PLACEHOLDER*/',
                f'const lineData = {json.dumps(self.line_data)};'
            )

            self.browser.setHtml(html_content, QUrl.fromLocalFile(""))

    def update_chart_data(self):
        """演示动态更新数据"""
        # 生成新的随机数据
        import random
        new_data = []
        for i, line in enumerate(self.line_data):
            new_points = [p + random.uniform(-1, 1) for p in line["points"]]
            new_data.append({
                "color": line["color"],
                "points": new_points
            })

        # 更新配置（演示动态修改Y轴范围）
        new_config = {
            "y_min": min(min(line["points"]) for line in new_data) - 1,
            "y_max": max(max(line["points"]) for line in new_data) + 1,
            "show_x_labels": self.chart_config["show_x_labels"],
            "x_labels": self.chart_config["x_labels"],
            "legends": self.chart_config["legends"]
        }

        # 发送到JS端
        self.browser.page().runJavaScript(
            f"updateChart({json.dumps(new_data)}, {json.dumps(new_config)})"
        )


if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = EnhancedChartWindow()
    win.show()
    sys.exit(app.exec())