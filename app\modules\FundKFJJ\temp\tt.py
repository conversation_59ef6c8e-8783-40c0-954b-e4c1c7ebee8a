class tt():
    def __init__(self, index: int, tab_index: int, page: int, sort_dep: int, sort: str, start_date: str, end_date: str):
        self.base_url = "https://fund.eastmoney.com/data/rankhandler.aspx?"
        self.header = {
            "Host": "fund.eastmoney.com",
            "Connection": "keep-alive",
            "sec-ch-ua-platform": "\"Windows\"",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"",
            "sec-ch-ua-mobile": "?0",
            "Accept": "*/*",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Dest": "script",
            "Referer": "https://fund.eastmoney.com/data/fundranking.html",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; nid_id=533447622; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; EMFUND0=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND1=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND2=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND3=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND4=08-04%2012%3A35%3A13@%23%24%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; EMFUND5=08-07%2023%3A04%3A55@%23%24%u4E2D%u4FE1%u5EFA%u6295%u5317%u4EA4%u6240%u7CBE%u9009%u4E24%u5E74%u5B9A%u5F00%u6DF7%u5408A@%23%24016303; EMFUND6=08-07%2023%3A10%3A30@%23%24%u65B0%u534E%u5229%u7387%u503A%u503A%u5238E@%23%24016295; EMFUND7=08-08%2011%3A27%3A07@%23%24%u5FB7%u90A6%u946B%u661F%u4EF7%u503C%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408A@%23%24001412; EMFUND8=08-08%2011%3A31%3A38@%23%24%u534E%u590F%u5317%u4EA4%u6240%u7CBE%u9009%u4E24%u5E74%u5B9A%u5F00%u6DF7%u5408%u53D1%u8D77%u5F0F@%23%24014283; EMFUND9=08-08 11:34:38@#$%u6C47%u6DFB%u5BCC%u5065%u5EB7%u751F%u6D3B%u4E00%u5E74%u6301%u6709%u6DF7%u5408A@%23%24011826; st_si=62826458616042; fullscreengg=1; fullscreengg2=1; emshistory=%5B%22%E5%86%9B%E5%B7%A5%22%5D; websitepoptg_api_time=1754973537603; st_asi=delete; _adsame_fullscreen_20308=1; _qimei_i_1=2cba6edcd65c; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=45; st_psi=20250813134526457-112200312936-2687972716; ASP.NET_SessionId=s5ni0uype3hr2jcflkykpvyv"
        }
        print(tab_index)
        self.kf_type = ["all", "zs", "gp", "hh", "zq", "qdii", "fof"]
        self.kf_index = index
        self.tab = self.tab_(index).get(tab_index)
        self.page = page
        self.sort_dep = self.sort_dep_fun(sort_dep)
        self.sort_status = sort
        self.start_date = start_date
        self.end_date = end_date
        self.params = {
            "op": "ph",  # 排行
            "dt": "kf",  # 开放基金
            "ft": self.kf_type[self.kf_index],
            "rs": "",
            "gs": 0,
            "sc": self.sort_dep,  # 排序依据 基金代码 基金简称， 日期，单位净值，累计净值，且增长率，近1周，近1月，近3月，近6月，近1年，近2年，近3年，今年来，成立来，自定义
            # ["dm","jc","jzrq","dwjz","ljjz","rzdf","zzf","1yzf","3yzf","6yzf","1nzf","2nzf","3nzf","jnzf","lnzf","qjzf"]
            "st": self.sort_status,  # 升序，降序["desc","asc"]
            "sd": self.start_date,  # sc="qjzf" sd起始日期
            "ed": self.end_date,  # sc="qjzf" ed结束日期
            "qdii": self.tab,  # 所有默认为空   %7C:|
            # 指数，债券，qdii，
            # 指数{"沪深指数":"053|","行业主题":"054|","大盘指数":"01|","中小盘指数":"02,03|","股票指数":"001|","债券指数":"003|","海外指数":"000003|"}
            # 债券{"长期纯债":"041|","短期纯债":"042|","混合债基":"043|","定期开放债券":"008|","可转债":"045|",}
            # qdii{"全球股票":"311","亚太股票":"312","大中华区股票":"313","美国股票":"317","股债混合":"320","债券":"330","商品":"340",}
            "tabSubtype": ",,,,,",
            "pi": self.page,  # 页数
            "pn": 50,  # 单页条数
            "dx": 1,
            "v": 0.7143502235731323
        }


    def tab_(self, index, ):
        if index == 1:
            tab_l = {0: "", 1: "053|", 2: "054|", 3: "01|", 4: "02,03|", 5: "001|", 6: "003|", 7: "000003|"}
        elif index == 4:
            tab_l = {0: "", 1: "041|", 2: "042|", 3: "043|", 4: "008|", 5: "045|", }
        elif index == 5:
            tab_l = {0: "", 1: "311", 2: "312", 3: "313", 4: "317", 5: "320", 6: "330", 7: "340", }
        else:
            tab_l = {0: "|"}
        return tab_l


    def sort_dep_fun(self, index):
        sort_dep_l = ["dm", "jc", "jc", "jzrq", "dwjz", "ljjz", "rzdf", "zzf", "1yzf", "3yzf", "6yzf", "1nzf", "2nzf",
                      "3nzf", "jnzf", "lnzf", "", "qjzf"]
        return sort_dep_l[index]


    def run(self):
        try:
            url = self.base_url + f"op={self.params["op"]}&" + f"dt={self.params["dt"]}&" + f"ft={self.params["ft"]}&" + f"re={self.params["rs"]}&" + f"gs={self.params["gs"]}&" + f"sc={self.params["sc"]}&" + f"st={self.params["st"]}&" + f"sd={self.params["sd"]}&" + f"ed={self.params["ed"]}&" + f"qdii={self.params["qdii"]}&" + f"tabSubtype={self.params["tabSubtype"]}&" + f"pi={self.params["pi"]}&" + f"pn={self.params["pn"]}&" + f"dx={self.params["dx"]}&" + f"v={self.params["v"]}"
            data = requests.get(url, headers=self.header, verify=False).text
            print(data)
            json1 = data.replace("var rankData = {datas:", "").replace("};", "")
            json2 = data.replace("var rankData = {datas:", "")
            split_s = json2.index("]") + 1  # 分割符索引
            data_l = ast.literal_eval(json2[0:split_s])  # 行数据列表
            data_list = []
            for i in data_l:
                tt = list(i.split(",")[0:17])
                tt = [
                    "-" if item == "" and 5 < i < 15  # 对第 5-15 个元素，空字符串替换为 "-"
                    else f"{float(item):.2f}%" if item != "" and 5 < i < 16  # 对第 5-15 个元素，非空字符串先转化为浮点型，再保留两位小数并加上 "%"
                    else item  # 其他元素保持不变
                    for i, item in enumerate(tt)
                ]
                if i.split(",")[18] == "":
                    tt.append("-")
                else:
                    tt.append("{:.2f}%".format(float(i.split(",")[18])))
                data_list.append(tt)
            data_all_info = json1[split_s + 1:].split(",")
            info_key, info_value = [], []
            for i in range(0, len(data_all_info)):
                info_key.append(data_all_info[i].split(":")[0])
                info_value.append(int(data_all_info[i].split(":")[1]))
            info_dict = dict(zip(info_key, info_value))
            print(f"kf_index={self.kf_index}", f"kf_sub_index={self.tab}", f"kf_page={self.page}", self.sort_dep,
                  self.sort_status, self.start_date, self.end_date)
            self.finished.emit(data_list, info_dict)
        except Exception as e:
            print(e)