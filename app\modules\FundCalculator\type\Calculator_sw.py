from app.modules.FundCalculator.api.exchange import get_exchange
from app.modules.FundCalculator.api.get_exchange_html import get_exchange_html
from app.modules.FundCalculator.common_ui import common_
from main_w import Ui_MainWindow
import asyncio
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit

class Calculator_sw:
    def __init__(self, ui:Ui_MainWindow,):
        self.ui = ui
        self.common_ui = common_(self.ui)
        self.init_setup()

    def init_setup(self):
        self.ui.calculator_type_lb.setText("税务类计算器")
        self.sub_listWidget_list = ["个人所得税计算器", "购房相关税费计算器"]

    def show_sw_gf_widget(self):
        try:
            self.sw_gz_status = True
            self.common_ui.update_result_lb(["房款总价：", "印花税：", "契税："])
            self.common_ui.clear_result(["元", "元", "元"])
            self.sw_inner_list = ["", ""]
            for i in range(1, 3):
                getattr(self.ui, f"sw_gf_input_{i}").setText(self.sw_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_sw_gf

            def clear_sw_gf(event):
                self.common_ui.clear_result(["元", "元", "元"])
                self.ui.sw_gf_input_1.setText("")
                self.ui.sw_gf_input_2.setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_sw_gf
        except Exception as e:
            print(e)


    def return_result_sw_gf(self, event):
        try:
            result = []
            money = self.ui.sw_gf_input_1.text()
            area = self.ui.sw_gf_input_2.text()
            if self.common_ui.check_calculator_num(input_str=money) and self.common_ui.check_calculator_num(input_str=area):
                result1 = float(money) * float(area)
                result2 = result1 * 0.0005
                result3 = result1 * 0.015
                result = [result1, result2, result3]
            print(result)
            for i in range(1, 4):
                getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def show_sw_gr_widget(self):
        try:
            self.sw_rate = 3
            self.sw_gz_status = True
            self.common_ui.update_result_lb(["适用税率:", "应交税款：", "实得收入："])
            self.common_ui.clear_result(["%", "元", "元"])
            self.sw_inner_list = ["", "0", "5000", ""]
            for i in range(1, 4):
                getattr(self.ui, f"sw_gr_input_{i}").setText(self.sw_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_sw_gr

            def clear_sw_gr(event):
                self.common_ui.clear_result(["%", "元", "元"])
                self.ui.sw_gr_input_1.setText("")
                self.ui.sw_gr_input_2.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_sw_gr
        except Exception as e:
            print(e)


    def return_result_sw_gr(self, event):
        try:
            result = []
            money = self.ui.sw_gr_input_1.text()
            sb_money = self.ui.sw_gr_input_2.text()
            start_money = self.ui.sw_gr_input_3.text()
            self.ui.sw_gr_input_4.currentTextChanged.connect(self.gr_combox_select)
            if self.common_ui.check_calculator_num(money) and self.sw_gz_status:
                result1 = self.sw_rate
                if float(money) > float(start_money):
                    result2 = self.sw_rate / 100 * (float(money) - float(start_money) - float(sb_money))
                else:
                    result1 = 0
                    result2 = 0
                result3 = float(money) - result2 - float(sb_money)
                result = [result1, result2, result3]
            elif self.common_ui.check_calculator_num(money) and not self.sw_gz_status:
                result1 = self.sw_rate
                result2 = self.sw_rate / 100 * float(money)
                result3 = float(money) - result2
                result = [result1, result2, result3]
            for i in range(1, 4):
                getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def gr_combox_select(self,text):
        match (text):
            case "工资、薪金所得":
                self.sw_rate = 3
                self.sw_gz_status=True
                self.hide_show_sw_gr(True)
            case "个体工商户生产、经营所得":
                self.sw_gz_status = False
                self.sw_rate = 5
                self.hide_show_sw_gr(False)
            case "对企事业单位的承包经营、承租经营所得":
                self.sw_gz_status = False
                self.sw_rate = 35
                self.hide_show_sw_gr(False)
            case "劳务报酬所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "稿酬所得":
                self.sw_gz_status = False
                self.sw_rate = 14
                self.hide_show_sw_gr(False)
            case "特许权使用所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "利息、股息、红利所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "财产租赁所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "财产转让所得":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "偶然所得(如: 中奖、中彩)":
                self.sw_gz_status = False
                self.sw_rate = 20
                self.hide_show_sw_gr(False)
            case "个人拍卖所得":
                self.sw_gz_status = False
                self.hide_show_sw_gr(False)
                self.sw_rate = 20
    def bind_sw_sub_(self, item):
        item_name = item.text()
        match (item_name):
            case "个人所得税计算器":
                self.show_sw_gr_widget()
            case "购房相关税费计算器":
                self.show_sw_gf_widget()
        self.common_ui.sub_type_widget_(self.sw_type, item_name,self.sub_listWidget_list)

    def hide_show_sw_gr(self, status):
        if status:
            self.ui.sw_item_lb_2.show()
            self.ui.sw_item_lb_3.show()
            self.ui.sw_gr_input_2.show()
            self.ui.sw_gr_input_3.show()
        else:
            self.ui.sw_item_lb_2.hide()
            self.ui.sw_item_lb_3.hide()
            self.ui.sw_gr_input_2.hide()
            self.ui.sw_gr_input_3.hide()

    def show_sw_widget(self):
        self.sw_type = ["gr", "gf", ]
        self.ui.sw_total_widget.show()
        # 绑定左侧列表
        self.ui.calculator_type_listWidget.itemClicked.connect(self.bind_sw_sub_)
        self.show_sw_gr_widget()
        self.common_ui.clear_result(["%", "元", "元"])
        self.common_ui.sub_type_widget_(self.sw_type, self.sub_listWidget_list[0],self.sub_listWidget_list)

