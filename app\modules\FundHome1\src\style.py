QLabel_green="""
color:#00811F;
"""
QLabel_red="""
color:#FF030F;
}
"""
QLabel_black="""
color:#6B6B6B;
"""
QWidget_green_bg="""
QLabel {  
    border: 1; /* 移除边框 */  
    background-color: qlineargradient(  
        x1: 0, y1: 0,  
        x2: 0, y2: 1,  
        stop: 0 #BCE2BD, /* 红色开始 */  
        stop: 1 #ffffff  /* 绿色结束 */  
    );  
    border-radius: 6px; /* 圆角 */  
}  
"""
QWidget_red_bg="""
QLabel {  
    border: 1; /* 移除边框 */  
    background-color: qlineargradient(  
        x1: 0, y1: 0,  
        x2: 0, y2: 1,  
        stop: 0 #f9c3c6, /* 红色开始 */  
        stop: 1 #ffffff  /* 绿色结束 */  
    );  
    border-radius: 6px; /* 圆角 */  
}  
"""
QWidget_black_bg="""
QLabel {  
    border: 1; /* 移除边框 */  
    background-color: qlineargradient(  
        x1: 0, y1: 0,  
        x2: 0, y2: 1,  
        stop: 0 #D3D3D3, /* 红色开始 */  
        stop: 1 #ffffff  /* 绿色结束 */  
    );  
    border-radius: 6px; /* 圆角 */  
}  
"""