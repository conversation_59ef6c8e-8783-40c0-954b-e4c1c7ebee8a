from app.modules.FundCalculator.common_ui import common_
from main_w import Ui_MainWindow
import asyncio
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit

class Calculator_jj():
    def __init__(self,ui:Ui_MainWindow):
        self.ui = ui
        self.common_ui = common_(self.ui)
        self.init_setup()

    def init_setup(self):
        self.img_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"
        self.ui.calculator_type_lb.setText("基金类计算器")
        self.sub_listWidget_list = ["申(认)购费用计算器", "赎回费用计算器", "收益计算器", "持有期计算器"]


    def show_jj_jjsg_widget(self):
        try:
            self.common_ui.update_result_lb(["手续费：", "成交份额：", "准申购金额："])
            self.common_ui.clear_result(["元", "份", "元"])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_jj_jjsg

            def clear_jj_jjsg(event):
                self.common_ui.clear_result(["元", "份", "元"])
                self.ui.jj_jjsg_input_1.setText("0")
                self.ui.jj_jjsg_input_2.setText("0")
                self.ui.jj_jjsg_input_3.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_jj_jjsg
        except Exception as e:
            print(e)

    def return_result_jj_jjsg(self, event):
        try:
            result = []
            money = self.ui.jj_jjsg_input_1.text()
            dwjz = self.ui.jj_jjsg_input_2.text()  # 单位净值
            rate = self.ui.jj_jjsg_input_3.text()  # 单位净值
            if self.common_ui.check_calculator_num(money) and self.common_ui.check_calculator_num(dwjz) and self.common_ui.check_calculator_num(rate):
                result3 = float(money) / (1 + float(rate) / 100)  # 净申购金额
                result1 = (float(money) - result3)  # 手续费
                result2 = float(result3) / float(dwjz)  # 成交份额
                result = [result1, result2, result3]
            for i in range(1, 4):
                getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_jj_jjsh_widget(self):
        try:
            self.common_ui.update_result_lb(["手续费：", "确认金额:", ""])
            self.common_ui.clear_result(["元", "份", ""])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_jj_jjsh

            def clear_jj_jjsh(event):
                self.common_ui.clear_result(["元", "份", ""])
                self.ui.jj_jjsh_input_1.setText("0")
                self.ui.jj_jjsh_input_2.setText("0")
                self.ui.jj_jjsh_input_3.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_jj_jjsh
        except Exception as e:
            print(e)

    def return_result_jj_jjsh(self, event):
        try:
            p_num = self.ui.jj_jjsh_input_1.text()
            dwjz = self.ui.jj_jjsh_input_2.text()  # 单位净值
            rate = self.ui.jj_jjsh_input_3.text()  # 单位净值
            if self.common_ui.check_calculator_num(p_num) and self.common_ui.check_calculator_num(dwjz) and self.common_ui.check_calculator_num(rate):
                result1 = float(p_num) * float(dwjz) * float(rate) / 100  # 手续费
                result2 = float(p_num) * float(dwjz) - result1  # 确认金额
                result = [result1, result2]
                for i in range(1, 3):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_jj_jjsy_widget(self):
        try:
            self.common_ui.update_result_lb(["持有期总收益率：", "持有期年化收益率：", ""])
            self.common_ui.clear_result(["%", "%", ""])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_jj_jjsy

            def clear_jj_jjsy(event):
                self.common_ui.clear_result(["%", "%", ""])
                self.ui.jj_jjsy_input_1.setText("0")
                self.ui.jj_jjsy_input_2.setText("0")
                self.ui.jj_jjsy_input_3.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_jj_jjsy
        except Exception as e:
            print(e)

    def return_result_jj_jjsy(self, event):
        try:
            s_money = self.ui.jj_jjsy_input_1.text()
            e_money = self.ui.jj_jjsy_input_2.text()  # 单位净值
            date_ = self.ui.jj_jjsy_input_3.text()  # 单位净值
            if self.common_ui.check_calculator_num(s_money) and self.common_ui.check_calculator_num(e_money) and self.common_ui.check_calculator_num(
                    date_):
                result1 = (float(e_money) - float(s_money)) / float(s_money) * 100
                result2 = result1 * 365 / float(date_)
                result = [result1, result2]
                for i in range(1, 3):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_jj_jjcyq_widget(self):
        try:
            self.common_ui.update_result_lb(["持有天数：", "持有月数", "持有年数"])
            self.common_ui.clear_result(["天", "月", "年"])
            self.jj_jjcyq_cal_1_status = False
            self.jj_jjcyq_cal_2_status = False
            self.ui.jj_jjcyq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.jj_jjcyq_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.jj_jjcyq_cal1.mousePressEvent = partial(self.toggle_calculator_cal,
                                                         status_name="jj_jjcyq_cal_1_status",
                                                         cal_name="jj_jjcyq_calendarWidget_1")
            self.ui.jj_jjcyq_cal2.mousePressEvent = partial(self.toggle_calculator_cal,
                                                         status_name="jj_jjcyq_cal_2_status",
                                                         cal_name="jj_jjcyq_calendarWidget_2")
            self.ui.jj_jjcyq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("jj_jjcyq_input_1", date)
            )
            self.ui.jj_jjcyq_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("jj_jjcyq_input_2", date)
            )
            self.common_ui.hide_cal()
            self.jj_inner_list = [datetime.today().strftime("%Y-%m-%d"), ""]
            for i in range(1, 3):
                getattr(self.ui, f"jj_jjcyq_input_{i}").setText(self.jj_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_jj_jjcyq

            def clear_jj_jjcyq(event):
                self.common_ui.clear_result(["天", "月", "年"])
                self.ui.jj_jjcyq_input_1.setText(datetime.today().strftime("%Y-%m-%d"))
                self.ui.jj_jjcyq_input_2.setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_jj_jjcyq
        except Exception as e:
            print(f"show_jj_jjcyq_widget error :{e}")

    def toggle_calculator_cal(self, event, status_name: str, cal_name: str):
        try:
            current_status = getattr(self, status_name)  # 获取当前状态

            if current_status:
                getattr(self.ui, cal_name).hide()  # 隐藏控件
                setattr(self, status_name, False)  # 设置状态为 False
            else:
                getattr(self.ui, cal_name).show()  # 显示控件
                setattr(self, status_name, True)  # 设置状态为 True
        except Exception as e:
            print(f"toggle_calculator_cal{e}")



    def on_calculator_cal_clicked(self, input_name: str, date: QDate):
        try:
            input_widget = getattr(self.ui, input_name)
            input_widget.setText(date.toString('yyyy-MM-dd'))
        except Exception as e:
            print(f"Error: {e}")
    def show_jj_widget(self):
            self.jj_type = ["jjsg", "jjsh", "jjsy", "jjcyq"]
            self.ui.jj_total_widget.show()
            self.ui.calculator_type_listWidget.itemClicked.connect(self.bind_jj_sub_)
            self.show_jj_jjsg_widget()
            self.common_ui.clear_result(["元", "份", "元"])
            self.common_ui.sub_type_widget_(self.jj_type, self.sub_listWidget_list[0],self.sub_listWidget_list)



    def return_result_jj_jjcyq(self, event):
        try:
            start_date = self.ui.jj_jjcyq_input_1.text()
            end_date = self.ui.jj_jjcyq_input_2.text()
            if self.common_ui.check_calculator_cal_(start_date, end_date, "1") and self.common_ui.check_calculator_cal_(start_date,
                                                                                                    end_date, "1"):
                result1 = self.common_ui.check_calculator_cal_(start_date, end_date, "1")
                result2 = self.common_ui.check_calculator_cal_(start_date, end_date, "1") / 30
                result3 = self.common_ui.check_calculator_cal_(start_date, end_date, "1") / 365
                result = [result1, result2, result3]
                for i in range(1, 4):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)
    def bind_jj_sub_(self, item):
            item_name = item.text()
            match (item_name):
                case "申(认)购费用计算器":
                    self.show_jj_jjsg_widget()
                case "赎回费用计算器":
                    self.show_jj_jjsh_widget()
                case "收益计算器":
                    self.show_jj_jjsy_widget()
                case "持有期计算器":
                    self.show_jj_jjcyq_widget()
            self.common_ui.sub_type_widget_(self.jj_type, item_name,self.sub_listWidget_list)