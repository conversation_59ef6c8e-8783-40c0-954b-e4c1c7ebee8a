import sys
from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget,
                             QHBoxLayout, QPushButton, QButtonGroup,
                             QScrollArea, QVBoxLayout)
from PyQt6.QtCore import Qt


class ScrollableHorizontalMenu(QMainWindow):
    def __init__(self, items):
        super().__init__()
        self.setWindowTitle("可滚动水平菜单")
        self.setGeometry(100, 100, 600, 50)

        # 主窗口部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 主布局（包含左右箭头和滚动区域）
        self.main_layout = QHBoxLayout(self.central_widget)
        self.main_layout.setSpacing(0)
        self.main_layout.setContentsMargins(0, 0, 0, 0)

        # 左箭头按钮
        self.left_arrow = QPushButton("◀")
        self.left_arrow.setFixedWidth(30)
        self.left_arrow.setFixedHeight(30)
        self.left_arrow.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        self.left_arrow.clicked.connect(self.scroll_left)
        self.left_arrow.setEnabled(False)  # 初始不可用（因为内容可能不需要滚动）

        # 右箭头按钮
        self.right_arrow = QPushButton("▶")
        self.right_arrow.setFixedWidth(30)
        self.right_arrow.setFixedHeight(30)
        self.right_arrow.setStyleSheet("""
            QPushButton {
                background-color: #f0f0f0;
                border: 1px solid #ccc;
                border-radius: 4px;
                font-size: 16px;
            }
            QPushButton:hover {
                background-color: #e0e0e0;
            }
            QPushButton:pressed {
                background-color: #d0d0d0;
            }
        """)
        self.right_arrow.clicked.connect(self.scroll_right)
        self.right_arrow.setEnabled(False)  # 初始不可用（因为内容可能不需要滚动）

        # 滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(False)  # 禁止自动调整大小
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)

        # 滚动内容容器
        self.scroll_content = QWidget()
        self.scroll_layout = QHBoxLayout(self.scroll_content)
        self.scroll_layout.setSpacing(10)
        self.scroll_layout.setContentsMargins(10, 0, 10, 0)

        # 存储按钮和对应项的字典
        self.button_map = {}

        # 创建按钮
        self.create_buttons(items)

        # 设置滚动区域的内容
        self.scroll_area.setWidget(self.scroll_content)

        # 添加到主布局
        self.main_layout.addWidget(self.left_arrow)
        self.main_layout.addWidget(self.scroll_area)
        self.main_layout.addWidget(self.right_arrow)

        # 更新箭头状态
        self.update_arrows()

    def create_buttons(self, items):
        """根据提供的列表创建水平按钮"""
        for item in items:
            button = QPushButton(item)
            button.setFixedHeight(30)  # 设置固定高度
            button.setStyleSheet("""
                QPushButton {
                    background-color: #f0f0f0;
                    border: 1px solid #ccc;
                    border-radius: 4px;
                    padding: 5px 15px;
                    font-size: 14px;
                }
                QPushButton:hover {
                    background-color: #e0e0e0;
                }
                QPushButton:pressed {
                    background-color: #d0d0d0;
                }
            """)

            # 连接点击信号
            button.clicked.connect(lambda checked, text=item: self.on_button_click(text))

            # 添加到布局和字典
            self.scroll_layout.addWidget(button)
            self.button_map[item] = button

    def on_button_click(self, item_text):
        """按钮点击事件处理"""
        print(f"点击了: {item_text}")
        # 这里可以添加你的业务逻辑
        # 例如高亮选中的按钮
        self.highlight_button(item_text)

    def highlight_button(self, item_text):
        """高亮显示被点击的按钮"""
        for text, button in self.button_map.items():
            if text == item_text:
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #4CAF50;
                        color: white;
                        border: 1px solid #4CAF50;
                        border-radius: 4px;
                        padding: 5px 15px;
                        font-size: 14px;
                    }
                """)
            else:
                button.setStyleSheet("""
                    QPushButton {
                        background-color: #f0f0f0;
                        border: 1px solid #ccc;
                        border-radius: 4px;
                        padding: 5px 15px;
                        font-size: 14px;
                    }
                    QPushButton:hover {
                        background-color: #e0e0e0;
                    }
                    QPushButton:pressed {
                        background-color: #d0d0d0;
                    }
                """)

    def scroll_left(self):
        """向左滚动"""
        current_scroll = self.scroll_area.horizontalScrollBar().value()
        new_scroll = max(0, current_scroll - 100)  # 每次滚动100像素
        self.scroll_area.horizontalScrollBar().setValue(new_scroll)
        self.update_arrows()

    def scroll_right(self):
        """向右滚动"""
        current_scroll = self.scroll_area.horizontalScrollBar().value()
        max_scroll = self.scroll_area.horizontalScrollBar().maximum()
        new_scroll = min(max_scroll, current_scroll + 100)  # 每次滚动100像素
        self.scroll_area.horizontalScrollBar().setValue(new_scroll)
        self.update_arrows()

    def update_arrows(self):
        """更新左右箭头的可用状态"""
        current_scroll = self.scroll_area.horizontalScrollBar().value()
        max_scroll = self.scroll_area.horizontalScrollBar().maximum()

        self.left_arrow.setEnabled(current_scroll > 0)
        self.right_arrow.setEnabled(max_scroll > 0 and current_scroll < max_scroll)


if __name__ == "__main__":
    app = QApplication(sys.argv)

    # 示例列表（可以很长）
    menu_items = [
        "全部", "智能分组", "持仓", "交易记录", "账户信息",
        "设置", "帮助", "关于", "退出", "更多功能1", "更多功能2","更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1", "更多功能1",
    ]

    window = ScrollableHorizontalMenu(menu_items)
    window.show()

    sys.exit(app.exec())