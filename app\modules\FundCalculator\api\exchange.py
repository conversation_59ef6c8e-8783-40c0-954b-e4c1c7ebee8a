import re

class get_exchange:
    def __init__(self):
        self.currency_code=['CNY', 'USD', 'EUR', 'GBP', 'AUD', 'CAD', 'JPY', 'HKD', 'INR', 'ZAR', 'TWD', 'MOP', 'THB', 'NZD', 'SGD', 'AED', 'ALL', 'AMD', 'ANG', 'AOA', 'ARS', 'AWG', 'AZN', 'BAM', 'BBD', 'BDT', 'BGN', 'BHD', 'BMD', 'BND', 'BOB', 'BRL', 'BSD', 'BTN', 'BWP', 'BZD', 'CHF', 'CLP', 'COP', 'CRC', 'CVE', 'CZK', 'DJF', 'DKK', 'DOP', 'DZD', 'EGP', 'ETB', 'FJD', 'FKP', 'GEL', 'GHS', 'GIP', 'GMD', 'GNF', 'GTQ', 'GYD', 'HNL', 'HRK', 'HTG', 'HUF', 'IDR', 'ILS', 'ISK', 'JMD', 'JOD', 'KES', 'KGS', 'KHR', 'KMF', 'KPW', 'KWD', 'KYD', 'KZT', 'LAK', 'LBP', 'LKR', 'LRD', 'LSL', 'MAD', 'MDL', 'MGA', 'MKD', 'MNT', 'MUR', 'MVR', 'MWK', 'MXN', 'MYR', 'MZN', 'NAD', 'NGN', 'NIO', 'NOK', 'NPR', 'OMR', 'PAB', 'PEN', 'PGK', 'PHP', 'PKR', 'PLN', 'PYG', 'QAR', 'RON', 'RSD', 'RWF', 'SAR', 'SBD', 'SCR', 'SEK', 'SHP', 'SLL', 'SRD', 'SVC', 'SZL', 'TJS', 'TMT', 'TND', 'TOP', 'TRY', 'TTD', 'TZS', 'UAH', 'UGX', 'UYU', 'UZS', 'VND', 'VUV', 'WST', 'XAF', 'XOF', 'XPF', 'ZMW']
        self.currency_name=['人民币', '美元', '欧元', '英镑', '澳元', '加元', '日元', '港币', '印度卢比', '南非兰特', '新台币', '澳门元', '泰铢', '新西兰元', '新加坡元', '阿联酋迪拉姆', '阿尔巴尼列克', '亚美尼亚德拉姆', '荷兰盾', '安哥拉宽扎', '阿根廷比索', '阿鲁巴弗罗林', '阿塞拜疆马纳特', '波黑可兑换马克', '巴巴多斯元', '孟加拉国塔卡', '保加利亚列弗', '巴林第纳尔', '百慕大元', '文莱元', '玻利维亚诺', '巴西雷亚尔', '巴哈马元', '不丹努扎姆', '博茨瓦纳普拉', '伯利兹元', '瑞士法郎', '智利比索', '哥伦比亚比索', '哥斯达黎加科朗', '佛得角埃斯库多', '捷克克朗', '吉布提法郎', '丹麦克朗', '多米尼加比索', '阿尔及利亚第纳尔', '埃及镑', '埃塞俄比亚比尔', '斐济元', '福克兰群岛镑', '格鲁吉亚拉里', '加纳塞地', '直布罗陀镑', '冈比亚达拉西', '几内亚法郎', '危地马拉格查尔', '圭亚那元', '洪都拉斯伦皮拉', '克罗地亚库纳', '海地古德', '匈牙利福林', '印度尼西亚卢比', '以色列新谢克尔', '冰岛克郎', '牙买加元', '约旦第纳尔', '肯尼亚先令', '吉尔吉斯斯坦索姆', '柬埔寨瑞尔', '科摩罗法郎', '朝鲜元', '科威特第纳尔', '开曼群岛元', '哈萨克斯坦坚戈', '老挝基普', '黎巴嫩镑', '斯里兰卡卢比', '利比里亚元', '莱索托洛蒂', '摩洛哥迪拉姆', '摩尔多瓦列伊', '马达加斯加阿里亚里', '马其顿代纳尔', '蒙古图格里克', '毛里求斯卢比', '马尔代夫拉菲亚', '马拉维克瓦查', '墨西哥比索', '林吉特', '莫桑比克新梅蒂卡尔', '纳米比亚元', '尼日利亚奈拉', '尼加拉瓜新科多巴', '挪威克朗', '尼泊尔卢比', '阿曼里亚尔', '巴拿马巴波亚', '秘鲁新索尔', '巴布亚新几内亚基那', '菲律宾比索', '巴基斯坦卢比', '波兰兹罗提', '巴拉圭瓜拉尼', '卡塔尔里亚尔', '罗马尼亚列伊', '塞尔维亚第纳尔', '卢旺达法郎', '沙特里亚尔', '所罗门群岛元', '塞舌尔卢比', '瑞典克朗', '圣赫勒拿镑', '塞拉利昂利昂', '苏里南元', '萨尔瓦多科朗', '斯威士兰里兰吉尼', '塔吉克斯坦索莫尼', '土库曼斯坦马纳特', '突尼斯第纳尔', '汤加潘加', '土耳其里拉', '特立尼达多巴哥元', '坦桑尼亚先令', '乌克兰格里夫纳', '乌干达先令', '乌拉圭比索', '乌兹别克斯坦苏姆', '越南盾', '瓦努阿图瓦图', '萨摩亚塔拉', '中非法郎', '西非法郎', '太平洋法郎', '赞比亚克瓦查']
        self.currency_list=[]
        self.currency_dict=self.return_dict_()

    def return_dict_(self):
        return dict(zip(self.currency_code,  self.currency_name))

    def get_currency_list(self):
        for i in range(len(self.currency_code)):
            self.currency_list.append(f"{self.currency_code[i]} - { self.currency_name[i]}")
        return self.currency_list

    def match_code_letter(self,query:str):
        temp_result_list=[]
        input_code = query.upper().strip()
        matches = {code: name for code, name in self.currency_dict.items() if input_code in code}
        if matches:
            print("匹配结果：")
            for code, name in matches.items():
                temp_result_list.append(f"{code} - {name}")
                # print(f"代码: {code}, 名称: {name}")
        else:
            print("未找到匹配的货币。")
        return  temp_result_list

    def match_code_text(self,query:str):
        temp_result_list = []
        input_text = query.strip()
        matches = {name: code for name, code in self.currency_dict.items() if input_text in name}
        if matches:
            print("匹配结果：")
            for name, code in matches.items():
                temp_result_list.append(f"{code} - {name}")
                # print(f"代码: {code}, 名称: {name}")
        else:
            print("未找到匹配的货币。")
        return temp_result_list

    def input_type(self,input_str:str):
        if re.match(r'^[a-zA-Z]+$', input_str):
            return "code"
            # 判断是否只包含中文
        elif re.match(r'^[\u4e00-\u9fff]+$', input_str):
            return "text"
        else:
            return "error"

    def extraction_code(self,text:str):
        print(f"extraction_code {text}:{text.split("-")[0].replace(" ","").lower()}")
        return text.split("-")[0].replace(" ","").lower()

    def extraction_name(self,text:str):
        return text.split("-")[1].replace(" ","").strip()

get_exchange=get_exchange()
# print(get_exchange.extraction_("GBP - 英镑")[:2])
# print(get_exchange.get_currency_list())
# print("CNY - 人民币" in get_exchange.currency_list)







