import json

import pandas as pd
from datetime import datetime, timedelta

import requests

from datetime import datetime, timedelta

headers = {
      "Host": "api.fund.eastmoney.com",
      "Connection": "keep-alive",
      "sec-ch-ua-platform": "\"Windows\"",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
      "sec-ch-ua-mobile": "?0",
      "Accept": "*/*",
      "Sec-Fetch-Site": "same-site",
      "Sec-Fetch-Mode": "no-cors",
      "Sec-Fetch-Dest": "script",
      "Referer": "https://fundf10.eastmoney.com/",
      "Accept-Encoding": "gzip, deflate, br, zstd",
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
      "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; IsHaveToNewFavor=0; AUTH_FUND.EASTMONEY.COM_GSJZ=AUTH*TTJJ*TOKEN; HAList=ty-90-BK0481-%u6C7D%u8F66%u96F6%u90E8%u4EF6%2Cty-0-159876-%u6709%u8272%u9F99%u5934ETF%2Cty-90-BK1037-%u6D88%u8D39%u7535%u5B50%2Cty-1-600489-%u4E2D%u91D1%u9EC4%u91D1; Eastmoney_Fund=019005_160216_005218_SABZ69_000001_002230_000331_000655_161725_011103_005669_166301_012768_320007_015150; st_si=69649652802874; st_asi=delete; ap_0_13a7d068=1; ap_1_68c1f65b=1; EMFUND0=04-30%2015%3A37%3A09@%23%24%u534E%u5B9D%u6709%u8272%u91D1%u5C5EETF@%23%24159876; EMFUND1=04-30%2016%3A17%3A46@%23%24%u5E7F%u53D1%u6539%u9769%u6DF7%u5408@%23%24001468; EMFUND3=05-01%2018%3A16%3A04@%23%24%u5BCC%u8363%u533B%u836F%u5065%u5EB7%u6DF7%u5408%u53D1%u8D77C@%23%24015656; EMFUND4=05-01%2020%3A34%3A46@%23%24%u534E%u590F%u548C%u8FBE%u9AD8%u79D1REIT@%23%24180103; EMFUND5=05-02%2021%3A57%3A34@%23%24%u6613%u65B9%u8FBE%u5E7F%u5F00%u4EA7%u56EDREIT@%23%24180105; EMFUND6=05-02%2013%3A24%3A17@%23%24%u524D%u6D77%u5F00%u6E90%u516C%u7528%u4E8B%u4E1A%u80A1%u7968@%23%24005669; EMFUND7=05-02%2013%3A24%3A15@%23%24%u56FD%u6CF0%u5927%u5B97%u5546%u54C1@%23%24160216; EMFUND8=05-02%2013%3A24%3A15@%23%24%u56FD%u6295%u745E%u94F6%u767D%u94F6%u671F%u8D27%28LOF%29C@%23%24019005; EMFUND2=05-02%2022%3A15%3A37@%23%24%u534E%u590F%u6210%u957F%u6DF7%u5408@%23%24000001; EMFUND9=05-02 22:18:03@#$%u9E4F%u534E%u78B3%u4E2D%u548C%u4E3B%u9898%u6DF7%u5408C@%23%24016531; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=53; st_psi=20250502222442396-119146300572-3559344240"
    }
def get_nearest_nav_from_dict(nav_dict, added_date):
    added_date = datetime.strptime(added_date, '%Y-%m-%d') if isinstance(added_date, str) else added_date
    sorted_dates = sorted(
        [datetime.strptime(d, '%Y-%m-%d') for d in nav_dict.keys()],
        reverse=True
    )
    for trade_date in sorted_dates:
        if trade_date <= added_date:
            return trade_date.strftime('%Y-%m-%d'), float(nav_dict[trade_date.strftime('%Y-%m-%d')])

def get_fund_nav_history(fund_code, start_date, end_date):
    #获取添加日期前15天和后15天的数据
    url=f"https://api.fund.eastmoney.com/f10/lsjz?callback=jQuery18307133205976102588_1746194332256&fundCode={fund_code}&pageIndex=1&pageSize={20}&startDate={start_date}&endDate={end_date}&_=1746196083017"
    try:
        data = requests.get(url,headers=headers,verify=False)
        data=json.loads(data.text.strip("jQuery18307133205976102588_1746194332256(").rstrip(");"))
        res=data["Data"]["LSJZList"]
        date_data ={}
        for i in res:
            date_data[i["FSRQ"]]=i["DWJZ"]
        return date_data
    except Exception as e:
        print(e)


def get_new_data(fund_code,start_date,end_date):
    url = f"https://api.fund.eastmoney.com/f10/lsjz?callback=jQuery18307133205976102588_1746194332256&fundCode={fund_code}&pageIndex=1&pageSize={20}&startDate={start_date}&endDate={end_date}&_=1746196083017"
    try:
        data = requests.get(url, headers=headers, verify=False)
        data = json.loads(data.text.strip("jQuery18307133205976102588_1746194332256(").rstrip(");"))
        res = data["Data"]["LSJZList"]
        # date_data = {}
        # for i in res:
        #     date_data[i["FSRQ"]] = i["DWJZ"]
        print(float(res[0]["DWJZ"]))
        return float(res[0]["DWJZ"])
    except Exception as e:
        print(e)

def get_date_range(base_date_str, format="%Y-%m-%d"):
    base_date = datetime.strptime(base_date_str, format)
    before_12_days = base_date - timedelta(days=15)
    after_12_days = base_date + timedelta(days=15)
    before_str = before_12_days.strftime(format)
    after_str = after_12_days.strftime(format)
    return before_str, after_str

if __name__ == '__main__':
    fund_code = '006373'
    add_date="2024-01-03"
    pre_add_date,after_add_date=get_date_range(add_date)
    print(pre_add_date,after_add_date)
    new_date=datetime.now().strftime("%Y-%m-%d")
    # get_fund_nav_history(fund_code, pre_add_date, after_add_date)
    date,nav=get_nearest_nav_from_dict(get_fund_nav_history(fund_code, pre_add_date, after_add_date), add_date)
    print(date,nav)
    new_data=get_new_data(fund_code, pre_add_date, new_date)

    print("{:.2f}%".format(((new_data/nav)-1)*100))

