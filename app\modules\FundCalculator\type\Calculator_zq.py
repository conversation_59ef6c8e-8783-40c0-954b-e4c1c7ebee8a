from app.modules.FundCalculator.common_ui import common_
from main_w import Ui_MainWindow
import asyncio
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit
class Calculator_zq:
    def __init__(self, ui:Ui_MainWindow,):
        self.ui = ui
        self.common_ui = common_(self.ui)
        self.init_setup()

    def init_setup(self):
        self.ui.calculator_type_lb.setText("债券类计算器")
        self.sub_listWidget_list = ["债券购买收益率计算器", "债券出售收益率计算器",
                                    "债券持有期间收益率计算器", "国债买卖计算器",
                                    "国债收益计算器", ]
    def show_zq_zqgm_widget(self):
        try:
            self.common_ui.update_result_lb(["债券收益率：", "", ""])
            self.common_ui.clear_result(["%", "", ""])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_zq_zqgm

            def clear_zq_zqgm(event):
                self.common_ui.clear_result(["%", "", ""])
                for i in range(1, 5):
                    getattr(self.ui, f"zq_zqgm_input_{i}").setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_zq_zqgm
        except Exception as e:
            print(e)


    def return_result_zq_zqgm(self, event):
        try:
            zq_money = self.ui.zq_zqgm_input_1.text()
            buy_money = self.ui.zq_zqgm_input_2.text()  # 单位净值
            date_ = self.ui.zq_zqgm_input_3.text()  # 单位净值
            rate = self.ui.zq_zqgm_input_4.text()  # 单位净值
            if self.common_ui.check_calculator_num(zq_money) and self.common_ui.check_calculator_num(
                    buy_money) and self.common_ui.check_calculator_num(rate):
                buy_money = float(buy_money)
                zq_money = float(zq_money)
                rate = float(rate) / 100
                date_ = int(date_)
                ins = (zq_money * rate * (date_ / 365) + (zq_money - buy_money)) / buy_money * 100
                result1 = round(ins, 2)
                self.ui.ck_result_1.setText(str(result1))
        except Exception as e:
            print(e)


    def show_zq_zqcs_widget(self):
        try:
            self.common_ui.update_result_lb(["债券出售收益率：", "", ""])
            self.common_ui.clear_result(["%", "", ""])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_zq_zqcs

            def clear_zq_zqcs(event):
                self.common_ui.clear_result(["%", "", ""])
                for i in range(1, 5):
                    getattr(self.ui, f"zq_zqcs_input_{i}").setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_zq_zqcs
        except Exception as e:
            print(e)


    def return_result_zq_zqcs(self, event):
        try:
            public_money = self.ui.zq_zqcs_input_1.text()
            sell_money = self.ui.zq_zqcs_input_2.text()  # 单位净值
            date_ = self.ui.zq_zqcs_input_3.text()  # 单位净值
            rate = self.ui.zq_zqcs_input_4.text()  # 单位净值
            if self.common_ui.check_calculator_num(public_money) and self.common_ui.check_calculator_num(
                    sell_money) and self.common_ui.check_calculator_num(rate):
                public_money = float(public_money)
                sell_money = float(sell_money)
                rate = float(rate) / 100
                date_ = int(date_)
                # 计算单利利息（按实际天数/365）
                interest = public_money * rate * (date_ / 365)
                # 计算资本利得
                capital_gain = sell_money - public_money
                # 计算总收益和持有期收益率
                total_return = interest + capital_gain
                holding_period_yield = total_return / public_money
                annualized_return = round(holding_period_yield / (date_ / 365) * 100, 2)
                self.ui.ck_result_1.setText(str(annualized_return))
        except Exception as e:
            print(e)


    def show_zq_zqcyq_widget(self):
        try:
            self.common_ui.update_result_lb(["债券持有期间收益率：", "", ""])
            self.common_ui.clear_result(["%", "", ""])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_zq_zqcyq

            def clear_zq_zqcyq(event):
                self.common_ui.clear_result(["%", "", ""])
                for i in range(1, 5):
                    getattr(self.ui, f"zq_zqcyq_input_{i}").setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_zq_zqcyq
        except Exception as e:
            print(e)


    def return_result_zq_zqcyq(self, event):
        try:
            face_value = self.ui.zq_zqcyq_input_1.text()
            buy_price = self.ui.zq_zqcyq_input_2.text()
            sell_price = self.ui.zq_zqcyq_input_3.text()  # 单位净值
            rate = self.ui.zq_zqcyq_input_4.text()  # 单位净值
            date_ = self.ui.zq_zqcyq_input_5.text()  # 单位净值
            if self.common_ui.check_calculator_num(face_value) and self.common_ui.check_calculator_num(
                    buy_price) and self.common_ui.check_calculator_num(sell_price):
                face_value = float(face_value)
                buy_price = float(buy_price)
                sell_price = float(sell_price)
                rate = float(rate) / 100
                date_ = int(date_)
                simple_interest = face_value * rate * (date_ / 365)
                capital_gain = sell_price - buy_price
                total_return_simple = simple_interest + capital_gain
                holding_period_yield_simple = total_return_simple / buy_price  # HPY（持有期收益率）
                annualized_return_simple = f"{holding_period_yield_simple / (date_ / 365) * 100}"
                self.ui.ck_result_1.setText(str(annualized_return_simple))
        except Exception as e:
            print(e)


    def show_zq_gzmm_widget(self):
        try:
            self.common_ui.update_result_lb(["金额：", "", ""])
            self.common_ui.clear_result(["元", "", ""])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_zq_gzmm

            def clear_zq_gzmm(event):
                self.common_ui.clear_result(["元", "", ""])
                for i in range(3, 5):
                    getattr(self.ui, f"zq_gzmm_input_{i}").setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_zq_gzmm
        except Exception as e:
            print(e)


    def return_result_zq_gzmm(self, event):
        try:
            price = self.ui.zq_gzmm_input_3.text()
            num = self.ui.zq_gzmm_input_4.text()
            if self.common_ui.check_calculator_num(price) and self.common_ui.check_calculator_num(num):
                self.ui.ck_result_1.setText(str(float(num) * float(price)))
        except Exception as e:
            print(e)


    def show_zq_gzsy_widget(self):
        try:
            self.common_ui.update_result_lb(["国债利息：", "本息合计：", ""])
            self.common_ui.clear_result(["元", "元", ""])
            self.ui.zq_gzsy_input_1.currentTextChanged.connect(self.gzsy_combox_select)
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_zq_gzsy

            def clear_zq_gzsy(event):
                self.common_ui.clear_result(["元", "元", ""])
                for i in range(2, 4):
                    getattr(self.ui, f"zq_gzsy_input_{i}").setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_zq_gzsy
        except Exception as e:
            print(e)


    def return_result_zq_gzsy(self, event):
        try:
            if self.ui.zq_gzsy_input_1.currentIndex() == 0:
                self.gzsy_time_ = 2
            money = self.ui.zq_gzsy_input_2.text()
            rate = self.ui.zq_gzsy_input_3.text()
            if self.common_ui.check_calculator_num(money) and self.common_ui.check_calculator_num(rate):
                result1 = float(money) * float(rate) / 100 * self.gzsy_time_
                self.ui.ck_result_1.setText(str(result1))
                self.ui.ck_result_2.setText(str(result1 + float(money)))
        except Exception as e:
            print(e)

    def gzsy_combox_select(self, text):
        match (text):
            case "2年期":
                self.gzsy_time_ = 2
            case "3年期":
                self.gzsy_time_ = 3
            case "5年期":
                self.gzsy_time_ = 5
            case "10年期":
                self.gzsy_time_ = 10
    def show_zq_widget(self):
            self.zq_type = ["zqgm", "zqcs", "zqcyq", "gzmm", "gzsy"]
            self.ui.zq_total_widget.show()
            # 绑定左侧列表
            self.ui.calculator_type_listWidget.itemClicked.connect(self.bind_zq_sub_)
            self.show_zq_zqgm_widget()
            self.common_ui.clear_result(["%", "", ""])
            self.common_ui.sub_type_widget_(self.zq_type, self.sub_listWidget_list[0],self.sub_listWidget_list)


    def bind_zq_sub_(self, item):
        item_name = item.text()
        match (item_name):
            case "债券购买收益率计算器":
                self.show_zq_zqgm_widget()
            case "债券出售收益率计算器":
                self.show_zq_zqcs_widget()
            case "债券持有期间收益率计算器":
                self.show_zq_zqcyq_widget()
            case "国债买卖计算器":
                self.show_zq_gzmm_widget()
            case "国债收益计算器":
                self.show_zq_gzsy_widget()
        self.common_ui.sub_type_widget_(self.zq_type, item_name,self.sub_listWidget_list)