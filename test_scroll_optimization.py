#!/usr/bin/env python3
"""
测试滚动优化功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QScrollArea, QLabel
from PyQt6.QtCore import Qt, QTimer

# 导入我们的MarkdownWebView组件
from app.modules.FundAiChat.FundAiChat import MarkdownWebView


class TestScrollOptimization(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("滚动优化测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 创建聊天容器
        self.chat_container = QWidget()
        self.chat_layout = QVBoxLayout(self.chat_container)
        self.chat_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        
        # 添加底部间距widget
        self.bottom_spacer = QWidget()
        self.bottom_spacer.setMinimumHeight(50)
        self.bottom_spacer.setStyleSheet("background-color: rgba(255, 0, 0, 0.1); border: 1px dashed red;")  # 可视化底部间距
        
        self.scroll_area.setWidget(self.chat_container)
        layout.addWidget(self.scroll_area)
        
        # 创建状态标签
        self.status_label = QLabel("滚动状态: 准备就绪")
        layout.addWidget(self.status_label)
        
        # 创建测试按钮
        button_layout = QVBoxLayout()
        
        short_button = QPushButton("添加短内容")
        short_button.clicked.connect(self.add_short_content)
        button_layout.addWidget(short_button)
        
        long_button = QPushButton("添加长内容")
        long_button.clicked.connect(self.add_long_content)
        button_layout.addWidget(long_button)
        
        stream_button = QPushButton("模拟流式输出")
        stream_button.clicked.connect(self.simulate_stream)
        button_layout.addWidget(stream_button)
        
        scroll_test_button = QPushButton("测试强制滚动")
        scroll_test_button.clicked.connect(self.test_force_scroll)
        button_layout.addWidget(scroll_test_button)
        
        clear_button = QPushButton("清空聊天")
        clear_button.clicked.connect(self.clear_chat)
        button_layout.addWidget(clear_button)
        
        layout.addLayout(button_layout)
        
        # 初始化底部间距
        self.chat_layout.addWidget(self.bottom_spacer)
    
    def add_markdown_message(self, content):
        """添加一个Markdown消息"""
        # 移除底部间距
        if self.bottom_spacer.parent():
            self.bottom_spacer.setParent(None)
        
        # 创建容器widget
        message_widget = QWidget()
        message_layout = QVBoxLayout(message_widget)
        message_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建圆角容器
        rounded_container = QWidget()
        rounded_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #EAEDF1;
                border-radius: 10px;
            }
            QWidget:hover {
                border: 1px solid #ccc;
                background-color: #F1F9FE;
            }
        """)
        
        container_layout = QVBoxLayout(rounded_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建MarkdownWebView
        webview = MarkdownWebView()
        webview.setStyleSheet("""
            QWebEngineView {
                border: none;
                background-color: transparent;
            }
        """)
        
        # 设置滚动回调
        webview.scroll_to_bottom_callback = self.scroll_to_bottom
        
        # 设置内容
        webview.update_content(content)
        
        container_layout.addWidget(webview)
        message_layout.addWidget(rounded_container)
        self.chat_layout.addWidget(message_widget)
        
        # 重新添加底部间距
        self.chat_layout.addWidget(self.bottom_spacer)
        
        # 滚动到底部
        self.scroll_to_bottom()
        
        return webview
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        try:
            self.status_label.setText("滚动状态: 正在滚动到底部...")
            
            # 确保最后一个widget可见
            if self.chat_layout.count() > 0:
                last_widget = self.chat_layout.itemAt(self.chat_layout.count() - 1).widget()
                if last_widget:
                    self.scroll_area.ensureWidgetVisible(last_widget)
            
            # 直接设置滚动条到最大值
            scroll_bar = self.scroll_area.verticalScrollBar()
            if scroll_bar:
                def force_scroll():
                    max_value = scroll_bar.maximum()
                    scroll_bar.setValue(max_value)
                    self.scroll_area.update()
                    self.status_label.setText(f"滚动状态: 滚动到 {scroll_bar.value()}/{max_value}")
                
                # 多个时间点执行滚动
                QTimer.singleShot(100, force_scroll)
                QTimer.singleShot(300, force_scroll)
                QTimer.singleShot(700, force_scroll)
                QTimer.singleShot(1000, force_scroll)
                QTimer.singleShot(1500, force_scroll)
            
            # 更新几何信息
            self.chat_container.updateGeometry()
            
        except Exception as e:
            self.status_label.setText(f"滚动错误: {e}")
    
    def add_short_content(self):
        """添加短内容"""
        content = f"""## 短内容测试 #{len([i for i in range(self.chat_layout.count()) if self.chat_layout.itemAt(i).widget() != self.bottom_spacer]) + 1}

这是一个短内容测试，用来验证滚动功能。

- 项目1
- 项目2
- 项目3

**测试完成！**"""
        
        self.add_markdown_message(content)
    
    def add_long_content(self):
        """添加长内容"""
        content = f"""## 长内容测试 #{len([i for i in range(self.chat_layout.count()) if self.chat_layout.itemAt(i).widget() != self.bottom_spacer]) + 1}

这是一个很长的内容，用来测试滚动到底部的功能是否正常工作。

### 详细说明

这个测试包含了大量的文本内容，目的是验证当内容超出可视区域时，滚动功能是否能够正确地将用户带到最底部。

### 代码示例

```python
def test_scroll_function():
    \"\"\"
    测试滚动功能的代码示例
    \"\"\"
    for i in range(10):
        print(f"这是第 {i+1} 行测试内容")
        time.sleep(0.1)
    
    print("滚动测试完成！")
    return True

# 执行测试
result = test_scroll_function()
if result:
    print("✅ 滚动功能正常工作")
else:
    print("❌ 滚动功能存在问题")
```

### 表格数据

| 序号 | 测试项目 | 状态 | 备注 |
|------|----------|------|------|
| 1 | 短内容滚动 | ✅ | 正常 |
| 2 | 长内容滚动 | 🔄 | 测试中 |
| 3 | 流式输出滚动 | ⏳ | 待测试 |
| 4 | 强制滚动 | ⏳ | 待测试 |

### 数学公式

滚动距离计算公式：

$$distance = content\\_height - viewport\\_height$$

其中：
- $content\\_height$ 是内容的总高度
- $viewport\\_height$ 是可视区域的高度

### 更多内容

为了确保这个内容足够长，我们添加更多的文本：

1. 第一段额外内容
2. 第二段额外内容
3. 第三段额外内容
4. 第四段额外内容
5. 第五段额外内容

### 引用块

> 这是一个引用块，用来测试引用内容的显示效果。
> 
> 滚动功能应该能够正确处理各种类型的内容，包括引用块。

### 最终测试

这是内容的最后一部分。如果滚动功能正常工作，用户应该能够看到这段文字的完整内容，包括这个句子的结尾。

**长内容测试完成！如果您能完整看到这行文字，说明滚动功能正常工作。** ✅"""
        
        self.add_markdown_message(content)
    
    def simulate_stream(self):
        """模拟流式输出"""
        # 创建一个新的消息
        webview = self.add_markdown_message("")
        
        # 模拟流式输出内容
        stream_content = [
            "# 流式输出滚动测试\n\n",
            "正在模拟AI回答的流式输出过程",
            "...\n\n",
            "## 第一部分\n\n",
            "这是流式输出的第一部分内容。\n\n",
            "### 代码示例\n\n",
            "```python\n",
            "def stream_output():\n",
            "    for chunk in response_chunks:\n",
            "        yield chunk\n",
            "        time.sleep(0.1)\n",
            "```\n\n",
            "## 第二部分\n\n",
            "继续添加更多内容来测试滚动。\n\n",
            "### 列表内容\n\n",
            "- 流式输出项目1\n",
            "- 流式输出项目2\n",
            "- 流式输出项目3\n\n",
            "## 第三部分\n\n",
            "这是最后一部分内容。\n\n",
            "### 最终结果\n\n",
            "如果您能看到这段文字，说明流式输出的滚动功能正常工作。\n\n",
            "**流式输出测试完成！** 🎉"
        ]
        
        # 模拟逐步添加内容
        self.stream_index = 0
        self.stream_data = stream_content
        self.stream_webview = webview
        self.timer = QTimer()
        self.timer.timeout.connect(self.add_stream_chunk)
        self.timer.start(300)  # 每300ms添加一块内容
    
    def add_stream_chunk(self):
        """添加流式内容块"""
        if self.stream_index < len(self.stream_data):
            self.stream_webview.append_content(self.stream_data[self.stream_index])
            self.stream_index += 1
            self.status_label.setText(f"流式输出: {self.stream_index}/{len(self.stream_data)}")
        else:
            self.timer.stop()
            self.status_label.setText("流式输出完成")
    
    def test_force_scroll(self):
        """测试强制滚动"""
        self.status_label.setText("执行强制滚动测试...")
        self.scroll_to_bottom()
    
    def clear_chat(self):
        """清空聊天记录"""
        for i in reversed(range(self.chat_layout.count())):
            widget = self.chat_layout.itemAt(i).widget()
            if widget and widget != self.bottom_spacer:
                widget.setParent(None)
        
        # 确保底部间距仍然存在
        if not self.bottom_spacer.parent():
            self.chat_layout.addWidget(self.bottom_spacer)
        
        self.status_label.setText("聊天记录已清空")


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestScrollOptimization()
    window.show()
    sys.exit(app.exec())
