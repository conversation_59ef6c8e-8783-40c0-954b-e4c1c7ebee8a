import time

from app.modules.FundDTPH.api.get_dt_data import dt_web


class FundDTPH:
    def __init__(self, ui):
        self.ui = ui
        self.init_setup()

    def init_setup(self):  # 初始化变量
        self.dt_jj_index=0
        self.ui.top_dtph_widget.hide()
        self.dt_web_status = False
        self.ui.dt_web_return.mousePressEvent = self.toggle_dt_index_widget
        self.ui.before_page_lb_dt.mousePressEvent = self.sub_page_dt
        self.ui.next_page_lb_dt.mousePressEvent = self.add_page_dt
        self.ui.tail_page_lb_dt.mousePressEvent = self.tail_page_dt
        self.ui.first_page_lb_dt.mousePressEvent = self.first_page_dt
        self.ui.goto_page_lb_dt.mousePressEvent = self.goto_page_dt
        self.ui.top_img_2.mousePressEvent = self.toggle_dt_index_widget

    def toggle_dt_index_widget(self, event):
        if self.dt_web_status:
            self.dt_web_status = False
            self.ui.index_widget.show()
            self.ui.index_top_widget.show()
            self.ui.index_top2_widget.show()
            self.ui.top_dtph_widget.hide()
        else:
            self.show_dt_widget()
            self.ui.index_top_widget.hide()
            self.ui.index_widget.hide()
            self.ui.index_top2_widget.hide()
            self.dt_web_status = True



    def sub_page_dt(self, event):
        try:
            if self.dt_current_page > 1:
                self.dt_current_page -= 1
                self.load_dt_web(self.dt_jj_index, self.dt_current_page)
                self.update_dt_page()  # 更新dt页按钮
                self.ui.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
        except Exception as e:
            print(f"sub_page_dt  error:{e}")



    def add_page_dt(self, event):
        try:
            if self.dt_current_page < self.dt_total_page:
                self.dt_current_page += 1
                self.load_dt_web(self.dt_jj_index, self.dt_current_page)
                self.update_dt_page()  # 更新dt页按钮
                self.ui.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
        except Exception as e:
            print(f" add_page_dt error:{e}")

            # self.kf


    def tail_page_dt(self, event):
        self.dt_current_page = self.dt_total_page
        self.load_dt_web(self.dt_jj_index, self.dt_total_page)
        self.update_dt_page()
        self.ui.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))


    def first_page_dt(self, event):
        self.dt_current_page = 1
        self.load_dt_web(self.dt_jj_index, 1)
        self.update_dt_page()
        self.ui.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))


    def goto_page_dt(self, event):
        if int(self.ui.goto_Edit_dt.text()) > self.dt_total_page or int(self.ui.goto_Edit_dt) < 0:
            self.ui.goto_Edit_dt.setText("")
        else:
            self.dt_current_page = int(self.ui.goto_Edit_dt.text())
            self.load_dt_web(self.dt_jj_index, self.dt_current_page)
            self.update_dt_page()
            self.ui.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))


    def update_dt_page(self):
        if self.dt_total_page == 1:
            self.ui.goto_widget_2.hide()
        else:
            self.ui.goto_widget_2.show()
        # 对按钮显示判断
        if self.dt_current_page == 1 and self.dt_total_page == 1:
            self.isdisable_dt_page("before", True)
            self.isdisable_dt_page("next", True)
        elif self.dt_current_page == 1 and self.dt_current_page < self.dt_total_page:
            self.isdisable_dt_page("before", True)
            self.isdisable_dt_page("next", False)
        elif 1 < self.dt_current_page < self.dt_total_page:
            self.isdisable_dt_page("before", False)
            self.isdisable_dt_page("next", False)
        elif self.dt_current_page == self.dt_total_page:
            self.isdisable_dt_page("before", False)
            self.isdisable_dt_page("next", True)


    def isdisable_dt_page(self, page_name, status):
        if status:
            getattr(self.ui, f"{page_name}_page_lb_dt").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self.ui, f"{page_name}_page_lb_dt").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
                        color:   #295792}
                      QLabel:hover {
                     color: white;
                    background-color:#295792}
                                    }
                                """)


    def show_dt_widget(self):
        self.dt_current_page = 1
        self.ui.dt_list_Widget.itemClicked.connect(self.on_dtitem_clicked)
        self.ui.top_dtph_widget.show()
        self.load_dt_web(0, 1)
        self.dt_list_name = []
        for i in range(self.ui.dt_list_Widget.count()):
            item = self.ui.dt_list_Widget.item(i)
            self.dt_list_name.append(item.text())  # 打印列表项的文本


    def on_dtitem_clicked(self, item):
        self.dt_jj_index = self.dt_list_name.index(item.text())
        self.load_dt_web(self.dt_jj_index, 1)
        self.dt_current_page = 1


    def load_dt_web(self, index, page):
        self.s = time.time()
        self.ui.dt_loading_lb.setText("加载中...")
        # 创建并启动子线程
        self.worker_thread = dt_web(index=index, page=page)
        self.worker_thread.finished.connect(self.task_finished)
        self.worker_thread.start()


    def task_finished(self, web_data, page1):
        self.dt_total_page = page1
        self.e = time.time()
        self.ui.textBrowser.setHtml(web_data)
        self.ui.tail_page_lb_dt.setText(str(page1))
        self.ui.dt_loading_lb.setText("本次加载共耗时:{:.2f}s".format(self.e - self.s))