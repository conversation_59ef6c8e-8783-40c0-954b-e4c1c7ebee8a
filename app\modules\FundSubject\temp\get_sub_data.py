import json

import requests

headers={
  "Host": "api.fund.eastmoney.com",
  "Connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-site",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://fund.eastmoney.com/",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-C<PERSON>,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=14019251600570; st_asi=delete; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=5; st_psi=20250512154327822-112200312942-0756213057"
}
code="BK000144"
top_url=f"https://api.fund.eastmoney.com/ZTJJ/GetBKDetailInfoNew?callback=jQuery18305820809459464454_1747037513257&tp={code}&_=1747037513269"
table_url=f"https://api.fund.eastmoney.com/ZTJJ/GetBKRelTopicFundNew?callback=jQuery183003420881405741594_1747038772390&sort=undefined&sorttype=DESC&pageindex=1&pagesize=20&tp={code}&isbuy=1&_=1747038772718"
def format_value(value):
    if value=="":
        return "--"
    else:
        return "{:.2f}%".format(float(value))
def format_value_2(value):
    if value=="":
        return "--"
    else:
        return f"{value}%"
    # return value
def get_top_data():
    response = requests.get(top_url, headers=headers,verify=False)
    response_text = json.loads(response.text.strip("jQuery18305820809459464454_1747037513257(").replace(")", ""))

    rank_list=[]

    data=response_text["Data"]
    rate_list = [data["D"], data["W"], data["M"], data["Q"],data["Y"],data["SY"]]
    # rank_list=extend拼接日涨幅排
    rank_list=["--",
               f"{int(data["RANKW"])}/{int(data["WSC"])}",
               f"{int(data["RANKM"])}/{int(data["MSC"])}",
               f"{int(data["RANKQ"])}/{int(data["QSC"])}",
               f"{int(data["RANKY"])}/{int(data["YSC"])}",
               f"{int(data["RANKSY"])}/{int(data["SYSC"])}",]
    return rate_list,rank_list

def get_sub_table_data():
    response = requests.get(table_url, headers=headers,verify=False)
    response_text = json.loads(response.text.strip("jQuery183003420881405741594_1747038772390(").replace(")", ""))
    result_list=[]
    for i in response_text["Data"]:
        t=[
            i["FCODE"],#基金代码
            i["SHORTNAME"],#基金名称
            i["FTYPE"],#基金类型
            i["SYRQ"],#日期
            i["DWJZ"],#最新净值
            format_value_2(i["RZDF"]),#日增长率
            format_value(i["RELATION"]),#持仓占比,
            format_value_2(i["SYL_Z"]),
            format_value_2(i["SYL_Y"]),
            format_value_2(i["SYL_3Y"]),
            format_value_2(i["SYL_6Y"]),
            format_value_2(i["SYL_JN"]),
            format_value_2(i["SYL_1N"]),
            format_value_2(i["SYL_2N"]),
            format_value_2(i["SYL_3N"]),
            format_value_2(i["SYL_LN"]),#成立来

        ]
        result_list.append(t)
    for i in result_list:
        print(i)


print(get_top_data())
# get_sub_table_data()