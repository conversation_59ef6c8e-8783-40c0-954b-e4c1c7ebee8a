import requests
from bs4 import BeautifulSoup
from PyQt6.QtCore import QThread, pyqtSignal

class get_other_article(QThread):
    finished = pyqtSignal(list)
    def __init__(self,type,url_page):
        super().__init__()
        self.time_,self.href_, self.title_ = [], [], []
        self.header = {
            "Referer": "https://fund.eastmoney.com/",
            "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; fullscreengg=1; fullscreengg2=1; st_si=42294786152722; st_asi=delete; _adsame_fullscreen_20308=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=4; st_psi=20250402182348206-112200304021-6445147670; fund_registerAd_1=1",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 Edg/134.0.0.0"
        }
        match (type):
            case "jjgd":
                self.base_url="https://fund.eastmoney.com/a/cjjgd_{}.html".format(url_page)
            case "jjxx":
                self.base_url = "https://fund.eastmoney.com/a/cjjxx_{}.html".format(url_page)
            case "jjyw":
                self.base_url = "https://fund.eastmoney.com/a/cjjyw_{}.html".format(url_page)
            case "tzcl":
                self.base_url="https://fund.eastmoney.com/a/cjjtzcl_{}.html".format(url_page)
            case "smzx":
                self.base_url ="https://fund.eastmoney.com/a/csmjj_{}.html".format(url_page)
    def run(self):
        data=requests.get(self.base_url).text
        soup = BeautifulSoup(data, "html.parser")
        infos_div = soup.find("div", class_="infos")
        page_div = soup.find("div", class_="Page")
        if infos_div:
            li_items = infos_div.find_all("li")
            for li in li_items:
                time = li.find("span").text.strip()
                a_tag = li.find("a")
                href = "https://fund.eastmoney.com/a/" + a_tag["href"]
                title = a_tag["title"]
                self.time_.append(time)
                self.href_.append(href)
                self.title_.append(title)
                print(title)
        if page_div:
            a_tags = page_div.find_all("a")
            page_numbers = []
            for a in a_tags:
                text = a.text.strip()
                if text.isdigit():
                    page_numbers.append(int(text))
            if page_numbers:
                max_page = max(page_numbers)
        self.finished.emit([self.time_, self.href_,self.title_,max_page])
