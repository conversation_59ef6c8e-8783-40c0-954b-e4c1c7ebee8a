import asyncio
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit

from app.modules.FundCalculator.common_ui import common_
from main_w import Ui_MainWindow


class Calculator_ck:
    def __init__(self, ui:Ui_MainWindow,):
        self.ui = ui
        self.common_ui = common_(self.ui)
        self.init_setup()

    def init_setup(self):

        self.img_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"
        self.ui.calculator_type_lb.setText("存款类计算器")
        self.sub_listWidget_list = ["活期储蓄计算器", "整存整取计算器", "零存整取计算器", "整存零取计算器",
                                    "存本取息计算器", "定活两便计算器", "通知存款计算器", "教育储蓄计算器"]




    def bind_ck_sub_(self, item):
        item_name = item.text()
        match (item_name):
            case "活期储蓄计算器":
                self.show_ck_hq_widget()
            case "整存整取计算器":
                self.show_ck_zczq_widget()
            case "零存整取计算器":
                self.show_ck_lczq_widget()
            case "整存零取计算器":
                self.show_ck_zclq_widget()
            case "存本取息计算器":
                self.show_ck_cbqx_widget()
            case "定活两便计算器":
                self.show_ck_dhlb_widget()
            case "通知存款计算器":
                self.show_ck_tzck_widget()
            case "教育储蓄计算器":
                self.show_ck_jycx_widget()
        self.common_ui.sub_type_widget_(self.ck_type, item_name,self.sub_listWidget_list)

    def show_ck_widget(self):
        self.ck_type = ["hq", "zczq", "lczq", "zclq", "cbqx", "dhlb", "tzck", "jycx"]
        self.ui.ck_total_widget.show()
        # 绑定左侧列表
        self.ui.calculator_type_listWidget.itemClicked.connect(self.bind_ck_sub_)
        self.show_ck_hq_widget()
        self.common_ui.clear_result(["元", "元", "元"])
        self.common_ui.sub_type_widget_(self.ck_type, self.sub_listWidget_list[0],self.sub_listWidget_list)

    def on_calculator_cal_clicked(self, input_name: str, date: QDate):
        try:
            input_widget = getattr(self.ui, input_name)
            input_widget.setText(date.toString('yyyy-MM-dd'))
        except Exception as e:
            print(f"Error: {e}")


    def toggle_calculator_cal(self, event, status_name: str, cal_name: str):
        try:
            current_status = getattr(self, status_name)  # 获取当前状态
            if current_status:
                getattr(self.ui, cal_name).hide()  # 隐藏控件
                setattr(self, status_name, False)  # 设置状态为 False
            else:
                getattr(self.ui, cal_name).show()  # 显示控件
                setattr(self, status_name, True)  # 设置状态为 True
        except Exception as e:
            print(e)
    def show_ck_hq_widget(self):
        try:
            self.update_result_lb(["实得本息总额：", "存入本金总额：", "扣除利息税金额："])
            self.ck_hq_cal_1_status = False
            self.ck_hq_cal_2_status = False
            self.common_ui.clear_result(["元", "元", "元"])
            self.ui.ck_hq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_hq_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_hq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_hq_cal_1_status",
                                                      cal_name="ck_hq_calendarWidget_1")
            self.ui.ck_hq_cal2.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_hq_cal_2_status",
                                                      cal_name="ck_hq_calendarWidget_2")
            self.ui.ck_hq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_hq_input_1", date)
            )
            self.ui.ck_hq_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_hq_input_4", date)
            )
            self.common_ui.hide_cal()
            self.ui.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "0.35", ""]
            for i in range(1, 5):
                getattr(self.ui, f"ck_hq_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_hq

            def clear_ck_hq(event):
                self.common_ui.clear_result(["元", "元", "元"])
                self.ui.ck_hq_input_2.setText("")
                self.ui.ck_hq_input_4.setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_hq
        except Exception as e:
            print(e)


    def return_result_ck_hq(self, event):
        try:
            start_date = self.ui.ck_hq_input_1.text()
            money = self.ui.ck_hq_input_2.text()
            rate = self.ui.ck_hq_input_3.text()
            end_date = self.ui.ck_hq_input_4.text()
            if self.common_ui.check_calculator_cal_(start_date, end_date, "1") != False and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                day_num = self.common_ui.check_calculator_cal_(start_date, end_date, "1")
                result1 = day_num * float(rate) / 36500 * float(money) + float(money)
                result2 = float(money)
                result3 = 0
                result = [result1, result2, result3, ]
                for i in range(1, 4):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def update_result_lb(self, result_lb_list: list):
        for i in range(1, len(result_lb_list) + 1):
            getattr(self.ui, f"ck_result_lb_{i}").setText(result_lb_list[i - 1])


    def show_ck_zczq_widget(self):
        try:
            self.zczq_time_d = 3
            self.common_ui.clear_result(["元", "元", ""])
            self.update_result_lb(["到期本息总额:", "扣除利息税金额:", ""])
            self.ck_zczq_cal_1_status = False
            self.ui.ck_zczq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_zczq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_zczq_cal_1_status",
                                                        cal_name="ck_zczq_calendarWidget_1")
            self.ui.ck_zczq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_zczq_input_1", date)
            )
            self.common_ui.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "1.1", ]
            for i in range(1, 4):
                getattr(self.ui, f"ck_zczq_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_zczq

            def clear_ck_hq(event):
                self.common_ui.clear_result(["元", "元", ""])
                self.ui.ck_hq_input_2.setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_hq
        except Exception as e:
            print(e)


    def return_result_ck_zczq(self, event):
        try:
            start_date = self.ui.ck_zczq_input_1.text()
            money = self.ui.ck_zczq_input_2.text()
            rate = self.ui.ck_zczq_input_3.text()
            self.ui.ck_zczq_input_4.currentTextChanged.connect(self.zczq_combox_select)
            if self.common_ui.check_calculator_cal_(start_date, "", 0) and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                result1 = float(rate) / 1200 * self.zczq_time_d * float(money) + float(money)
                result2 = 0
                result = [result1, result2, ]
                for i in range(1, 3):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def zczq_combox_select(self, text):
        match (text):
            case "三个月":
                self.zczq_time_d = 3
            case "半年":
                self.zczq_time_d = 6
            case "一年":
                self.zczq_time_d = 12
            case "二年":
                self.zczq_time_d = 24
            case "三年":
                self.zczq_time_d = 36
            case "五年":
                self.zczq_time_d = 60


    def show_ck_lczq_widget(self):
        try:
            self.lczq_time_d = 12
            self.common_ui.clear_result(["元", "元", ""])
            self.update_result_lb(["到期本息总额:", "扣除利息税金额:", ""])
            self.ck_lczq_cal_1_status = False
            self.ui.ck_lczq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_lczq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_lczq_cal_1_status",
                                                        cal_name="ck_lczq_calendarWidget_1")
            self.ui.ck_lczq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_lczq_input_1", date)
            )
            self.common_ui.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "1.35", ]
            for i in range(1, 4):
                getattr(self.ui, f"ck_lczq_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_lczq

            def clear_ck_lczq(event):
                self.common_ui.clear_result(["元", "元", ""])
                self.ui.ck_lczq_input_2.setText("")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_lczq
        except Exception as e:
            print(e)


    def return_result_ck_lczq(self, event):
        try:
            start_date = self.ui.ck_lczq_input_1.text()
            money = self.ui.ck_lczq_input_2.text()
            rate = self.ui.ck_lczq_input_3.text()
            self.ui.ck_lczq_input_4.currentTextChanged.connect(self.lczq_combox_select)
            if self.common_ui.check_calculator_cal_(start_date, "", 0) and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                annual_rate = float(rate) / 100
                months = self.lczq_time_d
                monthly_amount = float(money)
                monthly_rate = annual_rate / 12  # 月利率
                total_interest = 0
                for month in range(1, months + 1):
                    total_interest += monthly_amount * monthly_rate * (months - month + 1)
                total_amount = monthly_amount * months + total_interest
                result1 = total_amount
                result2 = 0
                result = [result1, result2, ]
                for i in range(1, 3):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def lczq_combox_select(self, text):
        match (text):
            case "一年":
                self.lczq_time_d = 12
            case "三年":
                self.lczq_time_d = 36
            case "五年":
                self.lczq_time_d = 60


    def show_ck_zclq_widget(self):
        try:
            self.zclq_time_d = 12
            self.take_fq = 12
            self.common_ui.clear_result(["元", "元", "元"])
            self.update_result_lb(["每次支取金额:", "所得利息金额:", "扣除利息税金额:"])
            self.ck_zclq_cal_1_status = False
            self.ui.ck_zclq_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_zclq_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_zclq_cal_1_status",
                                                        cal_name="ck_zclq_calendarWidget_1")
            self.ui.ck_zclq_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_zclq_input_1", date)
            )
            self.common_ui.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "2.85", ]
            for i in range(1, 4):
                getattr(self.ui, f"ck_zclq_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_zclq

            def clear_ck_zclq(event):
                self.common_ui.clear_result(["元", "元", "元"])
                self.ui.ck_zclq_input_2.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_zclq

            self.ui.zclq_radioButton_1.setChecked(True)

        except Exception as e:
            print(e)


    def return_result_ck_zclq(self, event):
        try:
            def take_f(n):
                self.take_fq = n

            self.ui.zclq_radioButton_1.toggled.connect(lambda: take_f(12) if self.ui.zclq_radioButton_1.isChecked() else None)
            self.ui.zclq_radioButton_2.toggled.connect(lambda: take_f(4) if self.ui.zclq_radioButton_2.isChecked() else None)
            self.ui.zclq_radioButton_3.toggled.connect(lambda: take_f(2) if self.ui.zclq_radioButton_3.isChecked() else None)
            start_date = self.ui.ck_zclq_input_1.text()
            money = self.ui.ck_zclq_input_2.text()
            rate = self.ui.ck_zclq_input_3.text()
            self.ui.ck_zclq_input_4.currentTextChanged.connect(self.zclq_combox_select)
            if self.common_ui.check_calculator_cal_(start_date, "", 0) and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                """
                   计算分期存款利息
                   :param principal: 总本金
                   :param annual_rate: 年利率
                   :param months: 总月数
                   :param install_per_year: 每年存款次数(12=月,4=季,2=半年)
                   :return: (每次存款金额, 总利息)
                   """
                annual_rate = float(rate) / 100
                installments = self.zclq_time_d * self.take_fq // 12
                deposit_per_install = float(money) / installments
                total_interest = float(money) * annual_rate * (installments + 1) / (2 * self.take_fq)
                result1 = deposit_per_install
                result2 = total_interest
                result = [result1, result2, 0]
                for i in range(1, 4):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def zclq_combox_select(self, text):
        match (text):
            case "一年":
                self.zclq_time_d = 12
            case "三年":
                self.zclq_time_d = 36
            case "五年":
                self.zclq_time_d = 60


    def show_ck_cbqx_widget(self):
        try:
            self.cbqx_time_d = 12
            self.common_ui.clear_result(["元", "元", "元"])
            self.update_result_lb(["每次支取利息金额:", "到期支取本息金额:", "扣除利息税金额:"])
            self.ck_cbqx_cal_1_status = False
            self.ui.ck_cbqx_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_cbqx_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_cbqx_cal_1_status",
                                                        cal_name="ck_cbqx_calendarWidget_1")
            self.ui.ck_cbqx_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_cbqx_input_1", date)
            )
            self.common_ui.hide_cal()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "2.85", ]
            for i in range(1, 4):
                getattr(self.ui, f"ck_cbqx_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_cbqx

            def clear_ck_cbqx(event):
                self.common_ui.clear_result(["元", "元", "元"])
                self.ui.ck_cbqx_input_2.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_cbqx
        except Exception as e:
            print(e)


    def return_result_ck_cbqx(self, event):
        try:
            start_date = self.ui.ck_cbqx_input_1.text()
            money = self.ui.ck_cbqx_input_2.text()
            rate = self.ui.ck_cbqx_input_3.text()
            self.ui.ck_cbqx_input_4.currentTextChanged.connect(self.cbqx_combox_select)
            if self.common_ui.check_calculator_cal_(start_date, "", 0) and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                result1 = float(rate) / 100 / 12 * float(money)
                result2 = float(money) + (result1 * self.cbqx_time_d / 12)
                result = [result1, result2, 0]
                for i in range(1, 4):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def cbqx_combox_select(self, text):
        match (text):
            case "一年":
                self.cbqx_time_d = 12
            case "三年":
                self.cbqx_time_d = 36
            case "五年":
                self.cbqx_time_d = 60


    def show_ck_dhlb_widget(self):
        try:
            self.update_result_lb(["到期本息总额:", "扣除利息税金额:", ""])
            self.ck_dhlb_cal_1_status = False
            self.ck_dhlb_cal_2_status = False
            self.common_ui.clear_result(["元", "元", ""])
            self.ui.ck_dhlb_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_dhlb_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_dhlb_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_dhlb_cal_1_status",
                                                        cal_name="ck_dhlb_calendarWidget_1")
            self.ui.ck_dhlb_cal2.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_dhlb_cal_2_status",
                                                        cal_name="ck_dhlb_calendarWidget_2")
            self.ui.ck_dhlb_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_dhlb_input_1", date)
            )
            self.ui.ck_dhlb_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_dhlb_input_4", date)
            )
            self.common_ui.hide_cal()
            # self.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "0.66", ""]
            for i in range(1, 5):
                getattr(self.ui, f"ck_dhlb_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_dhlb

            def clear_ck_dhlb(event):
                self.common_ui.clear_result(["元", "元", ""])
                self.ui.ck_dhlb_input_2.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_dhlb
        except Exception as e:
            print(e)


    def return_result_ck_dhlb(self, event):
        try:
            start_date = self.ui.ck_dhlb_input_1.text()
            money = self.ui.ck_dhlb_input_2.text()
            rate = self.ui.ck_dhlb_input_3.text()
            end_date = self.ui.ck_dhlb_input_4.text()
            if self.common_ui.check_calculator_cal_(start_date, end_date, "1") != False and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                day_num = self.common_ui.check_calculator_cal_(start_date, end_date, "1")
                result1 = day_num * float(rate) / 36500 * float(money) + float(money)
                result2 = 0
                result = [result1, result2, ]
                for i in range(1, 3):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def show_ck_tzck_widget(self):
        try:
            self.update_result_lb(["到期本息总额:", "扣除利息税金额:", ""])
            self.ck_tzck_cal_1_status = False
            self.ck_tzck_cal_2_status = False
            self.common_ui.clear_result(["元", "元", ""])
            self.ui.ck_tzck_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_tzck_cal2.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_tzck_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_tzck_cal_1_status",
                                                        cal_name="ck_tzck_calendarWidget_1")
            self.ui.ck_tzck_cal2.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_tzck_cal_2_status",
                                                        cal_name="ck_tzck_calendarWidget_2")
            self.ui.ck_tzck_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_tzck_input_1", date)
            )
            self.ui.ck_tzck_calendarWidget_2.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_tzck_input_4", date)
            )
            self.common_ui.hide_cal()
            # self.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "0.8", ""]
            for i in range(1, 5):
                getattr(self.ui, f"ck_tzck_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_tzck

            def clear_ck_tzck(event):
                self.common_ui.clear_result(["元", "元", ""])
                self.ui.ck_tzck_input_2.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_tzck
        except Exception as e:
            print(e)


    def return_result_ck_tzck(self, event):
        try:
            start_date = self.ui.ck_tzck_input_1.text()
            money = self.ui.ck_tzck_input_2.text()
            rate = self.ui.ck_tzck_input_3.text()
            end_date = self.ui.ck_tzck_input_4.text()
            self.ui.ck_tzck_input_5.currentTextChanged.connect(self.tzck_combox_select)
            if self.common_ui.check_calculator_cal_(start_date, end_date, "1") != False and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                day_num = self.common_ui.check_calculator_cal_(start_date, end_date, "1")
                result1 = day_num * float(rate) / 36500 * float(money) + float(money)
                result2 = 0
                result = [result1, result2, ]
                for i in range(1, 3):
                    getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def tzck_combox_select(self, text):
        match (text):
            case "一天通知存款":
                self.ui.ck_tzck_input_3.setText("0.8")
            case "七天通知存款":
                self.ui.ck_tzck_input_3.setText("1.35")


    def show_ck_jycx_widget(self):
        try:
            # region Description
            self.update_result_lb(["到期本息总额:", "", ""])
            self.ck_jycx_cal_1_status = False
            self.jycx_time_d = 12
            self.common_ui.clear_result(["元", "", ""])
            self.ui.ck_jycx_cal1.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(42, 42))
            self.ui.ck_jycx_cal1.mousePressEvent = partial(self.toggle_calculator_cal, status_name="ck_jycx_cal_1_status",
                                                        cal_name="ck_jycx_calendarWidget_1")
            self.ui.ck_jycx_calendarWidget_1.clicked.connect(
                lambda date: self.on_calculator_cal_clicked("ck_jycx_input_1", date)
            )
            self.common_ui.hide_cal()
            # self.ck_hq_widget.show()
            self.ck_inner_list = [datetime.today().strftime("%Y-%m-%d"), "0", "3", ""]
            for i in range(1, 4):
                getattr(self.ui, f"ck_jycx_input_{i}").setText(self.ck_inner_list[i - 1])
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_ck_jycx

            def clear_ck_jycx(event):
                self.common_ui.clear_result(["元", "", ""])
                self.ui.ck_jycx_input_2.setText("0")

            self.ui.calculator_reset_btn.mousePressEvent = clear_ck_jycx
            # endregion
        except Exception as e:
            print(e)


    def return_result_ck_jycx(self, event):
        try:
            start_date = self.ui.ck_jycx_input_1.text()
            money = self.ui.ck_jycx_input_2.text()
            rate = self.ui.ck_jycx_input_3.text()
            self.ui.ck_jycx_input_4.currentTextChanged.connect(self.jycx_combox_select)
            if self.common_ui.check_calculator_cal_(start_date, "", "0")  and self.common_ui.check_calculator_num(
                    money) and self.common_ui.check_calculator_num(rate):
                annual_rate = float(rate) / 100
                months = self.jycx_time_d
                monthly_amount = float(money)
                monthly_rate = annual_rate / 12  # 月利率
                total_interest = 0
                for month in range(1, months + 1):
                    total_interest += monthly_amount * monthly_rate * (months - month + 1)
                total_amount = monthly_amount * months + total_interest
                result1 = total_amount
                result = [result1, ]
                for i in range(1, 2): getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)


    def jycx_combox_select(self, text):
        match (text):
            case "一年期":
                self.ui.ck_jycx_input_3.setText("3")
                self.jycx_time_d = 12
            case "三年期":
                self.ui.ck_jycx_input_3.setText("4.25")
                self.jycx_time_d = 36
            case "六年期":
                self.ui.ck_jycx_input_3.setText("4.75")
                self.jycx_time_d = 72