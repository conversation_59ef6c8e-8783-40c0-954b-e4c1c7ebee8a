import re

import requests
# url="https://finance.eastmoney.com/a/202503223353229630.html"
# data=requests.get(url)
# with open("temp_article","w",encoding="utf8")as f:
#     f.write(data.text)
with open("temp_article","r",encoding="utf8")as f:
    data=f.read()

from lxml import etree
h=etree.HTML(data)
# l=h.xpath("..//div[@class='txtinfos']/p/text()|..//div[@class='txtinfos']/p/img/text()")
# # print(h.xpath("/div/p/text()"))
# for i in l:
#     print(i)

# 正则表达式
pattern = r'<!--文章主体-->(.*?)<!-- 文尾部其它信息 -->'
# 匹配内容
match = re.search(pattern, data, re.DOTALL)

if match:
    content = match.group(1).strip()  # 获取匹配的内容并去掉首尾空白
    print("匹配到的内容：")
    print(content.replace('<a  target="_blank" href="https://acttg.eastmoney.com/pub/xxlout_khmbdfcfappnew_random_0007467a"><img src="https://np-newspic.dfcfw.com/download/D25411840164398846730_w690h389.jpg" class="em_handle_adv_close" /></a>',""))
else:
    print("未找到匹配的内容")
