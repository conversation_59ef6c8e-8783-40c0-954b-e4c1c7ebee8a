import matplotlib.pyplot as plt
import time
# 设置中文字体
s=time.time()
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams['axes.unicode_minus'] = False
plt.figure(figsize=(8, 5))
plt.grid(axis='y',color='#E9E9E9',linestyle='--', alpha=0.5, zorder=0)
# 数据
categories = ['衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', 'niu','衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋', '袜子','niu','衬衫', '羊毛衫', '雪纺衫', '裤子', '高跟鞋']
values = [5, 20, 36, 10, 75, 90,6, 20, 36, 10, 75, 90,6, 20, 36, 10, 75, 90]
categories = [ '\n'.join(list(category)) for category in categories ]

# 创建柱状图
# 创建柱状图
plt.bar(categories, values, color='red', zorder=3,label="主力净流入")  # 柱状颜色为红色，边框为黑色

# 取消标题和x轴标签
plt.title('')  # 取消标题
plt.xlabel('')  # 取消x轴标签
plt.ylabel('销售额（万元）')

# 添加数据标签（文字颜色为红色）
for i, value in enumerate(values):
    plt.text(i, value + 1, str(value)+"亿", ha='center', va='bottom', color='red')

# 设置图表边框
for spine in plt.gca().spines.values():
    spine.set_edgecolor('black')  # 设置边框为黑色
plt.legend(loc='upper left')
# 取消刻度
plt.xticks([])  # 取消x轴刻度
# plt.yticks([])  # 取消y轴刻度

# 设置x轴数据纵向排列，颜色为蓝色
plt.xticks(range(len(categories)), categories, color='#072F92')
# 设置图表边框
ax = plt.gca()
ax.spines['top'].set_visible(False)    # 隐藏上框线
ax.spines['right'].set_visible(False)  # 隐藏右框线
ax.spines['left'].set_edgecolor('black')   # 左框线为黑色
ax.spines['bottom'].set_edgecolor('black') # 下框线为黑色

# 保存为图片
plt.savefig('bar_chart.png', dpi=300, bbox_inches='tight')
e=time.time()
print(e-s)
# 显示图表
plt.show()