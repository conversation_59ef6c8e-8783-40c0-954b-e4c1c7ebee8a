def return_zs_info_color( start: str, data: list):
    # 最高，最低，涨跌幅，涨跌额
    start = eval(start)
    result = []
    s=[]
    for i in data:
        s.append(float(i.strip("%")))
    result = compare_zs(s[0], start, result)
    result = compare_zs(s[1], start, result)
    result = compare_zs(s[2], 0, result)
    result = compare_zs(s[3], 0, result)
    return result


# @staticmethod
def compare_zs( a, b, arr: list):
    if a > b:
        arr.append(1)
    elif a == b:
        arr.append(0)
    else:
        arr.append(-1)
    return arr

print(return_zs_info_color("21092.1",['21138.72', '20893.29', '-0.45%', '-94.95',]))