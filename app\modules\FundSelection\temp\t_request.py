import json

import requests

url = "https://api.fund.eastmoney.com/favor/GetFundsInfo"
url_zfph = "https://api.fund.eastmoney.com/favor/ranknew"
params = {
    "fcodes":"000001,002230,000331,000655,161725,011103,005669,166301,012768,320007,015150",

}
# params ={'fcodes': '013014,005555,009256,011111,000013,008888,000033,005358,000003,001790,000009,008898'}
Headers = {
        "Host": "api.fund.eastmoney.com",
        "Connection": "keep-alive",
        "Content-Length": "103",
        "sec-ch-ua-platform": "\"Windows\"",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "Accept": "application/json, text/javascript, */*; q=0.01",
        "sec-ch-ua": "\"Microsoft Edge\";v=\"135\", \"Not-A.Brand\";v=\"8\", \"Chromium\";v=\"135\"",
        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
        "sec-ch-ua-mobile": "?0",
        "Origin": "https://favor.fund.eastmoney.com",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "cors",
        "Sec-Fetch-Dest": "empty",
        "Referer": "https://favor.fund.eastmoney.com/",
        "Accept-Encoding": "gzip, deflate, br, zstd",
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",

        "Cookie":"qgqp_b_id=50d74afee65419b05e9120f0df53c69f; IsHaveToNewFavor=0; AUTH_FUND.EASTMONEY.COM_GSJZ=AUTH*TTJJ*TOKEN; HAList=ty-90-BK0481-%u6C7D%u8F66%u96F6%u90E8%u4EF6%2Cty-0-159876-%u6709%u8272%u9F99%u5934ETF%2Cty-90-BK1037-%u6D88%u8D39%u7535%u5B50%2Cty-1-600489-%u4E2D%u91D1%u9EC4%u91D1; st_si=81646649172854; st_asi=delete; ap_0_13a7d068=1; ap_1_68c1f65b=1; EMFUND0=04-29%2022%3A38%3A12@%23%24%u5E7F%u53D1%u5148%u8FDB%u5236%u9020%u80A1%u7968%u53D1%u8D77%u5F0FC@%23%24014192; EMFUND1=04-30%2016%3A01%3A06@%23%24%u6052%u8D8A%u5320%u5FC3%u4F18%u9009%u4E00%u5E74%u6301%u6709%u6DF7%u5408A@%23%24015150; EMFUND2=04-30%2015%3A26%3A00@%23%24%u8BFA%u5B89%u6052%u946B%u6DF7%u5408@%23%24006429; EMFUND3=04-30%2015%3A26%3A01@%23%24%u5357%u65B9%u745E%u7965%u4E00%u5E74%u6DF7%u5408A@%23%24005810; EMFUND4=04-30%2013%3A09%3A09@%23%24%u4E07%u5BB6%u5168%u7403%u6210%u957F%u4E00%u5E74%u6301%u6709%u671F%u6DF7%u5408%28QDII%29A@%23%24012535; EMFUND5=04-30%2015%3A35%3A11@%23%24%u6052%u8D8A%u5320%u5FC3%u4F18%u9009%u4E00%u5E74%u6301%u6709%u6DF7%u5408C@%23%24015151; EMFUND6=04-30%2015%3A37%3A09@%23%24%u534E%u5B9D%u6709%u8272%u91D1%u5C5EETF@%23%24159876; EMFUND7=04-30%2016%3A17%3A46@%23%24%u5E7F%u53D1%u6539%u9769%u6DF7%u5408@%23%24001468; EMFUND8=05-01%2018%3A15%3A30@%23%24%u534E%u590F%u6210%u957F%u6DF7%u5408@%23%24000001; EMFUND9=05-01 18:16:04@#$%u5BCC%u8363%u533B%u836F%u5065%u5EB7%u6DF7%u5408%u53D1%u8D77C@%23%24015656; Eastmoney_Fund=000001_002230_000331_000655_161725_011103_005669_166301_012768_320007_015150; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=44; st_psi=20250501200212824-119146300572-4993512930"
    }
def format_data(data):
        return f"{data}%"


def format_data_2(data):
    if data=="":
        return "--"
    return f"{data}%"

def get_data_zjlb():
        response = requests.post(url, headers=Headers,data=params,verify=False)
        res_l=json.loads(response.text)["Data"]["KFS"]
        print(res_l)
        ll=[]
        for i in res_l:
                l=[
                        i["FCODE"],#代码
                        i["SHORTNAME"],#名称
                        i["FTYPE"],#类型
                        i["FSRQ"],#最新日期
                        i["DWJZ"],#单位日期
                        i["LJJZ"],#累计净值
                        i["RZDE"],#日增长值
                        format_data(i["RZDF"]),#日增长率
                        format_data(i["SYL_LN"])#成立来
                ]
                ll.append(l)
        print(ll[0])


def get_data_zfph():
        params_ = {
                "fcodes": "019005,160216,005218,000001,002230,000655,161725,011103,005669,166301,012768,320007,015150",
                # "gid": "-1"
        }
        response = requests.post(url_zfph, headers=Headers, data=params_,verify=False)
        res_l = json.loads(response.text)["Data"]
        res_ll=[]
        for i in res_l:
            l=[
                i["FCODE"],#代码
                i["SHORTNAME"],#名称
                i["PDATE"],#日期
                #添加自选日期，
                #添加以来收益率
                format_data_2(i["SYL_Z"]),#近一周
                format_data_2(i["SYL_Y"]),#近一月
                format_data_2(i["SYL_3Y"]),#近三月
                format_data_2(i["SYL_6Y"]),#近六月
                format_data_2(i["SYL_JN"]),#今年来
                format_data_2(i["SYL_1N"]),#近一年
                format_data_2(i["SYL_2N"]),#近两年
                format_data_2(i["SYL_3N"]),#近三年
                format_data_2(i["SYL_5N"]),#近五年
            ]
            res_ll.append(l)
        print(res_ll[2])
get_data_zjlb()
# get_data_zfph()

