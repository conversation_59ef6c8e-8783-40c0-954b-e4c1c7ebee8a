import concurrent.futures
import json
from concurrent.futures import Thread<PERSON>oolExecutor

from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy
class get_compare_data(QThread):
    finished = pyqtSignal(list,list,list,list,)
    def __init__(self,code_list,graph_seq):
        super().__init__()
        self.header={
              "Host": "api.fund.eastmoney.com",
              "Connection": "keep-alive",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "sec-ch-ua-mobile": "?0",
              "Accept": "*/*",
              "Sec-Fetch-Site": "same-site",
              "Sec-Fetch-Mode": "no-cors",
              "Sec-Fetch-Dest": "script",
              "Referer": "https://fund.eastmoney.com/",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=75994445429365; st_asi=delete; ap_0_13a7d068=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=9; st_psi=20250421150713164-112200312939-5598177333"
            }
        self.code_list = code_list
        self.result_list_JBXX = []
        self.result_list_YJPJ = []
        self.result_list_ZCPZ = []
        self.result_list_GRAPH = []

        self.code_param=",".join(map(str, code_list))

        self.code_name = []
        self.len_result=len(self.code_list)
        self.code_name_list=[]
        self.graph_seq_list=["week","month","threemonth","sixmonth","year","twoyear","threeyear"]
        self.JBXXurl=f"https://api.fund.eastmoney.com/FundCompare/JBXX?bzdm={self.code_param}&callback=jQuery183022900414814736725_1745219233201&_=1745219233210"
        self.YJPJurl = f"https://api.fund.eastmoney.com/FundCompare/YJPJBJ?bzdm={self.code_param}&callback=jQuery18307263176541602768_1745220869410&_=1745220869585"
        self.ZCPZurl = f"https://api.fund.eastmoney.com/FundCompare/ZCPZ?bzdm={self.code_param}&callback=jQuery18307263176541602768_1745220869411&_=1745220869420"
        self.LJSYLurl = f"https://api.fund.eastmoney.com/FundCompare/LJSYL?bzdm={self.code_param }&c={self.graph_seq_list[graph_seq]}&callback=jQuery183004562079219496318_1745496457601&_=1745496457723"
        self.TLPMurl=f"https://api.fund.eastmoney.com/FundCompare/TLPM?bzdm={self.code_param }&c={self.graph_seq_list[graph_seq]}&callback=jQuery183026223942108755394_1745583485471&utf-8&_=1745583497886"

    def data_format(self, s):
        if s == "":
            return "--"
        else:
            return "{}%".format(s)

    def grade_str(self,s):
        if s == "":
            return "暂无评级"
        else:
            return s

    def re_max(self, min_v, max_v):
        stepSize = (max_v - min_v) / 3
        min1, max1 = min_v - stepSize, max_v + stepSize
        sub1 = max_v - min_v
        if 0<sub1 <= 2.5:
            min1, max1 = round(min1, 1), round(max1, 1)
        elif 2.5<sub1 <= 20000:
            min1, max1 = int(min1), int(max1)
        return min1, max1

    def re_max_int(self, min_v, max_v):
        stepSize = (max_v - min_v) / 3
        return int(min_v - stepSize), int(max_v + stepSize)

    def return_JBXX_data(self):
        try:
            response = requests.get(self.JBXXurl, headers=self.header, verify=False, )
            response_text = eval(json.loads(
            response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")", ""))["Data"])
            for i in response_text:
                t = i.split(",")
                self.result_list_JBXX.append([
                    t[0],  # 代码
                    t[1],  # 基金名称
                    # t[2],  # 基金简称
                    t[3],  # 基金类型
                    t[7],  # 日期
                    t[6],  # 最新单位净值
                    t[8],  # 累计净值
                    self.data_format(t[9]),  # 日增长率
                    t[10],  # 上期单位净值
                    t[12],  # 基金经理
                    t[14],  # 基金公司
                ])
                z=[t[0],t[1]]
                self.code_name.append(z)
                self.code_name_list.append(t[1])
        except Exception as e:
            print(e)

    def data_format_2(self,data_list,start,end):
        t = [
            "--" if item == "" and start<= i < end+1  # 对第 5-15 个元素，空字符串替换为 "-"
            else item  # 其他元素保持不变
            for i, item in enumerate(data_list)
        ]
        return t

    def return_YJPJ_data(self):
        jdsy_data_list = []
        lsndsy_data_list = copy.deepcopy(self.code_name)
        dtsy_data_list = copy.deepcopy(self.code_name)
        jjpj_data_list = copy.deepcopy(self.code_name)
        response = requests.get(self.YJPJurl, headers=self.header, verify=False, )
        response_text = json.loads(response.text.strip("jQuery18307263176541602768_1745220869410(").replace(")", ""))["Data"]
        t_jdsy = json.loads(response_text)["jdsy"]
        t_lsndsy = json.loads(response_text)["lsndsy"]
        t_dtsy = json.loads(response_text)["dtsy"]
        t_jjpj = json.loads(response_text)["jjpj"]

        for i in range(len(t_jdsy)):
            jdsy_data_list.append(self.data_format_2(t_jdsy[i].split(","),3,12))
            lsndsy_data_list[i].extend(self.data_format_2(list(t_lsndsy[i].values()), 0, 4))
            dtsy_data_list[i].extend(self.data_format_2(t_dtsy[i].split(","), 0, 3))
            jjpj_data_list[i].extend(self.data_format_2([self.grade_str(j) for j in t_jjpj[i].split(",")], 0, 4))
        self.result_list_YJPJ = [jdsy_data_list,lsndsy_data_list,dtsy_data_list,jjpj_data_list]

    def return_ZCPZ_data(self):
        zcpz_data_list = []
        hypz_data_list = copy.deepcopy(self.code_name)
        gpcc_data_list = copy.deepcopy(self.code_name)
        zqcc_data_list = copy.deepcopy(self.code_name)
        response = requests.get(self.ZCPZurl, headers=self.header, verify=False, )
        response_text = json.loads(response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")", ""))["Data"]
        t_zcpz = json.loads(response_text)["zcpz"]
        t_hypz = json.loads(response_text)["hypz"]
        t_gpcc = json.loads(response_text)["gpcc"]
        t_zqcc = json.loads(response_text)["zqcc"]
        for i in t_zcpz:
            t = i.split(",")
            zcpz_data_list.append(self.data_format_2(t,2,11))
        for i in range(len(t_hypz)):
            if t_hypz[i]:
                t = t_hypz[i].split(",")
            else:
                t = ["" for i in range(10)]
            hypz_data_list[i].extend(self.data_format_2(t,0,9))
        for i in range(len(t_gpcc)):
            if t_gpcc[i]:
                t = []
                l=len(t_gpcc[i])
                sub=10-l
                for j in t_gpcc[i]:
                    k = j.split(",")
                    z = f"{k[1]}\n{k[2]}"
                    t.append(z)
                for h in range(sub):
                    t.append("")
            else:
                t = ["" for i in range(10)]
            gpcc_data_list[i].extend(self.data_format_2(t,0,9))
        for i in range(len(t_zqcc)):
            print(t_zqcc[i])
            if t_zqcc[i]:
                t = []
                l = len(t_zqcc[i])
                sub = 5 - l
                for j in t_zqcc[i]:
                    k = j.split(",")
                    z = f"{k[0]}\n{k[1]}"
                    t.append(z)
                for h in range(sub):
                    t.append("")
            else:
                t = ["" for i in range(5)]
            zqcc_data_list[i].extend(self.data_format_2(t,0,4))
        self.result_list_ZCPZ=[zcpz_data_list,hypz_data_list,gpcc_data_list,zqcc_data_list]

    def return_graph_data(self):
        try:
            chart_config_1, line_data_tm_1, start_date, end_date = self._get_chart_config(self.LJSYLurl, "LJSYL")
            chart_config_2, line_data_tm_2, start_date, end_date = self._get_chart_config(self.TLPMurl, "TLPM")
            html_content_LJSYL = self._generate_html(chart_config_1, line_data_tm_1, start_date, end_date, "template")
            html_content_TLPM = self._generate_html(chart_config_2, line_data_tm_2, start_date, end_date,"template_tlpm")
            self.result_list_GRAPH = [html_content_LJSYL, html_content_TLPM]
        except Exception as e:
            print(f"Error in return_graph_data: {e}")

    def graph_config_data(self, min_value, max_value, date_list, code_name_list, unit: str, all_data_list):

        # 配置参数
        chart_config = {
            "y_min": min_value,  # Y轴最小值（带%）
            "y_max": max_value,  # Y轴最大值（带%）
            "show_x_labels": False,
            "x_labels": date_list,
            "legends": code_name_list,
            "unit": unit  # 数据单位
        }

        line_data_tm = [
            {"color": "#FF6384", "points": all_data_list[0]},

            {"color": "#36A2EB", "points": all_data_list[1]},

            {"color": "#FFCE56", "points": all_data_list[2]},

            {"color": "#4BC0C0", "points": all_data_list[3]},

            {"color": "#9966FF", "points": all_data_list[4]},

            {"color": "#FF9F40", "points": all_data_list[5]},

            {"color": "#8AC24A", "points": all_data_list[6]},

            {"color": "#F06292", "points": all_data_list[7]},

            {"color": "#7986CB", "points": all_data_list[8]},

            {"color": "#E53935", "points": all_data_list[9]}
        ]

        return chart_config, line_data_tm

    def _get_chart_config(self, url, status):
        try:
            response = requests.get(url, headers=self.header, verify=False)
            response_text = json.loads(
                response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")", ""))
            date_list, all_data_list = self._parse_response_data(response_text)
            min_value, max_value = self._calculate_min_max(all_data_list, status)
            chart_config, line_data_tm = self.graph_config_data(min_value, max_value, date_list, self.code_name_list,
                                                                "%" if status == "LJSYL" else "", all_data_list)
            return chart_config, line_data_tm[:self.len_result], date_list[0], date_list[-1]
        except Exception as e:
            print(f"Error in _get_chart_config: {e}")
            raise

    def _parse_response_data(self, response_text):
        date_list = []
        all_data_list = [[] for _ in range(10)]
        for i in eval(response_text["Data"])["dataProvider"]:
            date_list.append(i["date"])
            for j in range(self.len_result):
                all_data_list[j].append(i[self.code_list[j]])
        return date_list, all_data_list

    def _calculate_min_max(self, all_data_list, status):
        max_ll = [max([float("1" if item == "undefined" else item) for item in i]) for i in all_data_list if i]
        min_ll = [min([float("1" if item == "undefined" else item) for item in i]) for i in all_data_list if i]
        return self.re_max(min(min_ll), max(max_ll)) if status == "LJSYL" else self.re_max_int(min(min_ll), max(max_ll))

    def _generate_html(self, chart_config, line_data, start_date, end_date, file_path):
        html_file = QFile(rf"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundCompare\src\{file_path}.html")
        if html_file.open(QIODevice.OpenModeFlag.ReadOnly | QIODevice.OpenModeFlag.Text):
            html_content = html_file.readAll().data().decode('utf-8')
            html_file.close()
            return html_content.replace(
                '/*CONFIG_PLACEHOLDER*/',
                f'const chartConfig = {json.dumps(chart_config)};'
            ).replace(
                '/*DATA_PLACEHOLDER*/',
                f'const lineData = {json.dumps(line_data)};'
            ).replace("start_date", start_date).replace("end_date", end_date)
        return ""
    def run(self):
        try:
            # 使用 ThreadPoolExecutor 并行执行数据获取任务
            self.return_JBXX_data()
            with ThreadPoolExecutor(max_workers=4) as executor:
                # 提交任务到线程池
                future_yjpj = executor.submit(self.return_YJPJ_data)
                future_zcpz = executor.submit(self.return_ZCPZ_data)
                future_graph = executor.submit(self.return_graph_data)

                # 等待所有任务完成
                concurrent.futures.wait([ future_yjpj, future_zcpz, future_graph])

            # 所有任务完成后，发送信号
            self.finished.emit(self.result_list_JBXX, self.result_list_YJPJ, self.result_list_ZCPZ, self.result_list_GRAPH)
        except Exception as e:
            print(f"Error in run method: {e}")




