import concurrent.futures
import json
from concurrent.futures import ThreadPoolExecutor

from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy

class get_filter_fund_data(QThread):
    finished = pyqtSignal(list,list)
    def __init__(self,ft_data,cp_data,rs_data,tp_data,rt_data,se_data,nx_data,page):
        super().__init__()
        self.headers={
              "Host": "fund.eastmoney.com",
              "Connection": "keep-alive",
              "sec-ch-ua-platform": "\"Windows\"",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
              "sec-ch-ua-mobile": "?0",
              "Accept": "*/*",
              "Sec-Fetch-Site": "same-origin",
              "Sec-Fetch-Mode": "no-cors",
              "Sec-Fetch-Dest": "script",
              "Referer": "https://fund.eastmoney.com/daogou/",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; IsHaveToNewFavor=0; kforders=0%3B-1%3B%3B%3B0%2C2%2C18%2C19%2C22%2C23%2C21%2C16; AUTH_FUND.EASTMONEY.COM_GSJZ=AUTH*TTJJ*TOKEN; HAList=ty-90-BK0481-%u6C7D%u8F66%u96F6%u90E8%u4EF6%2Cty-0-159876-%u6709%u8272%u9F99%u5934ETF%2Cty-90-BK1037-%u6D88%u8D39%u7535%u5B50%2Cty-1-600489-%u4E2D%u91D1%u9EC4%u91D1; searchbar_code=005358_009256_011111_005555_008888_005810_159876_001468_180103_180105; Eastmoney_Fund=000818_019005_006329_006536_009256_000001_006753_000331_006329; EMFUND0=05-01%2018%3A16%3A04@%23%24%u5BCC%u8363%u533B%u836F%u5065%u5EB7%u6DF7%u5408%u53D1%u8D77C@%23%24015656; EMFUND1=05-01%2020%3A34%3A46@%23%24%u534E%u590F%u548C%u8FBE%u9AD8%u79D1REIT@%23%24180103; EMFUND2=05-03%2017%3A50%3A11@%23%24%u6613%u65B9%u8FBE%u5E7F%u5F00%u4EA7%u56EDREIT@%23%24180105; EMFUND3=05-02%2013%3A24%3A17@%23%24%u524D%u6D77%u5F00%u6E90%u516C%u7528%u4E8B%u4E1A%u80A1%u7968@%23%24005669; EMFUND4=05-02%2013%3A24%3A15@%23%24%u56FD%u6CF0%u5927%u5B97%u5546%u54C1@%23%24160216; EMFUND5=05-02%2013%3A24%3A15@%23%24%u56FD%u6295%u745E%u94F6%u767D%u94F6%u671F%u8D27%28LOF%29C@%23%24019005; EMFUND6=05-03%2017%3A50%3A02@%23%24%u9E4F%u534E%u78B3%u4E2D%u548C%u4E3B%u9898%u6DF7%u5408C@%23%24016531; EMFUND7=05-03%2017%3A50%3A01@%23%24%u5E7F%u53D1%u4E2D%u503A7-10%u5E74%u56FD%u5F00%u503A%u6307%u6570E@%23%24011062; EMFUND8=05-03%2018%3A50%3A03@%23%24%u56FD%u5BCC%u5168%u7403%u79D1%u6280%u4E92%u8054%u6DF7%u5408%28QDII%29%u4EBA%u6C11%u5E01A@%23%24006373; EMFUND9=05-05 14:59:23@#$%u540C%u6CF0%u4EA7%u4E1A%u5347%u7EA7%u6DF7%u5408C@%23%24014939; st_si=14886929970387; st_asi=delete; ap_0_13a7d068=1; _adsame_fullscreen_20308=1; ASP.NET_SessionId=r1wbcrv1wiuqn3nenvrrebls; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=5; st_psi=20250507184122727-112200312940-4004409762"
            }
        self.base_url=f"https://fund.eastmoney.com/data/FundGuideapi.aspx?dt=4&ft={ft_data}&rs={rs_data}&sd=&ed=&cp={cp_data}&rt={rt_data}&tp={tp_data}&se={se_data}&nx={nx_data}&sc=1n&st=desc&pi={page}&pn=20&zf=diy&sh=list&rnd=0.728108826299158"#base

    def format_data(self, data):
        if data:
            return f"{data}%"
        return "--"

    def return_filter_data(self,):
        try:
            response = requests.get(self.base_url, headers=self.headers, verify=False)
            data_r = json.loads(response.text.strip("var rankData ="))["datas"]
            data_all = json.loads(response.text.strip("var rankData ="))
            allPages = data_all["allPages"]
            datacount = data_all["datacount"]
            other_data = [allPages, datacount]
            data_list = []
            for i in data_r:
                i = i.split(",")
                t = [
                    i[0],  # 基金代码,
                    i[1],  # 基金名称,
                    i[2],  # 基金简拼音,
                    i[3],  # 基金类型,
                    i[-10],  # 日期
                    i[-9],  # 基金净值,
                    self.format_data(i[-8]),  # 日增长率,
                    self.format_data(i[5]),  # 近1周,
                    self.format_data(i[6]),  # 近1月,
                    self.format_data(i[7]),  # 近3月,
                    self.format_data(i[8]),  # 近6月,
                    self.format_data(i[4]),  # 今年以来,
                    self.format_data(i[9]),  # 近1年,
                    self.format_data(i[10]),  # 近2年,
                    self.format_data(i[11]),  # 近3年,
                    self.format_data(i[-1]),  # 成立来
                ]
                data_list.append(t)
            return data_list,other_data
        except Exception as e:
            print(f"return_filter_data error:{e}")


    def run(self):
        table_data,other_data=self.return_filter_data()
        self.finished.emit(table_data,other_data)


