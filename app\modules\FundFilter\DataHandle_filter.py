import json

from pypinyin import pinyin, Style

from app.common.api.code_company import FundCompany


class DataHandle_filter():
    def __init__(self):
        self.bk_file=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundFilter\src\bk.txt"

        # self.fund_company = FundCompany_()

    def return_company_letter_dict(self):
        company_dict = FundCompany.get_company_dict_capital()
        return company_dict

    def get_pinyin_initial(self,name):
        """获取拼音首字母并转为大写"""
        initials = pinyin(name, style=Style.FIRST_LETTER)
        return str(initials[0][0]).upper()

    def return_fund_NameToCode(self,  fund_name_list):
        try:

            res=[]
            fund_dict = FundCompany.company_code_name_dict
            for i in fund_name_list:
                res.append(fund_dict[i])
            return res
        except Exception as e:
            print(f"error:{e}")

    #获取板块数据
    def get_bk_list(self,):
        with open(self.bk_file, "r", encoding="utf-8")as f:
            data=f.read()
        bk_dict={}
        data=json.loads(data)["Data"]
        for i in data:
            for j in data[i]:
                bk_dict[j["INDEXNAME"]]=j["INDEXCODE"]
        return bk_dict

    def return_bk_code_list(self,name_list):
        bk_dict=self.get_bk_list()
        res=[]
        for i in name_list:
            res.append(bk_dict[i])
        return res

    #返回所有筛选条件的数据label
    def return_all_filter_data(self,orign_text,new_data,type):
        all_filter_dict = {"类型":[],"公司":[],"业绩":[[],[]],"板块":[],"评级":[[],[]],"规模":"","年限":""}
        yj_type=["近1周","近1月","近3月","近6月","今年以来","近1年","近2年","近3年"]#传入索引
        pj_type = ["晨星评级", "上证评级", "招商评级", "济安评级"]

        # 将原始填充入字典
        old_dict = {}
        if orign_text!="":
            filter_list=orign_text.split("  |  ")

            yj_key_l=[]
            yj_value_l=[]
            pj_key_l=[]
            pj_value_l=[]
            for i in filter_list:
                if i.split(":")[0] in yj_type:
                    yj_key_l.append(i.split(":")[0])
                    yj_value_l.append(i.split(":")[1])
                elif i.split(":")[0] in pj_type:
                    pj_key_l.append(i.split(":")[0])
                    pj_value_l.append(i.split(":")[1])
                else:
                    key = i.split(":")[0]
                    value = i.split(":")[1]
                    old_dict[key]=value
            old_dict[",".join(yj_key_l)]=yj_value_l
            old_dict[",".join(pj_key_l)]=pj_value_l
            print(old_dict)
        # else:
        #     old_dict={}

        #将字典匹配到结果模板字典
        for key in list(old_dict.keys()):
            if "近" in key:
                all_filter_dict["业绩"] = [key.split(","),old_dict[key]]
            elif "评级" in key:
                all_filter_dict["评级"] = [key.split(","), old_dict[key]]
            else:
                all_filter_dict[key]=old_dict[key]
        print(all_filter_dict)

        #更新指定类型数据
        if type in ["业绩","评级","规模","年限"]:
            all_filter_dict[type] = new_data
        elif type in ["类型","公司","板块"]:
            if not new_data:
                all_filter_dict[type]=[]
            else:
                all_filter_dict[type] =",".join(new_data)

        # 返回文本数据
        new_text=""
        for i in list(all_filter_dict.keys()):
            if i in ["业绩","评级"] and all_filter_dict[i]!=[[],[]]:
                key_l=all_filter_dict[i][0]
                value_l=all_filter_dict[i][1]
                for j in range(len(key_l)):
                    new_text+=f"{key_l[j]}:{value_l[j]}  |  "
            elif i in ["类型","公司","板块",] and all_filter_dict[i]!=[]:
                new_text+=f"{i}:{all_filter_dict[i]}  |  "
            elif i in ["规模","年限"] and all_filter_dict[i]!="":
                new_text+=f"{i}:{all_filter_dict[i]}  |  "
        print(new_text)
        return new_text.rstrip("  |  ")

    @classmethod
    def trans_url_data(self,ft_data,cp_data,rs_data,tp_data,rt_data,se_data,nx_data):

        def ft(sel_list):
            type = ["gp", "hh", "zq", "zs", "qdii", "fof"]
            if sel_list:
                return ",".join(type[i] for i in sel_list)
            return ""

        def cp(sel_list):
            if sel_list:
                return ",".join(sel_list)
            return ""

        def rs(sel_type_list, n_list):
            rs_type = ["z", "1y", "3y", "6y", "jn", "1n", "2n", "3n"]  # z,10_1y20
            n = ["",10, 20, 50, 100]
            l = []
            if len(sel_type_list) == 0:
                return ""
            else:
                for i in range(len(sel_type_list)):
                    t = rs_type[sel_type_list[i]] + "," + str(n[n_list[i]])
                    l.append(t)
            return "_".join(l)

        def tp(sel_list):
            if len(sel_list) == 0:
                return ""
            else:
                return ",".join(sel_list)

        def rt(sel_type_list, n_list):
            rt_type = ["cx", "sz", "zs", "ja"]
            n = ["", 5, 4, 3, 2, 1]
            l = []
            if len(sel_type_list) == 0:
                return ""
            else:
                for i in range(len(sel_type_list)):
                    t = rt_type[sel_type_list[i]] + "," + str(n[n_list[i]])
                    l.append(t)
            return "_".join(l)

        def se(k):
            n = ["", 2, 10, 50, 100, 1001]
            return str(n[k])

        def nx(k):
            n = ["", 1, 2, 3, 5, 7, 71]
            return str(n[k])

        return ft(ft_data),cp(cp_data),rs(rs_data[0],rs_data[1]),tp(tp_data),rt(rt_data[0],rt_data[1]),se(se_data),nx(nx_data)









DataHandle_filter=DataHandle_filter()
