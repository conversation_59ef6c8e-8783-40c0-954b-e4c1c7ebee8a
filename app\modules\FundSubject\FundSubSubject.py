from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QTableWidgetItem, QHeaderView, QMessageBox

from app.modules.FundSubject.api.get_sub_subject_data import get_sub_subject_data
from app.modules.FundSubject.src import style
from main_w import Ui_MainWindow


class FundSubSubject:
    def __init__(self, ui:Ui_MainWindow,subject_name,subject_code,subject_data,subject_color):
        self.ui = ui
        self.subject_name = subject_name
        self.subject_code = subject_code
        self.subject_data = subject_data
        self.subject_color = subject_color
        self.init_setup_sub_subject()

    def init_setup_sub_subject(self):
        self.current_page=1
        self.ui.subject_sub_widget.show()
        # self.ui.subject_title.setText(self.subject_name)
        self.ui.subject_title.setText(self.subject_name)
        self.ui.subject_sub_return_lb.mousePressEvent = lambda event: self.ui.subject_sub_widget.hide()
        self.ui.subject_sub_return_lb1.mousePressEvent = lambda event: self.ui.subject_sub_widget.hide()
        #顶部根据日增幅设置背景色

        self.ui.subject_title.setStyleSheet(f"background-color:{self.subject_color};color:white")
        self.load_sub_subject_data()
        self.init_pages_status()

    def load_sub_subject_data(self):
        try:
            self.worker_thread=get_sub_subject_data(self.subject_code,self.current_page)
            self.worker_thread.finished.connect(self.task_finished_sub_subject,)
            self.worker_thread.start()

        except Exception as e:
            print(f"load_sub_subject_data error: {e}")

    def task_finished_sub_subject(self,rate_list,rank_list,fund_data,len_data):
        try:
            self.total_pages=int(len_data)//20+1
            self.load_top_data(rate_list,rank_list)
            self.load_table_data(fund_data)
            self.init_pages_status()
        except Exception as e:
            print(f"task_finished_sub_subject error: {e}")


    def format_value(self,value):
        if value == "":
            return "--"
        else:
            return "{:.2f}%".format(float(value))

    def load_top_data(self,rate_list,rank_list):
        try:
            for i in range(len(rate_list)):
                getattr(self.ui, f"subject_sub_rate_{i+1}").setText(self.format_value(rate_list[i]))
                if rate_list[i] and float(rate_list[i]) > 0:
                    getattr(self.ui, f"subject_sub_rate_{i+1}").setStyleSheet(style.QLabel_bg_color_red)
                elif rate_list[i] and float(rate_list[i]) < 0:
                    getattr(self.ui, f"subject_sub_rate_{i+1}").setStyleSheet(style.QLabel_bg_color_green)
                getattr(self.ui, f"subject_sub_rank_{i + 1}").setText("排名: "+rank_list[i])
        except Exception as e:
            print(f"load_top_data error: {e}")


    def load_table_data(self,data):

        headers=["基金代码","基金名称","基金类型","日期","最新净值","持仓股占比","日增长率","近1周","近1月","近3月","近6月","今年以来","近1年","近2年","近3年","成立以来"]
        try:
            self.ui.subject_tableWidget.setColumnCount(len(headers))
            self.ui.subject_tableWidget.setHorizontalHeaderLabels(headers)
            self.ui.subject_tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            self.ui.subject_tableWidget.horizontalHeader().setStyleSheet(style.QHeaderView_style)
            self.ui.subject_tableWidget.setRowCount(len(data))
            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    if col_idx in [0, 1, ]:
                        item.setForeground(QColor("#294EBC"))
                    if col_idx in [i for i in range(6, 16)]:  # "#009900","#FF0018",
                        if str(cell_data) != "--":
                            if float(str(cell_data).strip("%")) > 0:
                                item.setForeground(QColor("#FF0018"))
                            elif float(str(cell_data).strip("%")) < 0:
                                item.setForeground(QColor("#009900"))
                            else:
                                item.setForeground(QColor("black"))
                    self.ui.subject_tableWidget.setItem(row_idx, col_idx, item)
            row_height = 40  # 设置行高为 30 像素
            for row in range(self.ui.subject_tableWidget.rowCount()):
                self.ui.subject_tableWidget.setRowHeight(row, row_height)
            start = (self.current_page - 1) * 20 + 1
            end = start + len(data)
            self.ui.subject_tableWidget.setVerticalHeaderLabels([str(i) for i in range(start, end)])
        except Exception as e:
            print(f"load_table_data error: {e}")

    def page_lb_style(self,page_name,status):
        if status:
            getattr(self.ui, f"{page_name}").setStyleSheet(style.QLabel_page_yes)
        else:
            getattr(self.ui, f"{page_name}").setStyleSheet(style.QLabel_page_no)

    def init_pages_status(self, ):
        self.ui.subject_current_page.setText(f"{self.current_page}/{self.total_pages}")
        self.ui.subject_pre_page.mousePressEvent = self.sub_page
        self.ui.subject_after_page.mousePressEvent = self.add_page
        self.ui.subject_goto_btn.mousePressEvent = self.goto_page
        self.update_page_status()

    def sub_page(self, event):
        if self.current_page > 1:
            self.current_page -= 1
            self.update_page_status()
            self.load_sub_subject_data()

    def add_page(self, event):
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_page_status()
            self.load_sub_subject_data()
    #
    def goto_page(self, event):
        page_ = self.ui.subject_lineEdit.text()
        if page_:
            page_ = int(page_)
            if page_ > 0 and page_ <= self.total_pages:
                self.current_page = page_
                self.update_page_status()
                self.load_sub_subject_data()
                self.ui.subject_lineEdit.clear()
            else:
                QMessageBox.information(self.ui.subject_sub_widget, "提示", "请输入正确的页码")
        else:
            QMessageBox.information(self.ui.subject_sub_widget, "提示", "请输入正确的页码")

    def update_page_status(self, ):
        if self.current_page > 1:
            self.page_lb_style("subject_pre_page", True)
        else:
            self.page_lb_style("subject_pre_page", False)
        if self.current_page < self.total_pages:
            self.page_lb_style("subject_after_page", True)
        else:
            self.page_lb_style("subject_after_page", False)







