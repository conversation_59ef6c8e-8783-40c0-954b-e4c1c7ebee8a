
with open("tb.txt","r",encoding="utf-8")as f:
    data=f.read()

from bs4 import BeautifulSoup
import requests
# print(data)
html =data

soup = BeautifulSoup(html, 'html.parser')
rows = soup.find_all('tr')  # 获取所有行
all_list=[]
for row in rows:
    name_code = row.find('td', class_='fund-name-code')
    if not name_code:  # 检查是否找到元素
        continue
    try:
        name = name_code.find('a', class_='name').text.strip()
        code = name_code.find('a', class_='code').text.strip()




        date = row.find_all('td')[2].text.strip()
        net_value = row.find_all('td')[3].text.strip()
        lj_value = row.find_all('td')[4].text.strip()
        day_value = row.find_all('td')[5].text.strip()
        m6_value = row.find_all('td')[6].text.strip()
        y1_value = row.find_all('td')[7].text.strip()
        gm_value = row.find_all('td')[8].text.strip()
        manager_value = row.find_all('td')[9].text.strip()
        status_value = row.find_all('td')[10].text.strip()

        all_list.append([code,name,date,net_value,lj_value,day_value,m6_value,y1_value,gm_value,manager_value,"开放申购"])

        # print(f"基金名称: {name}, 代码: {code}, 日期: {date}, 净值: {net_value},累计净值：{lj_value}, 日涨幅: {day_value}, 6月涨幅: {m6_value}, 1年涨幅: {y1_value}, 基金规模: {gm_value},基金经理：{manager_value}")
    except AttributeError as e:
        print(f"解析错误: {e}, 跳过该行")
        continue
print(all_list)

# headers={
#   "Host": "fund.eastmoney.com",
#   "Connection": "keep-alive",
#   "sec-ch-ua-platform": "\"Windows\"",
#   "X-Requested-With": "XMLHttpRequest",
#   "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
#   "Accept": "text/html, */*; q=0.01",
#   "sec-ch-ua": "\"Microsoft Edge\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"",
#   "sec-ch-ua-mobile": "?0",
#   "Sec-Fetch-Site": "same-origin",
#   "Sec-Fetch-Mode": "cors",
#   "Sec-Fetch-Dest": "empty",
#   "Referer": "https://fund.eastmoney.com/Company/80000229.html",
#   "Accept-Encoding": "gzip, deflate, br, zstd",
#   "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
#   "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; st_si=04203385243650; st_asi=delete; ASP.NET_SessionId=2tczdhye3erlivwf1nlhnuje; _adsame_fullscreen_20308=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=7; st_psi=20250627154736881-112200312944-8564950000"
# }
# import requests
# response = requests.get(url='https://fund.eastmoney.com/Company/home/<USER>',headers=headers,verify=False)
# print(response.text)
