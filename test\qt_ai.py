import sys
from PyQt6.QtWidgets import (
    <PERSON>Application, QWidget, QVBoxLayout, QHBoxLayout, QLineEdit, QPushButton, QScrollArea, QLabel, QMessageBox
)
from PyQt6.QtCore import Qt, QThread, pyqtSignal
import os
from openai import OpenAI

# 配置 OpenAI 客户端
client = OpenAI(
    base_url="https://ark.cn-beijing.volces.com/api/v3",  # 替换为你的 API URL
    api_key="05a8b070-9d8b-4849-b866-06f94e70c81f",  # 替换为你的 API Key
)



# 流式输出线程
class StreamResponseThread(QThread):
    stream_signal = pyqtSignal(str)  # 用于发送流式输出的信号

    def __init__(self, question, chat_history):
        super().__init__()
        self.question = question
        self.chat_history = chat_history
        self._is_running = True  # 控制线程是否继续运行

    def run(self):
        # 调用 deepseek-R1 API 实现流式输出
        messages = self.chat_history + [{"role": "user", "content": self.question}]
        stream = client.chat.completions.create(
            model="deepseek-v3-241226",  # 替换为你的模型 ID
            messages=messages,
            stream=True,
        )
        for chunk in stream:
            if not self._is_running:  # 如果用户终止回答，停止输出
                break
            if chunk.choices and chunk.choices[0].delta.content:
                self.stream_signal.emit(chunk.choices[0].delta.content)

    def stop(self):
        self._is_running = False  # 终止线程


# 聊天窗口的主界面
class ChatWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.chat_history = []  # 保存聊天记录
        self.current_thread = None  # 当前流式输出线程

    def init_ui(self):
        self.setWindowTitle("AI 基金投资助手")
        self.setGeometry(100, 100, 600, 800)

        # 主布局
        main_layout = QVBoxLayout()#27

        # 聊天记录区域（可滚动）
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.chat_container = QWidget()#17
        self.chat_layout = QVBoxLayout(self.chat_container)#34
        self.chat_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.scroll_area.setWidget(self.chat_container)
        main_layout.addWidget(self.scroll_area)

        # 输入区域
        input_layout = QHBoxLayout()
        self.input_box = QLineEdit()
        self.input_box.setPlaceholderText("请输入您的问题...")
        self.send_button = QPushButton("发送")
        self.send_button.clicked.connect(self.toggle_send_stop)  # 绑定切换按钮事件
        self.clear_button = QPushButton("清除上下文")
        self.clear_button.clicked.connect(self.clear_context)
        input_layout.addWidget(self.input_box)
        input_layout.addWidget(self.send_button)
        input_layout.addWidget(self.clear_button)
        main_layout.addLayout(input_layout)

        self.setLayout(main_layout)

    def toggle_send_stop(self):
        if self.send_button.text() == "发送":
            self.send_question()
        else:
            self.stop_response()

    def send_question(self):
        question = self.input_box.text().strip()
        if not question:
            QMessageBox.warning(self, "提示", "请输入问题！")
            return

        # 添加用户问题到聊天窗口
        self.add_user_message(question)

        # 启动流式输出线程
        self.current_thread = StreamResponseThread(question, self.chat_history)
        self.current_thread.stream_signal.connect(self.add_ai_message_stream)
        self.current_thread.finished.connect(self.thread_finished)
        self.current_thread.start()

        # 清空输入框
        self.input_box.clear()

        # 将按钮文本改为“停止”
        self.send_button.setText("停止")

    def stop_response(self):
        # 终止当前流式输出线程
        if self.current_thread and self.current_thread.isRunning():
            self.current_thread.stop()
            self.current_thread.wait()

        # 将按钮文本恢复为“发送”
        self.send_button.setText("发送")

    def add_user_message(self, message):
        # 创建用户问题的 widget
        user_widget = QWidget()
        user_layout = QVBoxLayout(user_widget)
        user_label = QLabel(f"用户：{message}")
        user_label.setStyleSheet("color: blue; font-weight: bold;")
        user_layout.addWidget(user_label)
        self.chat_layout.addWidget(user_widget)

        # 将用户问题添加到聊天历史
        self.chat_history.append({"role": "user", "content": message})

    def add_ai_message_stream(self, chunk):
        # 获取最后一个 AI 回答的 widget，如果没有则新建
        if not self.chat_history or not self.chat_history[-1]["role"] == "assistant":
            self.chat_history.append({"role": "assistant", "content": ""})
            ai_widget = QWidget()
            ai_layout = QVBoxLayout(ai_widget)
            self.ai_label = QLabel("AI：")
            self.ai_label.setStyleSheet("color: green; font-weight: bold;")
            self.copy_button = QPushButton("复制")
            self.copy_button.clicked.connect(self.copy_text)
            ai_layout.addWidget(self.ai_label)
            ai_layout.addWidget(self.copy_button)
            self.chat_layout.addWidget(ai_widget)
        else:
            self.ai_label = self.chat_layout.itemAt(self.chat_layout.count() - 1).widget().layout().itemAt(0).widget()

        # 追加流式输出内容
        self.ai_label.setText(self.ai_label.text() + chunk)
        self.chat_history[-1]["content"] += chunk  # 更新聊天历史
        self.scroll_area.ensureWidgetVisible(self.ai_label)

    def copy_text(self):
        # 复制文本到剪贴板
        clipboard = QApplication.clipboard()
        clipboard.setText(self.ai_label.text())
        QMessageBox.information(self, "提示", "文本已复制到剪贴板！")

    def clear_context(self):
        # 清除聊天记录
        for i in reversed(range(self.chat_layout.count())):
            self.chat_layout.itemAt(i).widget().setParent(None)
        self.chat_history = []

    def thread_finished(self):
        # 线程结束后的操作
        self.current_thread = None

        # 将按钮文本恢复为“发送”
        self.send_button.setText("发送")

    def closeEvent(self, event):
        # 关闭窗口时终止线程
        if self.current_thread and self.current_thread.isRunning():
            self.current_thread.stop()
            self.current_thread.wait()
        event.accept()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = ChatWindow()
    window.show()
    sys.exit(app.exec())