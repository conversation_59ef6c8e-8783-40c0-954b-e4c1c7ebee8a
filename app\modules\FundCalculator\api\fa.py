import datetime
import sys
import os
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>, QMainWindow, QVBoxLayout, QWidget, QPushButton, QLineEdit, QMessageBox
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtCore import QUrl, QFile, QTextStream

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()

        self.setWindowTitle("Line Chart Viewer")
        self.setGeometry(100, 100, 800, 600)

        # 创建布局
        self.layout = QVBoxLayout()

        # 创建输入框
        self.input_x = QLineEdit(self)
        self.input_x.setPlaceholderText("Enter x-axis data (comma separated)")
        self.layout.addWidget(self.input_x)

        self.input_y = QLineEdit(self)
        self.input_y.setPlaceholderText("Enter y-axis data (comma separated)")
        self.layout.addWidget(self.input_y)

        # 创建按钮
        self.run_button = QPushButton("Run", self)
        self.run_button.clicked.connect(self.run)
        self.layout.addWidget(self.run_button)

        # 创建 Web 视图
        self.web_view = QWebEngineView(self)
        self.layout.addWidget(self.web_view)

        # 设置主窗口的布局
        container = QWidget()
        container.setLayout(self.layout)
        self.setCentralWidget(container)

        # 加载 HTML 模板
        self.html_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\widget\calculator\template.html"
        if not os.path.exists(self.html_path):
            QMessageBox.critical(self, "Error", "HTML template not found!")
            sys.exit(1)

    from PyQt6.QtWebEngineCore import QWebEngineScript

    def run(self):
        try:
            # 将时间戳转换为 datetime 对象

            # 将 datetime 对象格式化为字符串
            # date_string =
            with open("data_set.txt","r",encoding="utf8")as f:
                data_=f.read()
            data_=eval(data_)
            k = []
            time_ = []
            for i in data_:
                k.append(i["value"])
                time_.append(datetime.datetime.fromtimestamp(i["time"]/1000).strftime("%Y-%m-%d"))
            x_labels = time_
            y_values =k
            min_v = min(k)
            max_v = max(k)
            stepSize = (max_v - min_v) / 4
            min1 = min_v - stepSize / 2
            max1 = max_v + stepSize / 2
            sub1 = max_v - min_v
            if 0 < sub1 <= 0.5:
                stepSize = round((max_v - min_v) / 8, 2)
                max1 = round(max1, 1)
                min1 = round(min1, 1)
            elif 0.5 < sub1 <= 2:
                stepSize = round((max_v - min_v) / 4, 1)
                max1 = round(max1, 1)
                min1 = round(min1, 1)
            elif 2 < sub1 < 10000:
                stepSize = int((max_v - min_v) / 4)
                max1 = int(max1)
                min1 = int(min1)
            # return min_y, max_y, stepSize
        except ValueError:
            QMessageBox.warning(self, "Warning", "Invalid data format. Please enter comma-separated numbers.")
            return

        if len(x_labels) != len(y_values):
            QMessageBox.warning(self, "Warning", "X and Y data must have the same length.")
            return

        # 读取 HTML 模板
        with open(self.html_path, 'r') as file:
            html_content = file.read()
        print(min1,max1,stepSize)
        # cny="uu"
        # 将数据注入 HTML
        js_code = f"renderChart({x_labels}, {y_values},{stepSize},{min1},{max1});"
        html_content = html_content.replace("</body>", f"<script>{js_code}</script></body>")
        print(html_content)
        # 在 Web 视图中加载 HTML 内容
        self.web_view.setHtml(html_content)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())