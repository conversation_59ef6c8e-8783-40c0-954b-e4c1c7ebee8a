import sys
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QHBoxLayout,
    QLabel, QPushButton
)
from PyQt6.QtCore import Qt


class PaginationWidget(QWidget):
    def __init__(self, total_pages=900):
        super().__init__()
        self.total_pages = min(max(total_pages, 1), 3)  # 确保总页数在 1-900 之间
        self.current_page = 1  # 默认起始页数为 1

        self.init_ui()

    def init_ui(self):
        # 主布局
        layout = QVBoxLayout()

        # 当前页和总页数显示
        self.page_label = QLabel(f"第 {self.current_page} 页 / 共 {self.total_pages} 页")
        self.page_label.setAlignment(Qt.AlignmentFlag.AlignCenter)
        layout.addWidget(self.page_label)

        # 翻页按钮布局
        button_layout = QHBoxLayout()

        # 左翻页按钮
        self.left_button = QPushButton("← 上一页")
        self.left_button.setEnabled(self.current_page > 1)  # 初始状态：第 1 页时禁用
        self.left_button.clicked.connect(self.go_to_previous_page)
        button_layout.addWidget(self.left_button)

        # 右翻页按钮
        self.right_button = QPushButton("下一页 →")
        self.right_button.setEnabled(self.current_page < self.total_pages)  # 初始状态：最后一页时禁用
        self.right_button.clicked.connect(self.go_to_next_page)
        button_layout.addWidget(self.right_button)

        layout.addLayout(button_layout)
        self.setLayout(layout)

        # 设置窗口标题和大小
        self.setWindowTitle("分页控件")
        self.resize(300, 100)

    def go_to_previous_page(self):
        if self.current_page > 1:
            self.current_page -= 1
            self.update_ui()

    def go_to_next_page(self):
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.update_ui()

    def update_ui(self):
        # 更新页数显示
        self.page_label.setText(f"第 {self.current_page} 页 / 共 {self.total_pages} 页")

        # 更新按钮状态
        self.left_button.setEnabled(self.current_page > 1)
        self.right_button.setEnabled(self.current_page < self.total_pages)


if __name__ == "__main__":
    app = QApplication(sys.argv)
    pagination = PaginationWidget(total_pages=900)  # 可以修改 total_pages 测试
    pagination.show()
    sys.exit(app.exec())