import matplotlib.pyplot as plt
import numpy as np
import time
# 设置中文字体
s=time.time()
plt.rcParams["font.sans-serif"] = ["SimHei"]
plt.rcParams['axes.unicode_minus'] = False

plt.figure(figsize=(20, 8))
plt.grid(axis='y',color='#E9E9E9',linestyle='--', alpha=0.5, zorder=0)
categories = [
    "娃哈哈概念", "世界旅行", "手艺医疗", "A制药（医疗）", "肝糖类（医疗）",
    "水养殖","E-A概念","氢气概念","油总服","普瑞达概念",
    "央视50","工业4.0","3D打印","上证50","磷化工",
    "稀缺资源","黄金概念","小金属概念","培直钻石","航母概念",
"净水概念","空气能热泵","TOPCon电池","冷链物流","地热能",
"汽车拆解","动力电池回收","超级电容","五墨烯","上海自贸",
"体外诊断","PCB","创新药","流感","减肥药",
"工业大麻","光刻机(胶)","环氧丙烷","稀土永磁","复合集流体",

]

values = [
   13.8,12.5,11,10,9,8,7,6,5,3,13.8,12.5,11,10,9,8,7,6,5,3,13.8,12.5,11,10,9,8,7,6,5,3,0,-1,-2,-3,-4,-5,-6,-7,-8,-9
]
categories = [ '\n'.join(list(category)) for category in categories ]

# 创建条形图
plt.bar(categories, values, zorder=3, color=np.where(np.array(values) >= 0, 'red', 'green'))
plt.bar([], [], color='red', label='主力净流入')  # 红色图例
plt.bar([], [], color='green', label='主力净流出')  # 绿色图例

# 取消标题和x轴标签
plt.title('')  # 取消标题
plt.xlabel('')  # 取消x轴标签
plt.ylabel('销售额（万元）')

# 添加数据标签（文字颜色为红色）
# for i, value in enumerate(values):
#     plt.text(i, value + 1, str(value)+"亿", ha='center', va='bottom', color='red')
for i, (concept, value) in enumerate(zip(categories, values)):
    if value >= 0:
        # 大于 0 的标签：红色，位于柱状图上方
        plt.text(i, value , f'{value}亿', ha='center', va='bottom', color='red', fontsize=8)
    else:
        # 小于 0 的标签：绿色，位于柱状图下方
        plt.text(i, value , f'{value}亿', ha='center', va='top', color='green', fontsize=8)


# 设置图表边框
for spine in plt.gca().spines.values():
    spine.set_edgecolor('black')  # 设置边框为黑色
plt.legend(loc='upper right')
# 取消刻度
plt.xticks([])  # 取消x轴刻度
# plt.yticks([])  # 取消y轴刻度

# 设置x轴数据纵向排列，颜色为蓝色
plt.xticks(range(len(categories)), categories, color='#072F92')
# 设置图表边框
ax = plt.gca()
ax.spines['top'].set_visible(False)    # 隐藏上框线
ax.spines['right'].set_visible(False)  # 隐藏右框线
ax.spines['left'].set_edgecolor('#333333')   # 左框线为黑色
ax.spines['bottom'].set_edgecolor('#333333') # 下框线为黑色
import matplotlib.patches as mpatches

# 手动创建图例
red_patch = mpatches.Patch(color='red', label='主力净流入')
green_patch = mpatches.Patch(color='green', label='主力净流出')
plt.legend(handles=[red_patch, green_patch])

# 保存为图片
plt.savefig('bar_chart.png', dpi=300, bbox_inches='tight')
e=time.time()
print(e-s)
# 显示图表
plt.show()