import re

import requests


class article_data:
    def __init__(self):
        self.article_url=""

        self.html_start = """
                        <html>
                            <body>"""
        self.html_end = """
                        </body>
                        </html>
                        """
    @staticmethod
    def return_article_data(url):
        pattern = r'<!--文章主体-->(.*?)<!-- 文尾部其它信息 -->'
        header={
  "Host": "finance.eastmoney.com",
  "Connection": "keep-alive",
  "Cache-Control": "max-age=0",
  "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
  "sec-ch-ua-mobile": "?0",
  "sec-ch-ua-platform": "\"Windows\"",
  "Upgrade-Insecure-Requests": "1",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
  "Sec-Fetch-Site": "same-origin",
  "Sec-Fetch-Mode": "navigate",
  "Sec-Fetch-User": "?1",
  "Sec-Fetch-Dest": "document",
  "Referer": "https://finance.eastmoney.com/a/ccjdd.html",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; websitepoptg_api_time=1754238273039; nid_id=984469405; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; EMFUND0=null; EMFUND4=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND5=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND6=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND8=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND9=08-04 12:35:13@#$%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; st_si=13300841178050; st_asi=delete; fullscreengg=1; fullscreengg2=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=3; st_psi=20250804142925552-113104312931-9023304206; _qimei_i_1=41fa24dbc925"
}
        html_start = """
                                <html>
                                    <body>"""
        html_end = """
                                </body>
                                </html>
                                """
        data=requests.get(url,headers=header,verify=False).text
        match = re.search(pattern, data, re.DOTALL)
        # if match:
        article_ = match.group(1).strip()  # 获取匹配的内容并去掉首尾空白
        article_ = html_start + article_ + html_end
        # print(article_)
        return article_

article_data=article_data
print(article_data.return_article_data(url="https://finance.eastmoney.com/a/202508043474696020.html"))

