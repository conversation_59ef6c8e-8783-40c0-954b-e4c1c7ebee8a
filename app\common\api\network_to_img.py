from PyQt6.QtNetwork import QNetworkAccessManager, QNetworkRequest, QNetworkReply
from PyQt6.QtCore import QUrl
from PyQt6.QtGui import QPixmap
from main_w import Ui_MainWindow

class NetworkToImg:
    def __init__(self, network_manager, ui: Ui_MainWindow):
        self.ui = ui
        self.network_manager = network_manager
        # 连接信号槽
        self.network_manager.finished.connect(self.on_image_downloaded)

    def load_image(self, url, target_label):
        """加载网络图片到指定QLabel"""
        request = QNetworkRequest(QUrl(url))
        reply = self.network_manager.get(request)
        reply.target_label = target_label  # 绑定目标label名称

    def on_image_downloaded(self, reply):
        """图片下载完成回调"""
        target_label = getattr(reply, 'target_label', None)
        if target_label:
            label_widget = getattr(self.ui, target_label)

            if reply.error() == QNetworkReply.NetworkError.NoError:
                pixmap = QPixmap()
                pixmap.loadFromData(reply.readAll())
                label_widget.setPixmap(pixmap)
            else:
                label_widget.setText("图片访问失败")

        reply.deleteLater()

"""使用
# 初始化网络管理器和图片加载器
self.network_manager = QNetworkAccessManager()
self.network_img_loader = NetworkToImg(self.network_manager, self.ui)

# 使用封装的类加载图片
self.network_img_loader.load_image(
    "https://webquotepic.eastmoney.com/GetPic.aspx?nid=90.BK0478&imageType=rs&0.25487243381954694",
    "lzbk_img_lb"
)

"""
