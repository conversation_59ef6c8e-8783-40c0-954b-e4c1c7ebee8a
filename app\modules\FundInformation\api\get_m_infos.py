import asyncio
import re

import aiohttp
import requests
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from PyQt6.QtCore import pyqtSignal, QObject

from app.api.other.wrapp import calculate_time
from bs4 import BeautifulSoup
class url_infos():
    # @calculate_time
    def __init__(self,url_l):
        self.urls=url_l
        self.result_lock = threading.Lock()
        self.results = [None] * 20
        self.html_start = """
                                <html>
                                    <body>"""
        self.html_end = """
                                </body>
                                </html>
                                """
        self.replace_t='<img src="https://np-newspic.dfcfw.com/download/D25411840164398846730_w690h389.jpg" class="em_handle_adv_close" />'
        self.replace_t2='<img src="https://np-newspic.dfcfw.com/download/D24723587176925434542_w690h180_o.jpg" class="em_handle_adv_close" />'

    async def fetch_url_content(self,session, url):
        try:
            async with session.get(url.replace("fund","finance")) as response:
                data = await response.text()
                soup = BeautifulSoup(data, 'html.parser')
                items = soup.find_all('div', class_='item')
                # 提取日期和来源
                if len(items) == 5:
                    date = items[0].text.strip()  # 第一个item是日期
                    source = items[1].text.replace("来源：", "").strip()  # 第二个item是来源，去掉"来源："前缀
                elif len(items) == 6:
                    date = items[0].text.strip()  # 第一个item是日期
                    source = items[2].text.replace("来源：", "").strip()  # 第二个item是来源，去掉"来源："前缀
                else:
                    print("未找到日期和来源信息")
                    print(url)
                pattern = r'<!--文章主体-->(.*?)<!-- 文尾部其它信息 -->'
                match = re.search(pattern, data, re.DOTALL)
                if match:
                    article_ = match.group(1).strip().replace(self.replace_t,"").replace(self.replace_t2,"") # 获取匹配的内容并去掉首尾空白
                    article_ = self.html_start + article_ + self.html_end
                    print(article_)
                else:
                    article_ = self.html_start + "无数据" + self.html_end
                    print("无数据")
                content =[ date,source,article_]
                return content
        except requests.exceptions.RequestException as e:
            return  f"请求失败: {e}"

    async def main(self):
        async with aiohttp.ClientSession() as session:
            tasks = [self.fetch_url_content(session, url) for url in self.urls]
            results = await asyncio.gather(*tasks)
            for i, content in enumerate(results):
                self.results[i]=content
        return self.results

url_infos=url_infos

