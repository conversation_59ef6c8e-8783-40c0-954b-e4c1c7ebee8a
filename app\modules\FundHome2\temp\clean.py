from bs4 import BeautifulSoup

def extract_text_from_html(html_content):
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 获取所有段落
        paragraphs = []
        for p in soup.find_all('p'):
            text = p.get_text()
            if text:  # 只添加非空段落
                paragraphs.append(text)

        # 如果没有p标签，则获取全部文本
        if not paragraphs:
            return soup.get_text().strip()

        # 用换行符连接段落
        return '\n'.join(paragraphs)

    except Exception as e:
        print(f"文本提取失败: {e}")
        return html_content

# 使用示例
html_data = """<html>
                                    <body><p style="display:none;height:1px;overflow:hidden;"><br /></p><!-- EM_StockImg_Start --><p style="text-align:center;"><a  target="_blank" href="http://quote.eastmoney.com/us/BRK_A.html" data-code="BRK_A|106|7" data-code2="BRK_A|106|10|" class="EmImageRemark" target="_blank"><img src="https://webquoteklinepic.eastmoney.com/GetPic.aspx?nid=106.BRK_A&imageType=k&token=28dfeb41d35cc81d84b4664d7c23c49f&at=1" border="0" alt="K图 BRK_A_0" data-code="K BRK_A|106|7" data-code2="K BRK_A|106|10|" style="border:#d1d1d1 1px solid;" width="578" height="276" /></a></p><!-- EM_StockImg_End --><p style="display:none;height:1px;overflow:hidden;"><br /></p><!-- EM_StockImg_Start --><p style="text-align:center;"><a  target="_blank" href="http://quote.eastmoney.com/us/BRK_B.html" data-code="BRK_B|106|7" data-code2="BRK_B|106|1|" class="EmImageRemark" target="_blank"><img src="https://webquoteklinepic.eastmoney.com/GetPic.aspx?nid=106.BRK_B&imageType=k&token=28dfeb41d35cc81d84b4664d7c23c49f&at=1" border="0" alt="K图 BRK_B_0" data-code="K BRK_B|106|7" data-code2="K BRK_B|106|1|" style="border:#d1d1d1 1px solid;" width="578" height="276" /></a></p><!-- EM_StockImg_End --><p>　　当地时间8月2日，“股神”沃伦·巴菲特旗下的伯克希尔·哈撒韦公司（以下简称伯克希尔）在提交的监管文件中披露，公司对<span id="stock_105.KHC"><a  target="_blank" href="http://quote.eastmoney.com/unify/r/105.KHC" class="keytip" data-code="105,KHC">卡夫亨氏</a></span><span id="quote_105.KHC"></span>（Kraft Heinz）的投资进行了38亿美元的巨额减计，将其投资的账面价值降至84亿美元，较2017年年底的逾170亿美元大幅下降。<br /></p><p>　　对于巴菲特而言，此次减计可能是他辉煌投资生涯中一次罕见的重挫。伯克希尔在文件中明确，此次减计的部分原因是<span web="1"  target="_blank" href="http://quote.eastmoney.com/unify/r/105.KHC" class="em_stock_key_common" data-code="105,KHC">卡夫亨氏</span>公允价值的持续下滑。</p><p>　　<strong>0</strong><strong>1</strong></p><p>　　<strong>“我们在投资上犯了一些错误”</strong></p><p>　　<strong>关于对<span web="1"  target="_blank" href="http://quote.eastmoney.com/unify/r/105.KHC" class="em_stock_key_common" data-code="105,KHC">卡夫亨氏</span>的投资，伯克希尔二季报显示，公司派驻卡夫亨氏董事会的代表已于2025年5月19日辞任，导致公司获取财务信息的时效性和完整性受限，目前仅能依赖卡夫亨氏公开披露的信息。次日（5月20日），卡夫亨氏宣布正在评估可能提升股东价值的战略交易，但强调不保证最终能达成交易或确定具体时间表。</strong></p><p>　　伯克希尔表示，基于上述因素，结合当前的经济环境及其他不确定性，公司判定该项投资账面价值与公允价值之间的未实现亏损已构成非暂时性减值。因此，公司在第二季度确认了约50亿美元的税前减值损失，作为卡夫亨氏收益权益的组成部分，此举将该项投资的账面价值调减至公允价值水平。</p><p>　　伯克希尔称，由于公司获取卡夫亨氏财务信息的时效性受限于其公开披露节奏，公司认为相关信息将无法及时纳入当季合并财务报表。鉴于此，自2025年第二季度起，公司对卡夫亨氏投资的权益法核算将采用滞后一季度的处理方式。截至2025年6月30日，公司对卡夫亨氏投资的账面价值已低于按持股比例计算的股东权益份额。</p><p>　　巴菲特曾在2019年的媒体采访中对卡夫亨氏的投资做了反思。巴菲特说：“企业始终在与零售商的博弈中挣扎。真正强大的品牌，即便是与<span id="stock_106.WMT"><a  target="_blank" href="http://quote.eastmoney.com/unify/r/106.WMT" class="keytip" data-code="106,WMT">沃尔玛</a></span><span id="quote_106.WMT"></span>、Costco这类巨头抗衡也毫不逊色，而弱势品牌往往败下阵来。卡夫亨氏可能在应对某些零售商时犯了错。”</p><center><img src="https://np-newspic.dfcfw.com/download/D25670739813192139990_w864h482.jpg" alt="图片" width="580" class="rich_pages wxw-img" style="border:#d1d1d1 1px solid;padding:3px;margin:5px 0;" /></center><p>　　此外，巴菲特还表示：“我们在对卡夫亨氏的投资上犯了一些错误。我们对该公司业务的某些方面过于乐观，没有充分考虑行业竞争格局的变化以及成本控制等方面的挑战。”</p><p>　　今年5月，巴菲特在伯克希尔年度股东大会上宣布，他计划在年底退休。同时，他将向公司董事会推荐非<span id="bk_90.BK0474"><a  target="_blank" href="http://quote.eastmoney.com/unify/r/90.BK0474" class="keytip" data-code="90,BK0474">保险</a></span><span id="bkquote_90.BK0474"></span>业务副董事长格雷格·阿贝尔在年底接任首席执行官（CEO）的职位。</p><p style="text-align:center;"><a  target="_blank" href="https://acttg.eastmoney.com/pub/xxlout_khmbdfcfappnew_random_0007467a"><img src="https://np-newspic.dfcfw.com/download/D25753173264126031117_w690h389.jpg" class="em_handle_adv_close" /></a></p><p>　　<strong>0</strong><strong>2</strong></p><p>　　<strong>卡夫亨氏拟分拆或出售旗下品牌</strong></p><p>　　<strong>据了解，卡夫亨氏由原H.J.亨氏公司和卡夫食品合并后成立，是全球最大的食品与饮料公司之一，旗下拥有亨氏番茄酱、亨氏沙拉酱、冷冻薯条等产品。2015年，巴菲特作为关键推手，促成了卡夫与亨氏的合并。然而，自合并以来，这家包装食品巨头的股价已累计下跌62%，而同期标普500指数则上涨了202%。</strong></p><p>　　当前，卡夫亨氏正考虑对其业务进行重大拆分，计划将旗下大部分杂货业务剥离为独立上市实体。据知情人士透露，此次拆分估值或达200亿美元的杂货业务部门，可能成为今年消费品行业最大规模交易，最快将于今年第三季度末或第四季度完成。</p><p>　　拟剥离资产包含多个美国超市标志性品牌——奥斯卡梅尔肉类、Velveeta奶酪、果冻品牌Jell-O、麦斯威尔咖啡、绅士坚果、Lunchables方便餐及Capri Sun果汁饮料等。这些构成卡夫亨氏核心货架稳定型加工食品矩阵的产品虽深植美国家庭，却难以适应消费者日益青睐更新鲜、健康、少加工食品的潮流。</p><p>　　拆分后的存续公司将保留高增长及高端业务线，包括亨氏番茄酱、费城奶油奶酪及系列调味酱料，聚焦产品创新、清洁标签配方改良与国际市场拓展。此举呼应了食品行业主流趋势——传统巨头正收缩战线，集中资源发展具有全球吸引力的高利润率品牌。</p><p>　　卡夫亨氏的最大股东伯克希尔现持有约27%的流通股。其投资始于2013年2月与巴西私募3G资本联手以280亿美元（含债务）收购亨氏公司的交易，创下当时食品行业最大杠杆收购纪录。2015年，二者又将亨氏与卡夫食品集团合并，意图通过协同效应实现成本节约。不过，截至2025年年中，伯克希尔所持股份较其账面价值缩水约45亿美元。近期，伯克希尔已减少在其董事会的参与度，释放出淡出日常经营的信号。</p><p>　　<strong>0</strong><strong>3</strong></p><p>　　<strong>巴菲特就特朗普关税发出严厉警告</strong></p><p>　　<strong>伯克希尔财报显示，2025年第二季度，公司实现营收925.15亿美元，上年同期为936.53亿美元；归属于伯克希尔股东的净收益为123.70亿美元，上年同期为303.48亿美元，净利润暴跌59%。</strong></p><center><img src="https://np-newspic.dfcfw.com/download/D24721740563708172922_w779h866.jpg" alt="图片" width="580" class="rich_pages wxw-img" style="border:#d1d1d1 1px solid;padding:3px;margin:5px 0;" /></center><p>　　伯克希尔的运营利润在第二季度同比下降4%，至111.6亿美元。业绩下滑主要受到<span web="1"  target="_blank" href="http://quote.eastmoney.com/unify/r/90.BK0474" class="em_stock_key_common" data-code="90,BK0474">保险</span>承保业务下降的影响，而铁路、能源、制造业、服务业和零售业务的利润则均较去年同期有所增长。</p><p>　　财报显示，巴菲特的现金储备从3月底的3470亿美元小幅下降至3441亿美元（约合2.48万亿元人民币），这是三年来首次减少。此前，由于巴菲特难以找到合适的投资机会，这笔“现金弹药”持续攀升至历史新高。</p><p>　　值得关注的是，巴菲特在财报中再次对美国总统特朗普的关税及其对旗下各业务的潜在影响发出严厉警告。</p><p>　　伯克希尔在财报中称：“这些事件的变化速度，包括由国际贸易政策和关税发展引发的紧张局势，在2025年上半年加快了步伐。这些事件的最终结果仍存在相当大的不确定性，很有可能会对我们大多数，甚至是所有运营业务，以及我们持有的股票投资产生不利影响，从而显著影响我们的未来业绩。”</p><p style="display:none;height:1px;overflow:hidden;"><br /></p><!-- EM_Module_Extend_Start --><center style="text-align:left;"><div class="em_module_extend" style="display:none;"> </div></center><p class="em_media">（文章来源：中国商报）</p>                        </div>
                                </body>
                                </html>"""

clean_text = extract_text_from_html(html_data)
print(clean_text)