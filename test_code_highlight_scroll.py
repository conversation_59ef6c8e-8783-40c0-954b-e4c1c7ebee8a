#!/usr/bin/env python3
"""
测试代码高亮和滚动功能
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QScrollArea
from PyQt6.QtCore import Qt, QTimer

# 导入我们的MarkdownWebView组件
from app.modules.FundAiChat.FundAiChat import MarkdownWebView


class TestCodeHighlightScroll(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("代码高亮和滚动测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建滚动区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        self.scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 创建聊天容器
        self.chat_container = QWidget()
        self.chat_layout = QVBoxLayout(self.chat_container)
        self.chat_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.scroll_area.setWidget(self.chat_container)
        
        layout.addWidget(self.scroll_area)
        
        # 创建测试按钮
        button_layout = QVBoxLayout()
        
        highlight_button = QPushButton("测试代码高亮")
        highlight_button.clicked.connect(self.test_code_highlight)
        button_layout.addWidget(highlight_button)
        
        long_content_button = QPushButton("测试长内容滚动")
        long_content_button.clicked.connect(self.test_long_content)
        button_layout.addWidget(long_content_button)
        
        stream_button = QPushButton("测试流式输出滚动")
        stream_button.clicked.connect(self.test_stream_scroll)
        button_layout.addWidget(stream_button)
        
        clear_button = QPushButton("清空聊天")
        clear_button.clicked.connect(self.clear_chat)
        button_layout.addWidget(clear_button)
        
        layout.addLayout(button_layout)
        
        # 添加初始测试内容
        self.add_initial_content()
    
    def add_markdown_message(self, content):
        """添加一个Markdown消息"""
        # 创建容器widget
        message_widget = QWidget()
        message_layout = QVBoxLayout(message_widget)
        message_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建圆角容器
        rounded_container = QWidget()
        rounded_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #EAEDF1;
                border-radius: 10px;
            }
            QWidget:hover {
                border: 1px solid #ccc;
                background-color: #F1F9FE;
            }
        """)
        
        container_layout = QVBoxLayout(rounded_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建MarkdownWebView
        webview = MarkdownWebView()
        webview.setStyleSheet("""
            QWebEngineView {
                border: none;
                background-color: transparent;
            }
        """)
        
        # 设置滚动回调
        webview.scroll_to_bottom_callback = self.scroll_to_bottom
        
        # 设置内容
        webview.update_content(content)
        
        container_layout.addWidget(webview)
        message_layout.addWidget(rounded_container)
        self.chat_layout.addWidget(message_widget)
        
        # 滚动到底部
        self.scroll_to_bottom()
        
        return webview
    
    def scroll_to_bottom(self):
        """滚动到底部"""
        try:
            # 确保最后一个widget可见
            if self.chat_layout.count() > 0:
                last_widget = self.chat_layout.itemAt(self.chat_layout.count() - 1).widget()
                if last_widget:
                    self.scroll_area.ensureWidgetVisible(last_widget)
            
            # 直接设置滚动条到最大值
            scroll_bar = self.scroll_area.verticalScrollBar()
            if scroll_bar:
                QTimer.singleShot(50, lambda: scroll_bar.setValue(scroll_bar.maximum()))
                QTimer.singleShot(200, lambda: scroll_bar.setValue(scroll_bar.maximum()))
                
        except Exception as e:
            print(f"Error in scroll_to_bottom: {e}")
    
    def add_initial_content(self):
        """添加初始内容"""
        content = """# 代码高亮和滚动测试

这是一个测试代码高亮和自动滚动功能的示例。

## Python代码示例

```python
def calculate_investment_return(principal, annual_rate, years, compound_frequency=1):
    \"\"\"
    计算复利投资收益
    
    Args:
        principal: 本金
        annual_rate: 年利率
        years: 投资年数
        compound_frequency: 每年复利次数
    
    Returns:
        最终金额和收益
    \"\"\"
    final_amount = principal * (1 + annual_rate / compound_frequency) ** (compound_frequency * years)
    profit = final_amount - principal
    
    return {
        'final_amount': final_amount,
        'profit': profit,
        'profit_rate': (profit / principal) * 100
    }

# 示例使用
result = calculate_investment_return(100000, 0.08, 5, 12)
print(f"投资收益: {result['profit']:.2f}元")
```

## JavaScript代码示例

```javascript
class InvestmentCalculator {
    constructor(principal, rate, years) {
        this.principal = principal;
        this.rate = rate;
        this.years = years;
    }
    
    calculateCompoundInterest() {
        const amount = this.principal * Math.pow(1 + this.rate, this.years);
        return {
            finalAmount: amount,
            interest: amount - this.principal
        };
    }
    
    calculateMonthlyPayment() {
        const monthlyRate = this.rate / 12;
        const numPayments = this.years * 12;
        
        return (this.principal * monthlyRate * Math.pow(1 + monthlyRate, numPayments)) /
               (Math.pow(1 + monthlyRate, numPayments) - 1);
    }
}

// 使用示例
const calculator = new InvestmentCalculator(100000, 0.08, 5);
const result = calculator.calculateCompoundInterest();
console.log(`最终金额: ${result.finalAmount.toFixed(2)}`);
```

**代码高亮测试完成！** 🎨"""
        
        self.add_markdown_message(content)
    
    def test_code_highlight(self):
        """测试代码高亮"""
        content = """## 多语言代码高亮测试

### SQL查询示例

```sql
-- 查询基金收益排行
SELECT 
    fund_name,
    fund_code,
    return_1year,
    return_3year,
    risk_level,
    CASE 
        WHEN return_1year > 0.15 THEN '高收益'
        WHEN return_1year > 0.08 THEN '中等收益'
        ELSE '低收益'
    END as performance_category
FROM fund_performance 
WHERE risk_level <= 3
ORDER BY return_1year DESC
LIMIT 10;
```

### Java代码示例

```java
public class FundCalculator {
    private double principal;
    private double annualRate;
    private int years;
    
    public FundCalculator(double principal, double annualRate, int years) {
        this.principal = principal;
        this.annualRate = annualRate;
        this.years = years;
    }
    
    public double calculateFutureValue() {
        return principal * Math.pow(1 + annualRate, years);
    }
    
    public static void main(String[] args) {
        FundCalculator calc = new FundCalculator(10000, 0.08, 5);
        System.out.printf("未来价值: %.2f%n", calc.calculateFutureValue());
    }
}
```

### CSS样式示例

```css
.fund-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s ease;
}

.fund-card:hover {
    transform: translateY(-5px);
}

.fund-return {
    color: #27ae60;
    font-weight: bold;
    font-size: 1.2em;
}

.fund-return.negative {
    color: #e74c3c;
}
```

**多语言代码高亮测试完成！** ✨"""
        
        self.add_markdown_message(content)
    
    def test_long_content(self):
        """测试长内容滚动"""
        content = """## 长内容滚动测试

这是一个很长的内容，用来测试滚动功能是否正常工作。

### 基金投资策略详解

#### 1. 价值投资策略

价值投资是一种长期投资策略，通过分析公司的基本面来寻找被低估的股票。

#### 2. 成长投资策略

成长投资关注那些具有高增长潜力的公司，通常这些公司的收入和利润增长率较高。

#### 3. 指数投资策略

指数投资通过购买指数基金来分散风险，获得市场平均收益。

#### 4. 量化投资策略

量化投资使用数学模型和算法来进行投资决策。

### 详细的投资计算

让我们来看一个详细的投资计算示例：

```python
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

def monte_carlo_simulation(initial_investment, annual_return, volatility, years, simulations=1000):
    \"\"\"
    蒙特卡洛模拟投资收益
    \"\"\"
    results = []
    
    for _ in range(simulations):
        returns = np.random.normal(annual_return, volatility, years)
        portfolio_value = initial_investment
        
        for annual_return in returns:
            portfolio_value *= (1 + annual_return)
        
        results.append(portfolio_value)
    
    return np.array(results)

# 运行模拟
initial = 100000
results = monte_carlo_simulation(initial, 0.08, 0.15, 10)

print(f"平均最终价值: {np.mean(results):.2f}")
print(f"中位数最终价值: {np.median(results):.2f}")
print(f"最好情况: {np.max(results):.2f}")
print(f"最坏情况: {np.min(results):.2f}")
```

### 风险管理

风险管理是投资中最重要的环节之一。以下是一些关键的风险管理原则：

1. **分散投资** - 不要把所有鸡蛋放在一个篮子里
2. **资产配置** - 合理分配不同类型的资产
3. **定期再平衡** - 定期调整投资组合
4. **止损策略** - 设置合理的止损点
5. **长期持有** - 避免频繁交易

### 市场分析

市场分析包括技术分析和基本面分析两个方面。

#### 技术分析

技术分析通过研究价格图表和交易量来预测未来的价格走势。

#### 基本面分析

基本面分析通过研究公司的财务状况、行业前景等来评估投资价值。

### 投资心理学

投资心理学研究投资者的行为模式和心理偏差。

常见的心理偏差包括：
- 损失厌恶
- 确认偏差
- 羊群效应
- 过度自信

### 总结

投资是一门艺术，也是一门科学。成功的投资需要：

1. 扎实的知识基础
2. 良好的心理素质
3. 严格的纪律性
4. 持续的学习能力

**长内容测试完成！这应该会触发滚动到底部。** 📜"""
        
        self.add_markdown_message(content)
    
    def test_stream_scroll(self):
        """测试流式输出滚动"""
        # 创建一个新的消息
        webview = self.add_markdown_message("")
        
        # 模拟流式输出
        stream_content = [
            "# 流式输出滚动测试\n\n",
            "正在生成长内容",
            "...\n\n",
            "## 第一部分内容\n\n",
            "这是第一部分的内容，用来测试流式输出时的滚动功能。\n\n",
            "### 代码示例1\n\n",
            "```python\n",
            "def test_function():\n",
            "    print('测试函数')\n",
            "    return True\n",
            "```\n\n",
            "## 第二部分内容\n\n",
            "这是第二部分的内容，继续测试滚动。\n\n",
            "### 更多代码\n\n",
            "```javascript\n",
            "function calculateReturn(principal, rate) {\n",
            "    return principal * (1 + rate);\n",
            "}\n",
            "```\n\n",
            "## 第三部分内容\n\n",
            "这是第三部分，内容越来越长。\n\n",
            "### 表格数据\n\n",
            "| 基金名称 | 收益率 | 风险等级 |\n",
            "|----------|--------|----------|\n",
            "| 基金A | 8.5% | 中 |\n",
            "| 基金B | 12.3% | 高 |\n",
            "| 基金C | 5.2% | 低 |\n\n",
            "## 第四部分内容\n\n",
            "这是最后一部分内容。\n\n",
            "### 最终代码\n\n",
            "```sql\n",
            "SELECT * FROM funds \n",
            "WHERE return_rate > 0.08 \n",
            "ORDER BY return_rate DESC;\n",
            "```\n\n",
            "> **重要提示**: 这个测试应该会自动滚动到底部！\n\n",
            "**流式输出滚动测试完成！** 🚀"
        ]
        
        # 模拟逐步添加内容
        self.stream_index = 0
        self.stream_data = stream_content
        self.stream_webview = webview
        self.timer = QTimer()
        self.timer.timeout.connect(self.add_stream_chunk)
        self.timer.start(200)  # 每200ms添加一块内容
    
    def add_stream_chunk(self):
        """添加流式内容块"""
        if self.stream_index < len(self.stream_data):
            self.stream_webview.append_content(self.stream_data[self.stream_index])
            self.stream_index += 1
        else:
            self.timer.stop()
    
    def clear_chat(self):
        """清空聊天记录"""
        for i in reversed(range(self.chat_layout.count())):
            item = self.chat_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestCodeHighlightScroll()
    window.show()
    sys.exit(app.exec())
