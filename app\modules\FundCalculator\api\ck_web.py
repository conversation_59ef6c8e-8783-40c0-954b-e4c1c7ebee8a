import requests
from bs4 import BeautifulSoup

url="https://www.csai.cn/bankrate/newBankView.do?searchName=&page=&banksort=&fieldName=&scrollId=dingqiDeposit"
response=requests.get(url,verify=False,)
response_text=response.text
# print(response_text)
soup = BeautifulSoup(response_text, 'html.parser')

# 查找所有 <div class="tables_tr">
bank_rows = soup.find_all('div', class_='tables_tr')
bank_ck_dict={}
# 遍历每个银行行
for row in bank_rows:
    # 提取银行名称（<a>标签内的文本）
    bank_name = row.find('a').get_text(strip=True)
    bank_tds = row.find_all('span', class_='bank_td')
    numbers = [td.get_text(strip=True)for td in bank_tds]
    if len(numbers)==6:
        bank_ck_dict[bank_name]=numbers
for i in bank_ck_dict:
    print(i,bank_ck_dict[i])