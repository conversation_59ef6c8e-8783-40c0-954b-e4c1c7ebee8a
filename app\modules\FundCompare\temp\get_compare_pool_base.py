import json

import requests

#基本信息
JBXXurl="https://api.fund.eastmoney.com/FundCompare/JBXX?bzdm=000614,015016,001691,012060,012062,012584,012585,014319,014320,011971&callback=jQuery183022900414814736725_1745219233201&_=1745219233210"
YJPJurl="https://api.fund.eastmoney.com/FundCompare/YJPJBJ?bzdm=000614,015016,001691,012060,012062,012584,012585,014319,014320,011971&callback=jQuery18307263176541602768_1745220869410&_=1745220869585"
ZCPZurl="https://api.fund.eastmoney.com/FundCompare/ZCPZ?bzdm=011971,014319,001691,000011,001751,009512,610008,610108,002405,004253&callback=jQuery18307263176541602768_1745220869411&_=1745220869420"
LJSYLurl="https://api.fund.eastmoney.com/FundCompare/LJSYL?bzdm=011971,014319,001691,000011,001751,009512,610008,610108,002405,004253&c=year&callback=jQuery183004562079219496318_1745496457601&_=1745496457723"
TLPMurl="https://api.fund.eastmoney.com/FundCompare/TLPM?bzdm=011971,014319,001691,000011,001751,009512,610008,610108,002405,004253&c=month&callback=jQuery183026223942108755394_1745583485471&utf-8&_=1745583497886"
header={
  "Host": "api.fund.eastmoney.com",
  "Connection": "keep-alive",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-site",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://fund.eastmoney.com/",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=75994445429365; st_asi=delete; ap_0_13a7d068=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=9; st_psi=20250421150713164-112200312939-5598177333"
}
import copy
def format_str(s):
    if s=="":
        return "--"
    else:
        return "{}%".format(s)

def grade_str(s):
    if s=="":
        return "暂无评级"
    else:
        return s
code_name=[]

def get_jbxx_data(url):
    data_list = []
    response=requests.get(url,headers=header,verify=False,)
    response_text=json.loads(response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")",""))
    for i in eval(response_text["Data"]):
        t=i.split(",")
        data_list.append([
            t[0],#代码
            t[1],#基金名称
            # t[2],#基金简称
            t[3],#基金类型
            t[7],#日期
            t[6],#最新单位净值
            t[8],#累计净值
            format_str(t[9]),#日增长率
            t[10],#上期单位净值
            t[12],#基金经理
            t[14],#基金公司
        ])
        z = [t[0], t[1]]
        code_name.append(z)

def get_yjpj_data(url):
    jdsy_data_list = []


    lsndsy_data_list = copy.deepcopy(code_name)
    dtsy_data_list = copy.deepcopy(code_name)
    jjpj_data_list = copy.deepcopy(code_name)
    response = requests.get(url, headers=header, verify=False, )
    response_text = json.loads(response.text.strip("jQuery18307263176541602768_1745220869410(").replace(")", ""))
    t_jdsy=json.loads(response_text["Data"])["jdsy"]
    t_lsndsy=json.loads(response_text["Data"])["lsndsy"]
    t_dtsy=json.loads(response_text["Data"])["dtsy"]
    t_jjpj=json.loads(response_text["Data"])["jjpj"]
    for i in t_jdsy:
        t=i.split(",")
        jdsy_data_list.append(t)
    for i in range(len(t_lsndsy)):
        t=list(t_lsndsy[i].values())
        lsndsy_data_list[i].extend(t)
    for i in range(len(t_dtsy)):
        t=t_dtsy[i].split(",")
        dtsy_data_list[i].extend(t)

    for i in range(len(t_jjpj)):
        t=[grade_str(j) for j in t_jjpj[i].split(",")]
        jjpj_data_list[i].extend(t)
    # print(jdsy_data_list[0])
    print(lsndsy_data_list[0])
    print(dtsy_data_list[0])
    print(jjpj_data_list[0])

def get_zcpz_data(url):
    zcpz_data_list = []
    hypz_data_list = copy.deepcopy(code_name)
    gpcc_data_list = copy.deepcopy(code_name)
    zqcc_data_list = copy.deepcopy(code_name)
    response=requests.get(url,headers=header,verify=False,)
    response_text=json.loads(response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")",""))
    t_zcpz=json.loads(response_text["Data"])["zcpz"]
    t_hypz=json.loads(response_text["Data"])["hypz"]
    t_gpcc=json.loads(response_text["Data"])["gpcc"]
    t_zqcc=json.loads(response_text["Data"])["zqcc"]
    for i in t_zcpz:
        t=i.split(",")
        zcpz_data_list.append(t)
    print(zcpz_data_list[0])
    for i in range(len(t_hypz)):
        print( t_hypz[i])
        if t_hypz[i]:
            t = t_hypz[i].split(",")
        else:
            t = ["" for i in range(10)]
        hypz_data_list[i].extend(t)
    print(hypz_data_list[2])
    for i in range(len(t_gpcc)):
        if t_gpcc[i]!=[]:
            t=[]
            for j in t_gpcc[i]:
                k=j.split(",")
                z=f"{k[1]}/n{k[2]}"
                t.append(z)
        else:
            t=["" for i in range(10)]
        gpcc_data_list[i].extend(t)
    print(gpcc_data_list)
    for i in range(len(t_zqcc)):
        if t_zqcc[i] != []:
            t = []
            for j in t_zqcc[i]:
                k = j.split(",")
                z = f"{k[0]}/n{k[1]}"
                t.append(z)
        else:
            t=["" for i in range(5)]
        zqcc_data_list[i].extend(t)
    print(zqcc_data_list)

def get_ljsyl_data(url):
    s="011971,014319,001691,000011,001751,009512,610008,610108,002405,004253"
    s_url=s.split(",")
    data_list = []
    response = requests.get(url, headers=header, verify=False, )
    response_text = json.loads(response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")", ""))
    print(response_text["Data"])
    date=[]
    t1=[[] for i in range(10)]
    print(t1)
    for i in eval(response_text["Data"])["dataProvider"]:
        print(i)
        date.append(i["date"])
        for j in range(10):
            t1[j].append(i[s_url[j]])
    print(t1)
    print(len(date))
    print(data_list)

def get_tlpm_data(url):
    s="011971,014319,001691,000011,001751,009512,610008,610108,002405,004253"
    s_url=s.split(",")
    data_list = []
    response = requests.get(url, headers=header, verify=False, )
    response_text = json.loads(response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")", ""))
    print(response_text["Data"])
    date=[]
    t1=[[] for i in range(10)]
    for i in eval(response_text["Data"])["dataProvider"]:
        print(i)
        date.append(i["date"])
        for j in range(10):
            t1[j].append(i[s_url[j]])
    print(t1)
    print(len(date))
    print(type(max(t1[0])))




# get_jbxx_data(JBXXurl)
# get_yjpj_data(YJPJurl)
# get_zcpz_data(ZCPZurl)
# get_ljsyl_data(LJSYLurl)
get_tlpm_data(TLPMurl)
