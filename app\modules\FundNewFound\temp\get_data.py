import json
import re

import requests

headers={
  "Host": "fund.eastmoney.com",
  "Connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-origin",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://fund.eastmoney.com/data/xinfound.html",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=35324403182615; st_asi=delete; ap_0_13a7d068=1; ASP.NET_SessionId=nvr3t0bytqzhdkz1nhhlhkwc; _adsame_fullscreen_20308=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=10; st_psi=20250511183534956-112200312946-3328705501"
}
zs_fund_url="https://fund.eastmoney.com/data/FundNewIssue.aspx?t=zs&sort=jzrgq,desc&page=1,1000&isbuy=1&v=0.3229192471325548"#在售基金
xcl_fund_url="https://fund.eastmoney.com/data/FundNewIssue.aspx?t=xcln&sort=jzrgq,desc&y=&page=1,50&isbuy=1&v=0.6705815753202203"#新成立基金

def re_data(data):
    s = data.replace('datas:', '"datas":').replace('record:', '"record":').replace('pages:', '"pages":').replace('curpage:', '"curpage":')

    # 解析为字典
    result = json.loads(s)
    print(result)
    return result
def get_zs_data():
    try:
        response=requests.get(zs_fund_url,headers=headers,verify=False)
        data=response.text.strip("var newfunddata=")
        # data1="'"+data+"'"
        res=re_data(data)["datas"]
        res_list=[]
        for i in res:
            t=[
                i[0],#基金代码
                i[1],#基金名称
                i[2],#基金公司
                i[4],#基金类型
                i[5],#集中认购期
                i[6],#最高认购费率
                i[7],#基金经理
                i[8],#基金状态,认购期
            ]
            res_list.append(t)
        print(res_list)
        return res_list
    except Exception as e:
        print(e)
def get_xcl_data():
    try:
        response=requests.get(xcl_fund_url,headers=headers,verify=False)
        data=response.text.strip("var newfunddata=")
        # data1="'"+data+"'"
        res = re_data(data)["datas"]
        res_list = []
        for i in res:
            t = [
                i[0],  # 基金代码
                i[1],  # 基金名称
                i[2],  # 基金公司
                i[4],  # 基金类型
                i[10],  # 集中认购期
                i[5],  # 募集份额(亿份)
                i[6],  # 成立日期
                i[8],  # 基金经理
                i[9],  # 基金状态,认购期
                i[-1],#优惠费率
            ]
            res_list.append(t)
        print(res_list)
        return res_list
    except Exception as e:
        print(e)
get_zs_data()
get_xcl_data()