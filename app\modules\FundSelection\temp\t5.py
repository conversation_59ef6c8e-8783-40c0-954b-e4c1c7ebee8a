from PyQt6.QtWidgets import (QApplication, QMainWindow, QTableWidget,
                             QTableWidgetItem, QVBoxLayout, QWidget,
                             QPushButton, QHeaderView, QAbstractItemView)
from PyQt6.QtCore import Qt


class TableDeleteApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("批量删除表格行示例")
        self.setGeometry(100, 100, 600, 400)

        # 创建主窗口部件
        self.central_widget = QWidget()
        self.setCentralWidget(self.central_widget)

        # 创建布局
        self.layout = QVBoxLayout()
        self.central_widget.setLayout(self.layout)

        # 创建表格
        self.table = QTableWidget()
        self.table.setColumnCount(3)
        self.table.setHorizontalHeaderLabels(["ID", "名称", "描述"])
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.table.setSelectionBehavior(QAbstractItemView.SelectionBehavior.SelectRows)
        self.table.setSelectionMode(QAbstractItemView.SelectionMode.ExtendedSelection)

        # 添加示例数据
        self.populate_table()

        # 添加删除按钮
        self.delete_button = QPushButton("批量删除选中行")
        self.delete_button.clicked.connect(self.delete_selected_rows)

        # 将部件添加到布局
        self.layout.addWidget(self.table)
        self.layout.addWidget(self.delete_button)

    def populate_table(self):
        # 添加一些示例数据
        data = [
            [1, "项目A", "这是项目A的描述"],
            [2, "项目B", "这是项目B的描述"],
            [3, "项目C", "这是项目C的描述"],
            [4, "项目D", "这是项目D的描述"],
            [5, "项目E", "这是项目E的描述"],
        ]

        self.table.setRowCount(len(data))
        for row, items in enumerate(data):
            for col, text in enumerate(items):
                item = QTableWidgetItem(str(text))
                self.table.setItem(row, col, item)

    def delete_selected_rows(self):
        # 获取选中的行
        selected_rows = set()
        for index in self.table.selectedIndexes():
            selected_rows.add(index.row())

        # 按降序排序以便从下往上删除
        for row in sorted(selected_rows, reverse=True):
            self.table.removeRow(row)


if __name__ == "__main__":
    app = QApplication([])
    window = TableDeleteApp()
    window.show()
    app.exec()