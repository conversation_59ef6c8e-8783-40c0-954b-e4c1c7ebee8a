<!DOCTYPE html>
<html>
<head>
    <title>增强型线段图表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/luxon@3.0.1"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0"></script>
    <style>
        body { margin: 0; padding: 20px; font-family: Arial; }
        #chart-container { width: 500px; height: 350px; }
        .control-panel { margin-bottom: 15px; }
    </style>
</head>
<body>
    <div id="chart-container">
        <canvas id="line-chart"></canvas>
    </div>

    <script>
        /*CONFIG_PLACEHOLDER*/
        /*DATA_PLACEHOLDER*/

        let chart; // 全局图表引用

        function initChart() {
            const ctx = document.getElementById('line-chart').getContext('2d');

            // 准备数据集
            const datasets = lineData.map((line, index) => ({
                label: chartConfig.legends[index] || `数据集 ${index + 1}`,
                data: line.points,
                borderColor: line.color,
                backgroundColor: 'rgba(0,0,0,0)',
                borderWidth: 2,
                pointRadius: 3,
                tension: 0.1
            }));

            // 创建图表
            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartConfig.x_labels,
                    datasets: datasets
                },
                options: getChartOptions()
            });
        }

        function getChartOptions() {
            return {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        min: chartConfig.y_min,
                        max: chartConfig.y_max,

                    },
                    x: {
                        display: chartConfig.show_x_labels,
                        type: 'category',
                        ticks: {
                            autoSkip: true,
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            boxWidth: 12,
                            padding: 20
                        }
                    },
                    tooltip: {
                        mode: 'index',
                        intersect: false
                    }
                },
                interaction: {
                    mode: 'nearest',
                    axis: 'x',
                    intersect: false
                }
            };
        }

        // 更新图表函数
        function updateChart(newData, newConfig) {
            // 更新数据集
            chart.data.datasets = newData.map((line, index) => ({
                ...chart.data.datasets[index],
                data: line.points,
                borderColor: line.color
            }));

            // 更新配置
            chart.options.scales.y.min = newConfig.y_min;
            chart.options.scales.y.max = newConfig.y_max;
            chart.options.scales.x.display = newConfig.show_x_labels;

            chart.update();
        }

        // 初始化图表
        document.addEventListener('DOMContentLoaded', initChart);
    </script>
</body>
</html>