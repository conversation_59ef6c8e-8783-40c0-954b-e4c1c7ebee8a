import requests

class FundCodeName():
    def __init__(self):
        self.fund_code_list = []
        self.fund_name_list = []
        self.url="http://fund.eastmoney.com/js/fundcode_search.js"
        self.headers={
          "Host": "fund.eastmoney.com",
          "Cache-Control": "max-age=0",
          "Upgrade-Insecure-Requests": "1",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
          "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7",
          "Accept-Encoding": "gzip, deflate",
          "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
          "If-None-Match": "\"680f21ff-297867\"",
          "If-Modified-Since": "Mon, 28 Apr 2025 06:36:47 GMT"
        }

    def return_data_list(self):
        with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\common\js\code_name_js.txt","r",encoding="utf-8")as f:
            data=f.read()
        data_list=eval(data)
        return data_list

    def return_fund_code_name_list(self):
        data_list=self.return_data_list()
        for i in data_list:
            if "后端" not in i[2] and "REIT" not in i[2] and "货币" not in i[3]:
                self.fund_code_list.append(i[0])
                self.fund_name_list.append(i[2])
        return self.fund_code_list,self.fund_name_list

    def get_fund_type(self):
        data_list=self.return_data_list()
        l=set()
        for i in data_list:
            if i[3]!="" and i[3]!="Reits":
                l.add(i[3].split("-")[0])
        print(list(l))
        return list(l)

    def generate_fund_code_type_dict(self)->dict:#对传入的基金代码判断类型并返回一个字典
        type_list=self.get_fund_type()
        di={}
        data_list = self.return_data_list()
        for i in data_list:
            if  i[3]!="" and i[3]!="Reits" and "后端" not in i[2] and "REIT" not in i[2] and "货币" not in i[3]:
                for j in type_list:
                    if i[3].split("-")[0]==j:
                        di[i[0]]=j
        return di

    def return_fund_code(self,name):
        l=self.return_data_list()
        for i in l:
            if i[2]==name:
                return i[0]
        assert "无法匹配对应代码 None"




FundCodeName=FundCodeName()

# FundCodeName.generate_fund_code_type_dict()
# FundCodeName.check_code_type(["560010","000001","166301","320007","015150","161725","011103","012768"])


