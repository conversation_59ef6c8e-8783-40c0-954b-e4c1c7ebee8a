import asyncio
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit

from app.modules.FundCalculator.api.get_bank_ck import get_bank_ck_data
from app.modules.FundCalculator.api.get_bank_wh import get_bank_wh_data
from app.modules.FundCalculator.type.Calculator_ck import Calculator_ck
from app.modules.FundCalculator.type.Calculator_dk import Calculator_dk
from app.modules.FundCalculator.type.Calculator_jj import Calculator_jj
from app.modules.FundCalculator.type.Calculator_sw import Calculator_sw
from app.modules.FundCalculator.type.Calculator_wh import Calculator_wh
from app.modules.FundCalculator.type.Calculator_zq import Calculator_zq


class FundCalculator:
    def __init__(self, ui):
        self.ui = ui
        self.init_setup()

    def init_setup(self):
        self.bg_color_list = []
        self.calculator_type_list = ["ck", "sw", "wh", "jj", "dk", "zq"]
        self.ui.top_compute_ck.mousePressEvent = partial(self.change_calculator_index, index=0)
        self.ui.top_compute_sw.mousePressEvent = partial(self.change_calculator_index, index=1)
        self.ui.top_compute_wh.mousePressEvent = partial(self.change_calculator_index, index=2)
        self.ui.top_compute_jj.mousePressEvent = partial(self.change_calculator_index, index=3)
        self.ui.top_compute_dk.mousePressEvent = partial(self.change_calculator_index, index=4)
        self.ui.top_compute_zq.mousePressEvent = partial(self.change_calculator_index, index=5)
        self.close_calculator_widget()
        # self.ui.tz_compute_widget.hide()

        self.ck_calculator=Calculator_ck(self.ui)
        self.sw_calculator=Calculator_sw(self.ui)
        self.wh_calculator=Calculator_wh(self.ui)
        self.jj_calculator=Calculator_jj(self.ui)
        self.dk_calculator=Calculator_dk(self.ui)
        self.zq_calculator=Calculator_zq(self.ui)


    def change_calculator_index(self, event, index):
        self.calculator_widget_index = index
        self.show_calculator_widget(self.calculator_widget_index)

    def show_calculator_widget(self, index):
        try:
            self.ui.plainTextEdit_2.hide()
            self.ui.calculator_type_listWidget.clear()
            widget_name = self.calculator_type_list[index]
            for i in self.calculator_type_list:
                getattr(self.ui, f"{i}_total_widget").hide()
            self.ui.wh_time_widget.hide()
            match (index):
                case 0:  # 存款
                    getattr(self.ui, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["活期储蓄计算器", "整存整取计算器", "零存整取计算器", "整存零取计算器",
                                                "存本取息计算器", "定活两便计算器", "通知存款计算器", "教育储蓄计算器"]
                    self.ck_calculator.show_ck_widget()
                    self.ui.calculator_type_lb.setText("存款类计算器")
                case 1:  # 税务
                    self.ui.calculator_type_lb.setText("税务类计算器")
                    getattr(self.ui, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["个人所得税计算器", "购房相关税费计算器"]
                    self.sw_calculator.show_sw_widget()

                case 2:  # 外汇
                    getattr(self.ui, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["外汇兑换计算器"]
                    self.wh_calculator.show_wh_widget()

                case 3:  # 基金
                    getattr(self.ui, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["申(认)购费用计算器", "赎回费用计算器", "收益计算器", "持有期计算器"]
                    self.jj_calculator.show_jj_widget()
                    self.ui.calculator_type_lb.setText("基金类计算器")
                case 4:  # 贷款
                    getattr(self.ui, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["个人贷款计算器", ]
                    self.dk_calculator.show_dk_widget()
                    self.ui.calculator_type_lb.setText("贷款类计算器")
                case 5:  # 债券
                    getattr(self.ui, f"{widget_name}_total_widget").show()
                    self.sub_listWidget_list = ["债券购买收益率计算器", "债券出售收益率计算器",
                                                "债券持有期间收益率计算器", "国债买卖计算器",
                                                "国债收益计算器", ]
                    self.zq_calculator.show_zq_widget()
                    self.ui.calculator_type_lb.setText("债券类计算器")
            # 向listwidget添加内容
            for i in self.sub_listWidget_list:
                self.ui.calculator_type_listWidget.addItem(i)
        except Exception as e:
            print(e)



    def load_bank_wh_data(self, bank_name: str):
        try:
            self.worker_thread_bank_wh = get_bank_wh_data(bank_name=bank_name)
            self.worker_thread_bank_wh.finished.connect(self.task_finished_bank_wh)
            self.worker_thread_bank_wh.start()
            if not self.ck_table_status:
                self.bank_ck_info = asyncio.run(get_bank_ck_data(search_name="", page=8).main())
                self.ui.bank_ck_tableWidget.hide()
                self.ui.bank_search_load.setText("正在加载")
                self.all_bank_ck_data = self.bank_ck_info
                self.ck_table_status = True
        except Exception as e:
            print(e)

    def task_finished_bank_wh(self, wh_data_list):
        self.bank_wh_data = wh_data_list
        self.bank_wh_table_load(data=self.bank_wh_data)
        # self.all_bank_ck_data=ck_data_list
        # 加载各银行存款汇率
        # print(bank_ck_info)
        self.bank_ck_table_load(self.bank_ck_info)
        self.ui.bank_ck_tableWidget.show()

    def bank_wh_table_load(self, data):
        headers = ["货币代码", "货币名称", "买入价", "卖出价"]
        self.ui.bank_wh_tableWidget.setColumnCount(4)
        self.ui.bank_wh_tableWidget.setRowCount(len(data))
        self.ui.bank_wh_tableWidget.setHorizontalHeaderLabels(headers)
        for row in range(len(data)):
            for col in range(4):
                item = QTableWidgetItem(f"{data[row].split("|")[col]}")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.ui.bank_wh_tableWidget.setItem(row, col, item)

    def open_calculator_widget(self):
        try:
            #默认进入 存款（ck）
            self.show_calculator_widget(0)          #测试
            self.ui.calculator_widget.show()
            self.bank_wh_name_list = ["boc", "spdb", "icbc", "ecitic", "cmbchina", "cib", "cebbank", "ccb", "bankcomm","abchina"]
            # 加载牌价数据
            self.select_bank_name = "boc"
            self.ck_table_status = False
            self.load_bank_wh_data(self.select_bank_name)
            self.ui.bank_select.currentTextChanged.connect(self.bank_wh_combox_select)
            self.ui.bank_time_lb.setText(f"(以100单位外币兑换人民币) 最新数据截至：{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}")
            # 查询
            self.ui.bank_search_btn.mousePressEvent = self.query_bank_name

        except Exception as e:
            print(e)

    def query_bank_name(self, event):
        search_name = self.ui.bank_search_input.text()
        if search_name in ["", "银行", "银", "行"]:
            # query_data=asyncio.run(get_bank_ck_data(search_name="", page=8).main())
            self.bank_ck_table_load(self.all_bank_ck_data)
        else:
            l = []
            v_l = []
            for i in range(len(self.all_bank_ck_data)):
                if search_name in self.all_bank_ck_data[i][0] or search_name == self.all_bank_ck_data[0]:
                    # self.load_bank_search_ck(search_name=search_name)
                    l.append(i)
                    for j in l:
                        v_l.append((self.all_bank_ck_data[j]))
            self.bank_ck_table_load(v_l)
    # def load_bank_search_ck(self,search_name:str):
    #     try:
    #         self.bank_search_load.setText("正在搜索中")
    #         self.worker_thread_bank_ck = get_search_bank_data(search_name=search_name)
    #         self.worker_thread_bank_ck.finished.connect(self.task_finished_bank_ck)
    #         self.worker_thread_bank_ck.start()
    #         self.bank_ck_tableWidget.hide()
    #     except Exception as e:
    #         self.bank_search_load.setText("暂无数据，请重新搜索其他银行！")
    #
    #
    # def task_finished_bank_ck(self, ck_data_list):
    #     self.bank_ck_table_load(ck_data_list)
    #     self.bank_ck_tableWidget.show()
    def bank_ck_table_load(self, data):
        headers = ["银行", "三个月", "半年", "一年", "两年", "三年", "五年"]
        self.ui.bank_ck_tableWidget.setColumnCount(7)
        self.ui.bank_ck_tableWidget.setRowCount(len(data))
        self.ui.bank_ck_tableWidget.setHorizontalHeaderLabels(headers)
        for row in range(len(data)):
            for col in range(7):
                item = QTableWidgetItem(f"{data[row][col]}")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.ui.bank_ck_tableWidget.setItem(row, col, item)
        self.ui.bank_ck_tableWidget.setColumnWidth(0, 150)
        for i in range(1, 7):
            self.ui.bank_ck_tableWidget.setColumnWidth(i, 77)

    def bank_wh_combox_select(self, text):
        try:
            bank_mapping = {
                "中国银行": "boc",
                "浦发银行": "spdb",
                "中国工商银行": "icbc",
                "中信银行": "ecitic",
                "招商银行": "cmbchina",
                "兴业银行": "cebbank",
                "中国光大银行": "cib",
                "中国建设银行": "cebbank",  # 修正：应该是ccb而不是cebbank
                "交通银行": "bankcomm",
                "中国农业银行": "abchina"
            }

            self.select_bank_name = bank_mapping.get(text, "boc")  # 默认中国银行
            self.load_bank_wh_data(self.select_bank_name)
        except Exception as e:
            print(e)

    def close_calculator_widget(self):
        self.ui.calculator_widget.hide()
