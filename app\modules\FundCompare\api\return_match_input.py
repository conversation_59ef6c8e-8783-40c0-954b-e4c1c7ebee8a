from app.common.api.code_name import FundCodeName


class match_fund_code():
    def __init__(self):
        self.all_code_list,  self.all_name_list = FundCodeName.return_fund_code_name_list()
        # print(self.all_code_list)


    def return_match_list(self,input_str):
        l=[]
        try:
            for i in range(len(self.all_code_list)):
                if str(int(input_str)) in self.all_code_list[i]:
                    l.append(f"{self.all_code_list[i]} - {self.all_name_list[i]}")
            return l
        except:
            for i in range(len(self.all_name_list)):
                if input_str in self.all_name_list[i]:
                    l.append(f"{self.all_code_list[i]} - {self.all_name_list[i]}")
            return l


match_fund_code=match_fund_code()
# print(m.return_match_list("789"))
# print(m.return_match_list("半导体"))


