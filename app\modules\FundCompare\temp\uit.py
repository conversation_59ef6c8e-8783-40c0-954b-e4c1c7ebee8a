import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QTableWidget, QTableWidgetItem,
    QPushButton, QVBoxLayout, QWidget, QHeaderView, QHBoxLayout,
    QMessageBox
)
from PyQt6.QtCore import Qt, pyqtSlot
from PyQt6.QtGui import QIcon


class TableWithAllButtons(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("多功能表格")
        self.setGeometry(100, 100, 800, 400)

        # 示例数据
        self.data = [
            ["Alice", "25", "Engineer"],
            ["<PERSON>", "30", "Designer"],
            ["<PERSON>", "35", "Manager"],
            ["<PERSON>", "40", "Director"]
        ]

        self.init_ui()

    def init_ui(self):
        layout = QVBoxLayout()

        # 创建表格（数据列 + 操作列）
        self.table = QTableWidget()
        self.table.setColumnCount(6)  # 3数据列 + 3操作列
        headers = ["姓名", "年龄", "职业", "上移", "下移", "删除"]
        self.table.setHorizontalHeaderLabels(headers)
        self.table.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)

        self.load_data_to_table()
        layout.addWidget(self.table)

        container = QWidget()
        container.setLayout(layout)
        self.setCentralWidget(container)

    def load_data_to_table(self):
        """加载数据并添加所有操作按钮"""
        self.table.setRowCount(len(self.data))
        for row_idx, row_data in enumerate(self.data):
            # 填充数据
            for col_idx, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                self.table.setItem(row_idx, col_idx, item)

            # 操作列按钮容器
            btn_container = QWidget()
            btn_layout = QHBoxLayout()
            btn_layout.setContentsMargins(0, 0, 0, 0)
            btn_layout.setSpacing(5)

            # 上移按钮（第一行禁用）
            up_btn = QPushButton("↑")
            up_btn.setProperty("row", row_idx)
            up_btn.clicked.connect(self.move_up)
            up_btn.setEnabled(row_idx > 0)
            btn_layout.addWidget(up_btn)

            # 下移按钮（最后一行禁用）
            down_btn = QPushButton("↓")
            down_btn.setProperty("row", row_idx)
            down_btn.clicked.connect(self.move_down)
            down_btn.setEnabled(row_idx < len(self.data) - 1)
            btn_layout.addWidget(down_btn)

            # 删除按钮
            del_btn = QPushButton("×")
            del_btn.setProperty("row", row_idx)
            del_btn.clicked.connect(self.delete_row)
            del_btn.setStyleSheet("color: red;")  # 红色删除按钮
            btn_layout.addWidget(del_btn)

            btn_container.setLayout(btn_layout)
            self.table.setCellWidget(row_idx, len(row_data), btn_container)

    @pyqtSlot()
    def move_up(self):
        """上移当前行"""
        row = self.sender().property("row")
        if row > 0:
            self.data[row], self.data[row - 1] = self.data[row - 1], self.data[row]
            self.load_data_to_table()

    @pyqtSlot()
    def move_down(self):
        """下移当前行"""
        row = self.sender().property("row")
        if row < len(self.data) - 1:
            self.data[row], self.data[row + 1] = self.data[row + 1], self.data[row]
            self.load_data_to_table()

    @pyqtSlot()
    def delete_row(self):
        """删除当前行（带确认对话框）"""
        row = self.sender().property("row")

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除 {self.data[row][0]} 吗？",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )

        if reply == QMessageBox.StandardButton.Yes:
            self.data.pop(row)
            self.load_data_to_table()


if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = TableWithAllButtons()
    window.show()
    sys.exit(app.exec())