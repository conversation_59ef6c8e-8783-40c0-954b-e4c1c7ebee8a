# 用从python写从000001-999999爬取存在的对应的基金名称，并保存为fund_code.xlsx文件，字段名从左往右分别为，代码，基金名称，基金类型
import pandas as pd
import time
import requests
from datetime import datetime
#
# code = "000003"
# current_timestamp = int(datetime.now().timestamp())
# url1 = "http://fundgz.1234567.com.cn/js/{}.js?rt={}".format(code, current_timestamp)
# url="http://fund.eastmoney.com/js/fundcode_search.js"
# response = requests.get(url)
from openpyxl import load_workbook


# data = [["000001", "华夏成长混合", "HXCZHH", "混合型-灵活"]]
# df = pd.DataFrame(data, columns=columns)
# df.to_excel("fund_code.xlsx", index=False)

# 获取保存数据

# with open("code_js","w",encoding="utf8")as f:
#     f.write(response.text)
with open("../../txt/code_js", "r", encoding="utf8")as f:
    data_1=f.read()
start_time = time.time()
data_2=data_1.replace("var r = ","" ).replace("[[","").replace("]]","").replace('"',"")
data=[]
for i in data_2.split("],["):
    if i.split(",")[0].__contains__("881011"):
        print(i)
end_time = time.time()
print(end_time - start_time)



# print(data[0])
# columns = ["代码", "简拼", "基金名称", "基金类型"]
# # # data = [["000001", "华夏成长混合", "HXCZHH", "混合型-灵活"]]
# df = pd.DataFrame(data, columns=columns)
# df.to_excel("fund_code.xlsx", index=False)



# #搜索
# def read_file(name):
#     df = pd.read_excel(name)
#     return df
#
# # 搜索基金代码为 881011 的数据
# def search_fund_code(fund_code):
#     df = read_file("fund_code.xlsx")
#     result_p = df[df["代码"] == fund_code]
#     return result_p
#
# #模糊搜索基金
# def match_fund_name(fund_name):
#     df=read_file("fund_code.xlsx")
#     result_bil = df[df["基金名称"].str.contains(fund_name)]
#     return result_bil
#
# #模糊搜索基金代码为 含有88
# def match_fund_code(fund_code):
#     df = read_file("fund_code.xlsx")
#     df["代码"] = df["代码"].astype(str)
#     result = df[df["代码"].str.contains(fund_code)]
#
# def m():
#     print("11")
# def main():
#     read_file("fund_code.xlsx")
#
#     # result=match_fund_name("华夏")
#     # print(result)
#
#     # print(len(result.values.tolist()))
# # 输出结果
#
# # result.to_excel("search_result.xlsx", index=False)
# main()
# end_time = time.time()
# print(end_time - start_time)


