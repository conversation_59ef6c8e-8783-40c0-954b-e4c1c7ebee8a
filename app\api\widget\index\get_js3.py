#获取指定网址的数据
import requests
url="https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37106903001718821398_1742017746552&fs=m%3A10&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf5%2Cf6%2Cf108%2Cf161%2Cf330%2Cf162%2Cf163%2Cf28%2Cf17&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&_=1742017746556"
     # url=")https://futsseapi.eastmoney.com/list/225?callbackName=jQuery37107993860468508374_1741916700365&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741916700389"
# url="https://futsseapi.eastmoney.com/list/220?callbackName=jQuery37109328292741254196_1741926939240&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741926939250"
# url="https://futsseapi.eastmoney.com/list/114?callbackName=jQuery371037809900008308706_1741927713413&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741927713417"
# data=requests.get(url)
# with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\js.txt","w",encoding="utf8")as f:
#     f.write(data.text)
data_list=[]
with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\js.txt", "r", encoding="utf8")as f:
    data1=f.read()
dict_m=eval(data1.strip("jQuery37106903001718821398_1742017746552(").replace(");", ""))['data']['diff']
#page=eval(data1.strip("jjQuery37106903001718821398_1742017746552(").replace(");", ""))['data']['total']
# print(dict_m)
def trans_num(num):
    if num < 10000:
        return str(round(num, 2))  # 保留2位小数
    elif 10000 <= num < 100_000_000:  # 1万到1亿
        simplified = round(num / 10_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}万"
    elif 100_000_000 <= num < 1_000_000_000_000:  # 1亿到10000亿
        simplified = round(num / 100_000_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}亿"
    else:  # 大于10000亿
        simplified = round(num / 1_000_000_000_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}万亿"
for i in dict_m:
    data_list.append(list(i.values()))
print(data_list)

def t1():
    r_data = []
    for i in data_list:
        r_data.append([
            i[6] #代码
            ,i[8],#名称
            "{:.4f}".format(i[1]/10000),#最新价
            "{:.4f}".format(i[3]/10000),#涨跌额
            "{:.2f}%".format(i[2]/100),#涨跌幅
            trans_num(i[4]),#成交量
            trans_num(i[5]),#成交额
            trans_num(i[-6]),#持仓量
            "{:.3f}".format(i[-4]/1000),#行权价
            i[-3],#剩余日
            i[-2],#日增
            "{:.4f}".format(i[-7]/10000),#昨结
            "{:.4f}".format(i[-8]/10000)])#昨结
    return r_data
r_data=t1()
print(r_data)
rows=len(r_data)
cols=len(r_data[0])
print(rows,cols)
for row in range(rows):
    for col in range(cols):
        print(r_data[row][col],end=",")
    print()