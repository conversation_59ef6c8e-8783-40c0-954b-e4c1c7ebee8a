from PIL import Image
import mss

# 定义要截取的区域 (left, top, width, height)
monitor = {
    "top": 244,    # 距离屏幕顶部的像素
    "left": 863,   # 距离屏幕左侧的像素
    "width": 300,  # 区域宽度
    "height": 200  # 区域高度
}

with mss.mss() as sct:
    # 截取指定区域
    screenshot = sct.grab(monitor)
    # 转换为 PIL Image 对象
    img = Image.frombytes("RGB", screenshot.size, screenshot.rgb)
    img.save("mss_screenshot.png")  # 保存截图

