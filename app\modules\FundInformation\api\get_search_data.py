import json
import requests
from PyQt6.QtCore import QThread, pyqtSignal


class get_search_data(QThread):
    finished = pyqtSignal(list, list, list,list)

    def __init__(self):
        super().__init__()
        self.url = "https://searchadapter.eastmoney.com/api/HotKeyword/GetBatch?cb=jQuery35109401063130308724_1743079285885&count=20&stockCount=30&token=D43BF722C8E33BDC906FB84D85E326E8&_=1743079285887"

    def request_data(self, ):
        response = requests.get(self.url,verify=False).text
        data = json.loads(response.strip("jQuery35109401063130308724_1743079285885(").replace(")", ""))
        Currency = data["Data"]["Currency"]
        Stock = data["Data"]["Stock"]
        Currency_l = []
        Stock_l = []
        Currency_or = []
        Stock_or = []
        n, m = 1, 1
        for i in Currency:
            Currency_l.append(str(n) + "." + i.get("KeyPhrase"))
            Currency_or.append(i.get("KeyPhrase"))
            n += 1
        for j in Stock:
            Stock_l.append(str(m) + "." + j.get("Name") + "[" + j.get("Code") + "]")
            Stock_or.append(j.get("Name"))
            m += 1
        return Currency_l, Currency_or, Stock_l, Stock_or

    def run(self):
        try:
            Currency_l, Currency_or, Stock_l, Stock_or= self.request_data()
            self.finished.emit(Currency_l, Currency_or, Stock_l, Stock_or)
        except Exception as e:
            print(f"search_data error:{e}")
#获取热搜和热搜股票
# search_data = search_data()
# print(search_data.request_data())

