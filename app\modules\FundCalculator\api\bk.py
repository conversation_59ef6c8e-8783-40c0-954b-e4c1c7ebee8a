import json

import requests

url="https://stock.finance.sina.com.cn/forex/api/openapi.php/ForexService.getAllBankForex?ft=part&callback=jQuery18208467864580261412_1744801166110&_=*************"
response=requests.get(url,verify=False,)
response_text=response.text
print(response_text)
print(response_text.strip("jQuery18208467864580261412_1744801166110(").replace(")","").replace("/*<script>location.href='//sina.com';</script>*/",""))

data=json.loads(response_text.replace("jQuery18208467864580261412_1744801166110(","").replace(")","").replace("/*<script>location.href='//sina.com';</script>*/",""))
bank_data=data["result"]["data"]["icbc"]
print(bank_data)