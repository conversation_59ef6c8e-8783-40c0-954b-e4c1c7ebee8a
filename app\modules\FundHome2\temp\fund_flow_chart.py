import sys
import json
import os
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtCore import QUrl, QFile, QIODevice
from PyQt6.QtGui import QIcon

# 导入你的数据获取函数
from t3 import request_data

class FundFlowChartWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle('实时资金流向图表')
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央widget和布局
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建WebEngineView
        self.browser = QWebEngineView()
        layout.addWidget(self.browser)
        
        # 加载图表
        self.load_chart()
    
    def load_chart(self):
        """加载图表HTML模板并注入数据"""
        # 获取数据
        time_list, main_net_flow, super_large_net_flow, large_net_flow, medium_net_flow, small_net_flow = request_data()
        
        if not time_list:
            print("获取数据失败")
            return
        
        # 准备图表数据
        chart_data = {
            "time_labels": time_list,
            "datasets": [
                {"name": "主力净流入", "data": main_net_flow, "color": "#FF6384"},
                {"name": "超大单净流入", "data": super_large_net_flow, "color": "#36A2EB"},
                {"name": "大单净流入", "data": large_net_flow, "color": "#FFCE56"},
                {"name": "中单净流入", "data": medium_net_flow, "color": "#4BC0C0"},
                {"name": "小单净流入", "data": small_net_flow, "color": "#9966FF"}
            ]
        }
        
        # 读取HTML模板文件
        template_path = os.path.join(os.path.dirname(__file__), "fund_flow_template.html")
        html_file = QFile(template_path)
        
        if html_file.open(QIODevice.OpenModeFlag.ReadOnly | QIODevice.OpenModeFlag.Text):
            html_content = html_file.readAll().data().decode('utf-8')
            html_file.close()
            
            # 注入数据
            html_content = html_content.replace(
                '/*DATA_PLACEHOLDER*/',
                f'const chartData = {json.dumps(chart_data)};'
            )
            
            # 加载HTML到WebEngineView
            self.browser.setHtml(html_content, QUrl.fromLocalFile(""))
            print(f"图表加载完成，共{len(time_list)}个数据点")
        else:
            print(f"无法读取模板文件: {template_path}")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = FundFlowChartWindow()
    window.show()
    sys.exit(app.exec())
