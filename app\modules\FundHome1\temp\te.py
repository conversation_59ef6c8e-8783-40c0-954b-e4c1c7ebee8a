import json

import requests

header = {
      "Host": "push2.eastmoney.com",
      "Connection": "keep-alive",
      "sec-ch-ua-platform": "\"Windows\"",
      "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
      "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
      "sec-ch-ua-mobile": "?0",
      "Accept": "*/*",
      "Sec-Fetch-Site": "same-site",
      "Sec-Fetch-Mode": "no-cors",
      "Sec-Fetch-Dest": "script",
      "Referer": "https://quote.eastmoney.com/center/hsbk.html",
      "Accept-Encoding": "gzip, deflate, br, zstd",
      "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
      "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; _qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; EMFUND4=null; EMFUND5=null; st_si=95422376668622; fullscreengg=1; fullscreengg2=1; websitepoptg_api_time=1754146049931; st_asi=delete; EMFUND0=null; EMFUND6=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND7=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND8=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND9=08-03 18:14:19@#$%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=103; st_psi=20250803200144542-113200313002-2210851681; _qimei_i_1=5dc466fed132"
    }
url = "https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery3710556403952883711_1754215838619&fs=m%3A90&fields=f12%2Cf13%2Cf14%2Cf3%2Cf152%2Cf4%2Cf128%2Cf140%2Cf141%2Cf136&fid=f3&pn=1&pz=10&po=1&ut=fa5fd1943c7b386f172d6893dbfba10b&dect=1&wbp2u=%7C0%7C0%7C0%7Cweb&_=1754215838620"


def request_data():
    try:
        response = requests.get(url, headers=header, verify=False)

        # 去除JSONP包装，提取JSON数据
        json_str = response.text.strip("jQuery3710556403952883711_1754215838619(").rstrip(");")
        data = json.loads(json_str)

        # 初始化四个列表
        concept_code = []
        concept_name = []
        concept_value = []
        concept_stock = []

        # 解析diff数据
        diff_list = data["data"]["diff"]
        # print(diff_list)

        for item in diff_list:
            concept_code.append(str(item["f12"]))  # 概念代码
            concept_name.append(str(item["f14"]))  # 概念名称
            concept_value.append(str(item["f3"] / 100) + "%")  # 概念涨幅，小数点向前移动2位
            concept_stock.append(str(item["f140"]))  # 概念股

        return concept_code, concept_name, concept_value, concept_stock

    except Exception as e:
        print(f"请求数据失败: {e}")
        return [], [], [], []
print(request_data())