import os

class DataHandle():
    # 类变量
    history_file = os.path.join(os.path.dirname(__file__), "src/fund_history.txt")
    max_history = 10

    @staticmethod
    def add_fund_to_history(fund_name):
        """添加基金到浏览历史"""
        try:
            # 读取现有历史记录
            history_list = DataHandle.get_fund_history()

            # 如果基金已存在，先移除
            if fund_name in history_list:
                history_list.remove(fund_name)

            # 将新基金添加到列表开头
            history_list.insert(0, fund_name)

            # 保持最多10条记录
            history_list = history_list[:DataHandle.max_history]

            # 写入文件
            with open(DataHandle.history_file, "w", encoding="utf-8") as f:
                for fund in history_list:
                    f.write(fund + "\n")

        except Exception as e:
            print(f"添加基金历史记录失败: {e}")

    @staticmethod
    def get_fund_history():
        """获取基金浏览历史，返回前8条完整记录"""
        try:
            if not os.path.exists(DataHandle.history_file):
                return []

            with open(DataHandle.history_file, "r", encoding="utf-8") as f:
                history_list = [line.strip() for line in f.readlines() if line.strip()]

            # 限制最多8条记录，返回完整名称
            return history_list[:8]

        except Exception as e:
            print(f"读取基金历史记录失败: {e}")
            return []

    @staticmethod
    def get_fund_history_for_display():
        """获取用于显示的基金历史记录（截断长名称）"""
        try:
            full_history = DataHandle.get_fund_history()

            # 格式化列表元素：长度>10的截取前10个字符并添加...，否则保持原样
            formatted_list = []
            for item in full_history:
                if len(item) > 10:
                    formatted_list.append(item[:10] + "...")
                else:
                    formatted_list.append(item)

            return formatted_list

        except Exception as e:
            print(f"读取基金历史记录失败: {e}")
            return []

    @staticmethod
    def clear_fund_history():
        """清空基金历史记录"""
        try:
            # 清空文件内容
            with open(DataHandle.history_file, "w", encoding="utf-8") as f:
                f.write("")  # 写入空内容

            print("基金历史记录已清空")
            return True

        except Exception as e:
            print(f"清空基金历史记录失败: {e}")
            return False

    @staticmethod
    def get_search_fund(search_word):
        """根据参数匹配基金"""
        with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\code_js", "r", encoding="utf8") as f:
            data_1 = f.read()
        data_2 = data_1.replace("var r = ", "").replace("[[", "").replace("]]", "").replace('"', "")
        data = []
        data_l = []
        keyword = 0
        # if search_word=="":
        #     keyword=2

        if search_word.isdigit():  # 根据代码搜索
            keyword = 0
        elif search_word.isupper():  # 根据大写简称搜索
            keyword = 1
        elif '\u4e00' <= search_word<= '\u9fff':  # 根据名称模糊搜索
            keyword = 2
        for i in data_2.split("],["):
            if i.split(",")[keyword].__contains__(str(search_word)):
                data.append(i)
        len_match=len(data)
        if len(data) >= 11:
            len_data_l = 10
        else:
            len_data_l = len(data)
        for i in data[:len_data_l]:
            data_l.append(i.split(","))
        return data_l,len_match

    @staticmethod
    def zs_data_trans(data_list):
        """将整数列表除以100并保留2位小数，返回文本列表"""
        result = []
        for num in data_list:
            # 除以100并保留2位小数，转为字符串
            formatted_num = "{:.2f}".format(num / 100)
            result.append(formatted_num)
        return result

    @staticmethod
    def zs_data_color(zf_list):
        res=[]
        for i in zf_list:
            if "-" in i:
                res.append("green")
            elif i=="0":
                res.append("black")
            else:
                res.append("red")
        return res

    #栏宽度设定
    @staticmethod
    def zs_color_width(up,down,middle):
        """计算宽度的对应比例"""
        total = up + down + middle
        if total == 0:
            return [0, 0, 0]
        
        # 计算各自比例并乘以723
        up_width = int((up / total) * 723)
        down_width = int((down / total) * 723)
        middle_width = int((middle / total) * 723)
        return [up_width, down_width, middle_width]

DataHandle=DataHandle()
