<!DOCTYPE html>
<html style="height: 100%">

<head>
    <meta charset="utf-8">
    <title>ECharts Example</title>
    <!-- 引入 ECharts -->
    <script src="echarts.min.js"></script>
    <script type="text/javascript">
        if (typeof echarts === 'undefined') {
            alert('ECharts 加载失败');
        }
    </script>
</head>

<body style="height: 100%; margin: 0">
    <div id="main" style="height: 100%"></div>
    <h1>xu</h1>

    <script type="text/javascript">
        function initChart(data) {
            // 基于准备好的 DOM，初始化 echarts 实例
            myChart = echarts.init(document.getElementById('main'));
            myChart.setOption(data);
        }
    </script>
</body>

</html>