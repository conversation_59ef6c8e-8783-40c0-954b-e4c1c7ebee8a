from app.modules.FundHome3.api.get_gsy_data import get_gsy_data
from app.modules.FundHome3.api.get_hot_subject import get_hot_subject
from main_w import Ui_MainWindow
from app.modules.FundHome3.api.get_zqx_data import get_zqx_data

class FundHome3:
    def __init__(self, ui: Ui_MainWindow):
        self.ui = ui
        self.init_setup()


    def init_setup(self):
        pass
        # self.load_gsy_data()
        # self.load_zqx_data()
        # self.load_hot_subject()
        #TODO 基金详情跳转，
        #TODO 主题基金跳转

    def load_gsy_data(self):
        self.worker_thread_1 = get_gsy_data()
        self.worker_thread_1.finished.connect(self.task_finished_gsy_data)
        self.worker_thread_1.start()

    def task_finished_gsy_data(self,gsy_fund_code, gsy_fund_name, gsy_fund_value):
        for i in range(1,4):
            if len(gsy_fund_name[i-1])>16:
                getattr(self.ui,f"gsy_name_{i}").setText(gsy_fund_name[i-1][:15]+"...")
            else:
                getattr(self.ui, f"gsy_name_{i}").setText(gsy_fund_name[i - 1])
            getattr(self.ui,f"gsy_value_{i}").setText(f"{gsy_fund_value[i-1]}%")

    def load_zqx_data(self):

        self.worker_thread_2 = get_zqx_data()
        self.worker_thread_2.finished.connect(self.task_finished_zqx_data)
        self.worker_thread_2.start()

    def task_finished_zqx_data(self,zqx_fund_code, zqx_fund_name, zqx_fund_value):
        for i in range(1,4):
            if len(zqx_fund_name[i-1])>16:
                getattr(self.ui,f"zqx_name_{i}").setText(zqx_fund_name[i-1][:15]+"...")
            else:
                getattr(self.ui, f"zqx_name_{i}").setText(zqx_fund_name[i - 1])
            getattr(self.ui,f"zqx_value_{i}").setText(f"{zqx_fund_value[i-1]}%")

    def load_hot_subject(self):
        try:
            self.worker_thread_3 = get_hot_subject()
            self.worker_thread_3.finished.connect(self.task_finished_subject)
            self.worker_thread_3.start()
        except Exception as e:
            print(e)

    def task_finished_subject(self,subject_code,subject_name,subject_value):
        print(subject_code)
        for i in range(1,6):
            getattr(self.ui, f"rm_subject_name_{i}").setText(subject_name[i - 1])
            getattr(self.ui,f"rm_subject_value_{i}").setText(f"{subject_value[i-1]}%")

