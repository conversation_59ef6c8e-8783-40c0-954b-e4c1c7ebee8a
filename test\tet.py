import json
import re

import requests
from bs4 import BeautifulSoup
# url="https://fund.eastmoney.com/a/cjjtzcl_1.html"
keyword="格尔"
page=1
date=[]
url=[]
title=[]
content=[]
mediaName=[]


base_url=f'https://search-api-web.eastmoney.com/search/jsonp?cb=jQuery35108729933498276836_1743862416778&param=%7B%22uid%22%3A%22%22%2C%22keyword%22%3A%22{keyword}%22%2C%22type%22%3A%5B%22cmsArticleWebOld%22%5D%2C%22client%22%3A%22web%22%2C%22clientType%22%3A%22web%22%2C%22clientVersion%22%3A%22curr%22%2C%22param%22%3A%7B%22cmsArticleWebOld%22%3A%7B%22searchScope%22%3A%22default%22%2C%22sort%22%3A%22default%22%2C%22pageIndex%22%3A{page}%2C%22pageSize%22%3A10%2C%22preTag%22%3A%22%3Cem%3E%22%2C%22postTag%22%3A%22%3C%2Fem%3E%22%7D%7D%7D&_=1743862416779'
data=requests.get(base_url).text
# data = requests.get(url,verify=False).text
data=json.loads(data.strip("jQuery35108729933498276836_1743862416778(").replace(")",""))
for i in data["result"]["cmsArticleWebOld"]:
    date.append(i["date"])
    url.append(i["url"])
    title.append(i["title"])
    content.append(i["content"])
    mediaName.append(["mediaName"])

red_style=f"""<span style="color:red"><strong>{keyword}</strong></span>"""
gray_style_start=f"""<span style="color:gray">"""
gray_style_end=f"""&nbsp;-&nbsp;</span>"""


#标题处理
title_=[i.replace(f"<em>{keyword}</em>",red_style) for i in title]
content_=[i.replace(f"<em>{keyword}</em>",red_style) for i in content]
content_r=[]
date_=[gray_style_start+i+gray_style_end for i in date]
for i in range(10):
    content_r.append(date_[i]+content_[i])

print(title_)
print(content_r)
# print(date_)


















# s = "人民财讯4月3日电，<em>歌尔股份</em>今日跌停，成交额41.33亿元，换手率5.62%，盘后龙虎榜数据显示，深股通专用席位买<em>歌尔股份</em>入1.67亿元并卖出2.32亿元，二机构专......"
# l=s.replace("""<em>歌尔股份</em>""","""<span style="color:red"><strong>歌尔股份</strong></span>""")
# s.replace("3","4")
# l=s.split("em")
# print(l)
# l_s = """span style="color:red"><strong"""
# r_e = """strong></span"""
# l.insert(1,l_s)
# l.insert(-1,r_e)
# for i in l:
#     print(i,end="")
# print(l)


