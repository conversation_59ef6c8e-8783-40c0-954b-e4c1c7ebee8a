import sys
import os

import requests
import json
import tushare as ts
import pandas as pd
# url="https://push2his.eastmoney.com/api/qt/stock/kline/get?cb=jQuery35101085796900623961_1741149660730&secid=0.399001&ut=fa5fd1943c7b386f172d6893dbfba10b&fields1=f1%2Cf2%2Cf3%2Cf4%2Cf5%2Cf6&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61&klt=101&fqt=1&end=20500101&lmt=1000000&_=1741149660789"
# response = requests.get(url)
# with open("kva.txt","w",encoding="utf8")as f:
#     f.write(response.text)
with open("../../txt/kva.txt", "r", encoding="utf8")as f:
    data_r=f.read()
data_1=eval(data_r.strip("jQuery35101085796900623961_1741149660730(").replace(");",""))
data_2=data_1["data"]["klines"]
data_2_time=[]
data_2_now=[]
data_2_yes=[]
data_2_max=[]
data_2_min=[]
for i in data_2:
    data_2_time.append(i.split(",")[0])
    data_2_now.append(float(i.split(",")[1]))
    data_2_yes.append(float(i.split(",")[2]))
    data_2_max.append(float(i.split(",")[3]))
    data_2_min.append(float(i.split(",")[4]))
data_f={'dates':data_2_time,'open':data_2_now, 'close':data_2_yes, 'high':data_2_max, 'low':data_2_min}



def get_stock_data():
    pass
    # 获取所需数据的df,代码，日k
    df =pd.DataFrame(data_f)
    #
    # print(df)

    # 将日期设置为索引并按时间排序
    df.index = pd.to_datetime(df['dates'])
    df = df.sort_index()

    #
    # 提取需要的列（日期、开盘、收盘、最高、最低）
    stock_data = df[['open', 'close', 'high', 'low']]

    #
    # # 生成 K 线图所需的数组
    kline_data = []
    for row in stock_data.itertuples():
        kline_data.append([row.open, row.close, row.low, row.high])
    # print(kline_data)
    #
    # # 日期
    dates = stock_data.index.strftime('%Y-%m-%d').tolist()
    print(stock_data)
    # dates=df['datetime']

    #生成关键点
    point_data = [{
        "name": 'highest value',
        "type": 'max',
        "valueDim": 'highest'
    },
        {
            "name": 'lowest value',
            "type": 'min',
            "valueDim": 'lowest'
        }]

    #生成平均值
    # markLine = self.get_markline_data(stock_data)

    return {
        'dates': dates,
        'kline_data': kline_data,
        'point_data': point_data,
        # 'markLine': markLine,
    }

print(get_stock_data())