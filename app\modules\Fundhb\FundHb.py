import time
from functools import partial

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QHeaderView, QTableWidgetItem, QMessageBox, \
    QLineEdit

from app.modules.Fundhb.api.get_hbjj_data import get_hbjj_data
from app.modules.Fundhb.api.jj_company_code import get_jj_company
from app.modules.Fundhb.src import style
from main_w import Ui_MainWindow



# 货币基金
class FundHb:
    def __init__(self, ui: Ui_MainWindow):
        self.ui = ui
        self.init_setup()

    def init_setup(self):
        self.show_hbjj_status = False
        self.ui.hbjj_widget.hide()
        self.ui.top_img_3.mousePressEvent = self.show_hbjj_widget
        self.ui.top_img_label_3.mousePressEvent = self.show_hbjj_widget
        self.ui.hbjj_return_lb_2.mousePressEvent = self.toggle_hbjj_index



    def show_hbjj_widget(self, event):
        try:
            self.hbjj_intCompany = "0"
            self.hbjj_MinsgType = ""
            self.show_hbjj_status = True
            self.show_hbjj_company_status = False

            self.ui.hbjj_company_listWidget_2.hide()
            self.MinsgType_filter = ["", "", "", "", "", "a", "b"]  # 全部,100万以下，100万起
            self.load_hbjj_data(intCompany=self.hbjj_intCompany, MinsgType=self.hbjj_MinsgType, strSortCol="SYL_1N",
                                orderType="desc")

            for i in range(4, 7):
                getattr(self.ui, f"hbjj_filter_lb_{i}").mousePressEvent = partial(self.hbjj_filter_index, index=i - 1)

            self.ui.hbjj_filter_lb_4.setStyleSheet(style.default_filter_style)#默认 全部

            self.ui.hbjj_select_lineEdit_2.mousePressEvent = self.custom_mouse_press_hbjj
            self.ui.hbjj_company_listWidget_2.itemClicked.connect(self.select_hbjj_code)
            self.ui.hbjj_reset_btn_2.mousePressEvent = self.hbjj_filter_reset
        except Exception as e:
            print(e)

    def hbjj_filter_reset(self, event):
        self.hbjj_intCompany = "0"
        self.hbjj_MinsgType = ""
        self.load_hbjj_data(intCompany=self.hbjj_intCompany, MinsgType=self.hbjj_MinsgType, strSortCol="SYL_1N",
                            orderType="desc")
        for i in range(4, 7):
            getattr(self.ui, f"hbjj_filter_lb_{i}").setStyleSheet(style.none_filter_style)
        self.ui.hbjj_filter_lb_4.setStyleSheet(style.default_filter_style)
        self.ui.hbjj_select_lineEdit_2.clear()
        self.ui.hbjj_company_listWidget_2.hide()

    def custom_mouse_press_hbjj(self, event):
        try:
            super(QLineEdit, self.ui.hbjj_select_lineEdit_2).mousePressEvent(event)  # 调用父类方法，确保正常行为
            self.show_hbjj_listWidget()
        except Exception as e:
            print(e)

    def show_hbjj_listWidget(self, ):
        try:
            if not self.show_hbjj_company_status:
                # get_jj_company()#获取基金公司数据
                self.show_hbjj_company_status = True
            self.ui.hbjj_select_lineEdit_2.textChanged.connect(self.get_hbjj_company)
            self.ui.hbjj_company_listWidget_2.show()
            self.get_hbjj_company("")
            self.ui.hbjj_select_lineEdit_2.clear()
        except Exception as e:
            print(e)

    def hbjj_filter_index(self, event, index):
        for i in range(4, 7):
            getattr(self.ui, f"hbjj_filter_lb_{i}").setStyleSheet(style.none_filter_style)
        self.hbjj_MinsgType = self.MinsgType_filter[index]
        getattr(self.ui, f"hbjj_filter_lb_{index + 1}").setStyleSheet(style.default_filter_style)
        self.hbjj_MinsgType = self.MinsgType_filter[index]
        self.load_hbjj_data(intCompany=self.hbjj_intCompany, MinsgType=self.hbjj_MinsgType, strSortCol="SYL_1N",
                            orderType="desc")

    def get_hbjj_company(self, text):
        try:
            self.ui.hbjj_company_listWidget_2.clear()
            data_list = get_jj_company.check_query(query=text)
            for i in data_list:
                self.ui.hbjj_company_listWidget_2.addItem(i)
        except Exception as e:
            print(e)

    def select_hbjj_code(self, item):
        try:
            item_name = item.text()
            self.hbjj_intCompany = item_name.split("[")[1].strip("]")
            self.load_hbjj_data(intCompany=self.hbjj_intCompany, MinsgType=self.hbjj_MinsgType, strSortCol="SYL_1N",
                                orderType="desc")
            self.ui.hbjj_company_listWidget_2.hide()
            self.ui.hbjj_select_lineEdit_2.setText(item_name)
        except Exception as e:
            print(e)

    def toggle_hbjj_index(self, event):
        if self.show_hbjj_status:
            self.ui.hbjj_widget.hide()
            self.show_hbjj_status = False
        else:
            self.ui.hbjj_widget.show()
            self.show_hbjj_status = True

    def load_hbjj_data(self, intCompany, MinsgType, strSortCol, orderType):
        try:
            self.hbjj_st = time.time()
            self.ui.hbjj_widget.show()
            self.ui.hbjj_load_lb_2.setText("正在加载")
            self.worker_thread_hbjj = get_hbjj_data(intCompany=intCompany, MinsgType=MinsgType, strSortCol=strSortCol,
                                                    orderType=orderType)
            self.worker_thread_hbjj.finished.connect(self.task_finished_hbjj)
            self.worker_thread_hbjj.start()
        except Exception as e:
            print(e)

    def task_finished_hbjj(self, hbjj_data_list):
        self.hbjj_et = time.time()
        self.ui.hbjj_load_lb_2.setText(f"加载完成，本次耗时{self.hbjj_et - self.hbjj_st:.2f}s")
        self.load_hbjj_table(hbjj_data_list)

    def load_hbjj_table(self, data):
        headers = ["基金代码", "基金简称", "日期", "万份收益", "7日年化",
                   "14日年化", "28日年化", "近1月", "3月", "近6月", "近1年", "近2年", "近3年", "近5年", "今年来",
                   "成立来"]
        self.ui.hbjj_tableWidget_2.setColumnCount(len(headers))
        self.ui.hbjj_tableWidget_2.setRowCount(len(data))
        self.ui.hbjj_tableWidget_2.setHorizontalHeaderLabels(headers)
        for row in range(len(data)):
            for col in range(len(headers)):
                item = QTableWidgetItem(f"{data[row][col]}")
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                if col in [0, 1]:
                    item.setForeground(QColor("#294EBC"))
                elif col in range(7,16):
                    if data[row][col] != "--":
                        item.setForeground(QColor("#FF0018"))
                self.ui.hbjj_tableWidget_2.setItem(row, col, item)
        table_width = self.ui.hbjj_tableWidget_2.width() - 20  # 获取表格宽度
        column_count = self.ui.hbjj_tableWidget_2.columnCount()  # 获取列数
        if column_count > 0:
            column_width = table_width // column_count  # 计算每列的宽度
            for col in range(column_count):
                self.ui.hbjj_tableWidget_2.setColumnWidth(col, column_width)
        self.ui.hbjj_tableWidget_2.horizontalHeader().setStyleSheet(style.QHeaderView_style)
