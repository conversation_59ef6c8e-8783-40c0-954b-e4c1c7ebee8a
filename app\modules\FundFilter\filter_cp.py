from functools import partial

from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton

from app.modules.FundFilter.DataHandle_filter import DataHandle_filter


from app.modules.FundFilter.src import style


class filter_cp:
    def __init__(self, ui):
        self.ui = ui
        self.init_filter_cp()

    def init_filter_cp(self):
        try:
            self.filter_cp_list = []
            self.captical_list = ['A', 'B', 'C', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'M',
                                  'N', 'P', 'Q', 'R', 'S', 'T', 'W', 'X', 'Y', 'Z']
            self.filter_cp_captical_list= []
            # 初始化字母标签
            for letter in self.captical_list:
                label = getattr(self.ui, f"filter_cp_{letter}")
                label.setStyleSheet(style.QLabel_ft_normal)
                # if letter in self.filter_cp_captical_set:
                #     label.setStyleSheet(style.QLabel_ft_selected)
                # 修正参数顺序：event参数必须放在第一位
                label.mousePressEvent = lambda event, l=letter: self.letter_clicked(event, l)
            self.ui.filter_cp_none.mousePressEvent = self.clear_filter_cp
            self.ui.filter_cp_none.setStyleSheet(style.QLabel_ft_selected)
            self.company_dict = DataHandle_filter.return_company_letter_dict()
            self.init_fund_display()
        except Exception as e:
            print(f"init_filter_cp error: {e}")

    def update_cp_letter_lb(self):
        self.filter_cp_captical_set = set(self.filter_cp_captical_list)
        for letter in self.captical_list:
            label = getattr(self.ui, f"filter_cp_{letter}")
            if letter in self.filter_cp_captical_set:
                label.setStyleSheet(style.QLabel_ft_selected)
            else:
                label.setStyleSheet(style.QLabel_ft_normal)

    def init_fund_display(self):
        """正确初始化基金显示区域"""
        # 确保滚动区域已正确设置
        self.scroll_content = QWidget()
        self.fund_layout = QHBoxLayout(self.scroll_content)
        self.fund_layout.setSpacing(3)
        self.fund_layout.setContentsMargins(2, 2, 2, 2)

        # 设置滚动区域策略
        self.ui.filter_scrollArea_cp.setWidgetResizable(True)
        self.ui.filter_scrollArea_cp.setWidget(self.scroll_content)

        # 连接滚动按钮
        self.ui.fiter_cp_left.mousePressEvent = self.scroll_left
        self.ui.fiter_cp_right.mousePressEvent = self.scroll_right

    def letter_clicked(self, event, letter):
        """修正后的字母点击事件"""
        try:
            # 更新选中状态
            self.current_letter = letter
            self.ui.filter_cp_none.setStyleSheet(style.QLabel_ft_normal)
            getattr(self.ui, f"filter_cp_{letter}").setStyleSheet(style.QLabel_cp_selected)
            self.show_funds_for_letter(letter)
            self.update_cp_letter_lb()
        except Exception as e:
            print(f"letter_clicked error: {e}")

    def show_funds_for_letter(self, letter):
        """修正后的基金显示方法"""
        try:
            # 清空现有按钮
            while self.fund_layout.count():
                item = self.fund_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            # 添加新按钮
            if letter in self.company_dict:
                for fund_code, fund_name in self.company_dict[letter]:
                    btn = QPushButton(fund_name)
                    btn.setCheckable(True)
                    btn.setFixedHeight(35)
                    if fund_name in self.filter_cp_list:
                        btn.setStyleSheet(style.QButton_cp_selected)
                        btn.clicked.connect(partial(self.fund_button_clicked, fund_name=fund_name, status=False))
                    else:
                        btn.setStyleSheet(style.QButton_cp_normal)
                        btn.clicked.connect(partial(self.fund_button_clicked, fund_name=fund_name,status=True))
                    self.fund_layout.addWidget(btn)
            self.fund_layout.addStretch()

            #添加后判断列表里是否含有已添加是否需要高亮

            # 强制更新UI
            self.scroll_content.adjustSize()
            self.ui.filter_scrollArea_cp.viewport().update()
        except Exception as e:
            print(f"show_funds_for_letter error: {e}")

    def fund_button_clicked(self, fund_name,status):
        try:
            sender = self.ui.jj_filter_widget.sender()
            if status:#添加
                if sender.isChecked():
                    self.sender_append_list(fund_name)
                else:
                    self.sender_remove_list(fund_name)
            else:
                if sender.isChecked():
                    self.sender_remove_list(fund_name)
                else:
                    self.sender_append_list(fund_name)
            print(self.filter_cp_list)
            self.update_cp_letter_lb()
            self.update_cp_none_status()
            print(self.filter_cp_captical_set)
            result = DataHandle_filter.return_all_filter_data(self.ui.filter_all_lb.toPlainText(), self.filter_cp_list,
                                                              "公司")
            self.ui.filter_all_lb.setPlainText(result)

        except Exception as e:
            print(f"fund_button_clicked error: {e}")

    def sender_append_list(self,fund_name):
        self.filter_cp_list.append(fund_name)
        self.filter_cp_captical_list.append(DataHandle_filter.get_pinyin_initial(fund_name))

    def sender_remove_list(self,fund_name):
        self.filter_cp_list.remove(fund_name)
        self.filter_cp_captical_list.remove(DataHandle_filter.get_pinyin_initial(fund_name))

    def scroll_left(self, event):
        """向左滚动"""
        scroll_bar = self.ui.filter_scrollArea_cp.horizontalScrollBar()
        scroll_bar.setValue(scroll_bar.value() - 100)

    def scroll_right(self, event):
        """向右滚动"""
        scroll_bar = self.ui.filter_scrollArea_cp.horizontalScrollBar()
        scroll_bar.setValue(scroll_bar.value() + 100)

    def clear_filter_cp(self, event):
        """清空筛选条件"""
        try:
            self.filter_cp_list = []
            self.ui.filter_cp_none.setStyleSheet(style.QLabel_ft_selected)

            # 重置字母标签样式
            for letter in self.captical_list:
                getattr(self.ui, f"filter_cp_{letter}").setStyleSheet(style.QLabel_ft_normal)

            # 清空基金显示
            while self.fund_layout.count():
                item = self.fund_layout.takeAt(0)
                if item.widget():
                    item.widget().deleteLater()
            result = DataHandle_filter.return_all_filter_data(self.ui.filter_all_lb.toPlainText(), self.filter_cp_list, "公司")
            self.ui.filter_all_lb.setPlainText(result)

        except Exception as e:
            print(f"clear_filter_cp error: {e}")

    def update_cp_none_status(self):
        if not self.filter_cp_list:  # 如果列表为空
            self.ui.filter_cp_none.setStyleSheet(style.QLabel_ft_selected)
        else:
            self.ui.filter_cp_none.setStyleSheet(style.QLabel_ft_normal)

    def return_cp_list(self):
        fund_name_list = self.filter_cp_list
        print(fund_name_list)
        return DataHandle_filter.return_fund_NameToCode(fund_name_list)


