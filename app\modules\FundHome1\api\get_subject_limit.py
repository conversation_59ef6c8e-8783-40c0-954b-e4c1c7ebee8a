
from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy
import json
class get_subject_limit(QThread):
    finished = pyqtSignal(list ,list)
    def __init__(self ,):
        super().__init__()
        self.header ={
  "Host": "api.fund.eastmoney.com",
  "Connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-site",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://fund.eastmoney.com/",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; _qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; EMFUND4=null; EMFUND5=null; EMFUND6=null; EMFUND0=null; EMFUND9=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND8=07-31 16:04:23@#$%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; st_si=95422376668622; st_asi=delete; fullscreengg=1; fullscreengg2=1; websitepoptg_api_time=1754146049931; _qimei_i_1=59c14be8eb21; FundWebTradeUserInfo=JTdCJTIyQ3VzdG9tZXJObyUyMjolMjIlMjIsJTIyQ3VzdG9tZXJOYW1lJTIyOiUyMiUyMiwlMjJWaXBMZXZlbCUyMjolMjIlMjIsJTIyTFRva2VuJTIyOiUyMiUyMiwlMjJJc1Zpc2l0b3IlMjI6JTIyJTIyLCUyMlJpc2slMjI6JTIyJTIyLCUyMlN1cnZleURheSUyMjowLCUyMklzQXVkaXROZWVkUG9wJTIyOnRydWUlN0Q%3D; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=71; st_psi=2025080311490194-112200304021-6386614630"
}
        self.url ="https://api.fund.eastmoney.com//ztjj/GetZTJJListNew?callback=jQuery18302804660792210313_1754192965021&tt=0&dt=syl&st=D&_=1754192965035"


    def request_data(self):
        try:
            response = requests.get(self.url, headers=self.header, verify=False)
            
            # 去除JSONP包装，提取JSON数据
            json_str = response.text.strip("jQuery18302804660792210313_1754192965021(").rstrip(");")
            data = json.loads(json_str)
            
            # 获取数据列表
            data_list = data["Data"]
            
            # 获取前4个和后4个元素
            selected_items = data_list[:4] + data_list[-4:]
            
            subject_name = []
            subject_value = []
            
            for item in selected_items:
                subject_name.append(item["INDEXNAME"])
                
                # 格式化D值
                d_value = item["D"]
                if d_value > 0:
                    formatted_value = f"+{d_value:.2f}%"
                elif d_value < 0:
                    formatted_value = f"{d_value:.2f}%"
                else:
                    formatted_value = f"{0.00}%"
                
                subject_value.append(formatted_value)
            
            return subject_name, subject_value
        
        except Exception as e:
            print(f"请求数据失败: {e}")
            return [], []



    def run(self):
        try:
            subject_name, subject_value =self.request_data()
            self.finished.emit(subject_name, subject_value)
        except Exception as e:
            print(f"Error in run method: {e}")




