#获取指定网址的数据
import requests
url="https://futsseapi.eastmoney.com/list/115?callbackName=jQuery37101231601946064611_1741912262085&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741912262090"
url="https://futsseapi.eastmoney.com/list/225?callbackName=jQuery37107993860468508374_1741916700365&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741916700389"
url="https://futsseapi.eastmoney.com/list/220?callbackName=jQuery37109328292741254196_1741926939240&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741926939250"
url="https://futsseapi.eastmoney.com/list/114?callbackName=jQuery371037809900008308706_1741927713413&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741927713417"
data=requests.get(url).text
# with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\js.txt","w",encoding="utf8")as f:
#     f.write(data.text)
# data_list=[]
# with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\js.txt", "r", encoding="utf8")as f:
#     data1=f.read()
dict_m=eval(data.strip("jQuery37101231601946064611_1741912262085(").replace(")", ""))['total']
print(dict_m)
def trans_num(num):
    if num < 10000:
        return str(round(num, 2))  # 保留2位小数
    elif 10000 <= num < 100_000_000:  # 1万到1亿
        simplified = round(num / 10_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}万"
    elif 100_000_000 <= num < 1_000_000_000_000:  # 1亿到10000亿
        simplified = round(num / 100_000_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}亿"
    else:  # 大于10000亿
        simplified = round(num / 1_000_000_000_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}万亿"
# for i in dict_m:
#     data_list.append(list(i.values()))
# print(data_list)
# #
# # def t1():
# #     r_data = []
# #     for i in data_list:
# #         r_data.append([i[6],i[8],"{:.2f}".format(i[1]/100),"{:.2f}".format(i[3]/100),"{:.2f}%".format(i[2]/100),trans_num(i[4]),trans_num(i[5]),"{:.2f}".format(i[12]/100),"{:.2f}".format(i[11]/100),"{:.2f}".format(i[9]/100),"{:.2f}".format(i[10]/100)])
# #         return r_data
# def t2():
#     r_data = []
#     for i in data_list:
#         r_data.append(
#             [i[2],#代码
#             i[10],#名称
#             "{:.1f}".format(i[7]),#最新价
#              "{:.1f}".format(i[12]),#涨跌额
#              "{:.2f}%".format(i[13]),#涨跌幅
#              "{:.1f}".format(i[6]),#今开
#              "{:.1f}".format(i[1] ),#最高
#              "{:.1f}".format(i[4] ),#最低
#              "{:.1f}".format(i[-2] ),#昨结
#              trans_num(i[9]), #成交量
#              trans_num(i[-1]), #成交额
#              "{}".format(int(i[11]) ),#买盘
#              "{}".format(int(i[0]) ),#卖盘
#              "{}".format(int(i[5]) )])#持仓量
#
#     return r_data
# r_data=t2()
# rows=len(r_data)
# cols=len(r_data[0])
# print(rows,cols)
# for row in range(rows):
#     for col in range(cols):
#         print(r_data[row][col],end=",")
#     print()