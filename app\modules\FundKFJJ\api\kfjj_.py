from PyQt6.QtNetwork import *
from PyQt6.QtMultimedia import *
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
# from PyQt6.QtWebEngineCore import QWebEnginePage
from PyQt6.QtCore import QThread, pyqtSignal

class CustomDelegate(QStyledItemDelegate):
    def initStyleOption(self, option, index):
        super().initStyleOption(option, index)
        option.displayAlignment = Qt.AlignmentFlag.AlignCenter  # 设置文本水平居中

    def paint(self, painter, option, index):
        data = index.data(Qt.ItemDataRole.DisplayRole)
        if index.column() in [0,1]:
            option.palette.setColor(QPalette.ColorRole.Text, QColor("#003497"))
            super().paint(painter, option, index)
        # 处理第 5-15 列和第 17 列的数据
        elif 4 < index.column() <= 15 or index.column() == 17:
            if data == "-":
                # 如果数据是 "-"，不做任何操作
                super().paint(painter, option, index)
            elif "%" in data:
                value = float(data.strip("%"))
                option.palette.setColor(QPalette.ColorRole.Text, QColor("#DC0000"))  if value > 0 else option.palette.setColor(QPalette.ColorRole.Text, QColor("#097C4C"))  # 负数设置为绿色
        super().paint(painter, option, index)
