import ast

import requests
from urllib.parse import urlencode
import random
import json
header={
    "Cookie":'qgqp_b_id=50d74afee65419b05e9120f0df53c69f; fund_registerAd_1=1; st_si=18639092801927; st_asi=delete; ASP.NET_SessionId=iurcwwhnaijp1l0zlgb2mjnn; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=2; st_psi=20250319095514921-112200312936-9443065310',
    "Referer":"https://fund.eastmoney.com/data/fundranking.html"
}
base_url="https://fund.eastmoney.com/data/rankhandler.aspx?"
# 参数字典
params = {
    "op": "ph",#排行
    "dt": "kf",#开放基金
    "ft":"zs",#fund_type类型：全部，指数，股票，混合，债券，qdii,fof ["all","zs","gp","hh","zq","qdii","fof"]
    "rs":"",
    "gs":0,
    "sc":"1nzf",#排序依据 基金代码 基金简称， 日期，单位净值，累计净值，且增长率，近1周，近1月，近3月，近6月，近1年，近2年，近3年，今年来，成立来，自定义
    #["dm","jc","jzrq","dwjz","ljjz","rzdf","zzf","1yzf","3yzf","6yzf","1nzf","2nzf","3nzf","jnzf","lnzf","qjzf"]
    "st":"desc",#升序，降序["desc","asc"]
    "sd":"2024-03-19",#sc="qjzf" sd起始日期
    "ed":"2025-03-19",#sc="qjzf" ed结束日期
    "qdii":"%7C",#所有默认为空
    #指数，债券，qdii，
    #指数{"沪深指数":"053|","行业主题":"054|","大盘指数":"01|","中小盘指数":"02,03|","股票指数":"001|","债券指数":"003|","海外指数":"000003|"}
    #债券{"长期纯债":"041|","短期纯债":"042|","混合债基":"043|","定期开放债券":"008|","可转债":"045|",}
    #qdii{"全球股票":"311","亚太股票":"312","大中华区股票":"313","美国股票":"317","股债混合":"320","债券":"330","商品":"340",}
    "tabSubtype":",,,,,",
    "pi":1,#页数
    "pn":50,#单页条数
    "dx":1,
    "v":0.7143502235731329
}
# base_url="http://www.baidu.com"
data=requests.get(base_url,headers=header,params=params)
print(data.text)