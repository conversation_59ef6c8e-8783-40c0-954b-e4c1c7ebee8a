

from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy
import json
class get_hot_search(QThread):
    finished = pyqtSignal(list,)
    def __init__(self,):
        super().__init__()
        self.header={
              "Host": "fundsuggest.eastmoney.com",
              "Connection": "keep-alive",
              "sec-ch-ua-platform": "\"Windows\"",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
              "sec-ch-ua-mobile": "?0",
              "Accept": "*/*",
              "Sec-Fetch-Site": "same-site",
              "Sec-Fetch-Mode": "no-cors",
              "Sec-Fetch-Dest": "script",
              "Referer": "https://fund.eastmoney.com/",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; st_si=86432712158319; st_asi=delete; _qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; EMFUND4=null; EMFUND5=null; EMFUND6=null; EMFUND0=null; EMFUND9=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND8=07-31 16:04:23@#$%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; _qimei_i_1=6cb271e8d326; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=35; st_psi=20250731162316294-112200304021-5925885201"
            }
        self.url="https://fundsuggest.eastmoney.com/FundSearch/api/FundSearchAPI.ashx?callback=jQuery18307107648500971141_1753950195015&m=0&_=1753950244827"


    def request_data(self):
        try:
            response = requests.get(self.url, headers=self.header, verify=False)
            # 去除JSONP包装，提取JSON数据
            json_str = response.text.strip("jQuery18307107648500971141_1753950195015(").rstrip(")")
            data = json.loads(json_str)
            
            # 提取所有FundName到列表
            fund_names = []
            for item in data["Datas"]:
                fund_names.append(item["FundName"])

            return fund_names
        except Exception as e:
            print(f"请求数据失败: {e}")
            return []

    def run(self):
        try:
            result=self.request_data()
            self.finished.emit(result)
        except Exception as e:
            print(f"Error in run method: {e}")




