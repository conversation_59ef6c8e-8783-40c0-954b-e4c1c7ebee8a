async def async_get_fund_data(self, fund_code, add_date, instance):
    """异步获取单只基金数据"""
    loop = asyncio.get_event_loop()
    try:
        # 在单独的线程中执行同步IO操作
        return await loop.run_in_executor(
            None,
            partial(instance.get_fund_dict_and_new_data, fund_code, add_date)
        )
    except Exception as e:
        print(f"Error processing {fund_code}: {str(e)}")
        return None


async def batch_fetch_async(self, code_list, date_list, instance, max_concurrent=10):
    """批量异步获取基金数据"""
    semaphore = asyncio.Semaphore(max_concurrent)

    async def limited_task(code, date):
        async with semaphore:
            return await self.async_get_fund_data(code, date, instance)

    tasks = [limited_task(code, date) for code, date in zip(code_list, date_list)]
    return await asyncio.gather(*tasks, return_exceptions=True)


async def batch_process(self, code_list, date_list):
    """入口方法"""
    # 方案1: 使用纯异步
    results = await self.batch_fetch_async(code_list, date_list, self)

    # 处理结果
    valid_results = [r for r in results if r is not None]
    print(f"成功获取 {len(valid_results)}/{len(code_list)} 只基金数据")
    return valid_results
