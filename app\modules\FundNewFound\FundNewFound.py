from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import <PERSON>HeaderView, QTableWidgetItem, QMessageBox

from app.modules.FundNewFound.api.get_data import get_data
from app.modules.FundNewFound.src import img_html, style


class FundNewFound:
    def __init__(self, ui):
        self.ui = ui
        self.init_setup()
    def init_setup(self):  # 初始化变量
        self.current_page = 1
        self.show_widget_status = False
        self.ui.new_fund_widget.hide()
        self.ui.new_jj_lb.mousePressEvent = self.show_new_fund_widget
        self.ui.xfjj_return_lb.mousePressEvent = self.toggle_widget_index


    def toggle_widget_index(self,event):
        if self.show_widget_status:
            self.ui.new_fund_widget.hide()
            self.show_widget_status=False
        else:
            self.ui.new_fund_widget.show()

            self.show_widget_status = True
            self.load_new_fund_data()

    def show_new_fund_widget(self, event):
        self.ui.new_fund_widget.show()
        self.show_widget_status=True
        self.load_new_fund_data(self.current_page)

    def load_new_fund_data(self,page):
        self.ui.webEngineView.setHtml(img_html.html_content)
        self.worker_thread = get_data(page,"2")
        self.worker_thread.finished.connect(self.task_finished_new_fund)
        self.worker_thread.start()

    def load_new_fund_data_one(self, page):
        self.worker_thread = get_data(page,"1")
        self.worker_thread.finished.connect(self.task_finished_new_fund_one)
        self.worker_thread.start()

    def task_finished_new_fund(self, data1,data2,pages):
        self.zs_data=data1
        self.xcl_data=data2
        self.total_pages=pages
        headers_zs = ["基金代码", "基金名称", "基金公司", "基金类型", "集中认购期", "最高认购费率","基金经理", "基金状态"]
        headers_xcl = ["基金代码", "基金名称", "基金公司", "基金类型", "集中认购期", "募集份额(亿份)","成立日期","基金经理", "基金状态","优惠费率"]
        self.load_data_to_table_mb(table_name="xfjj_tableWidget_1", headers=headers_zs, data=self.zs_data, )
        self.load_data_to_table_mb(table_name="xfjj_tableWidget_2", headers=headers_xcl, data=self.xcl_data, )
        self.init_pages_status(self.total_pages)

    def task_finished_new_fund_one(self, data1,data2,pages):
        self.xcl_data=data2
        self.total_pages=pages
        headers_xcl = ["基金代码", "基金名称", "基金公司", "基金类型", "集中认购期", "募集份额(亿份)","成立日期","基金经理", "基金状态","优惠费率"]
        self.load_data_to_table_mb(table_name="xfjj_tableWidget_2", headers=headers_xcl, data=self.xcl_data, )
        self.init_pages_status(self.total_pages)

    def load_data_to_table_mb(self, table_name, headers, data ):
        try:
            getattr(self.ui, f"{table_name}").setColumnCount(len(headers))
            getattr(self.ui, f"{table_name}").setHorizontalHeaderLabels(headers)
            getattr(self.ui, f"{table_name}").horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            getattr(self.ui, f"{table_name}").horizontalHeader().setStyleSheet(style.QHeaderView_style)
            getattr(self.ui, f"{table_name}").setRowCount(len(data))
            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    if col_idx in [0, 1, ]:
                        item.setForeground(QColor("#294EBC"))
                    getattr(self.ui, f"{table_name}").setItem(row_idx, col_idx, item)
            row_height = 40  # 设置行高为 30 像素
            for row in range(getattr(self.ui, f"{table_name}").rowCount()):
                getattr(self.ui, f"{table_name}").setRowHeight(row, row_height)
            if table_name=="xfjj_tableWidget_2":
                start = (self.current_page - 1) * 50 + 1
                end = start + len(data)
                getattr(self.ui, f"{table_name}").setVerticalHeaderLabels([str(i) for i in range(start, end)])
        except Exception as e:
            print(f"load_data_to_table_mb error: {e}")

    def page_lb_style(self,page_name,status):
        if status:
            getattr(self.ui, f"{page_name}").setStyleSheet(style.QLabel_page_yes)
        else:
            getattr(self.ui, f"{page_name}").setStyleSheet(style.QLabel_page_no)


    def init_pages_status(self,all_pages):
        self.ui.xfjj_current_page.setText(f"{self.current_page}/{all_pages}")
        self.ui.xfjj_pre_page.mousePressEvent = self.sub_page
        self.ui.xfjj_after_page.mousePressEvent = self.add_page
        self.ui.xfjj_page_goto.mousePressEvent = self.goto_page
        self.update_page_status()

    def sub_page(self,event):
        if self.current_page>1:
            self.current_page-=1
            self.update_page_status()
            self.load_new_fund_data_one(self.current_page)

    def add_page(self,event):
        if self.current_page<self.total_pages:
            self.current_page+=1
            self.update_page_status()
            self.load_new_fund_data_one(self.current_page)

    def goto_page(self,event):
        page_=self.ui.xfjj_lineEdit.text()
        if page_:
            page_ = int(page_)
            if page_ > 0 and page_ <= self.total_pages:
                self.current_page = page_
                self.update_page_status()
                self.load_new_fund_data_one(self.current_page)
                self.ui.xfjj_lineEdit.clear()
            else:
                QMessageBox.information(self.ui.jj_filter_widget, "提示", "请输入正确的页码")
        else:
            QMessageBox.information(self.ui.jj_filter_widget, "提示", "请输入正确的页码")


    def update_page_status(self,):
        if self.current_page>1:
            self.page_lb_style("xfjj_pre_page",True)
        else:
            self.page_lb_style("xfjj_pre_page",False)
        if self.current_page<self.total_pages:
            self.page_lb_style("xfjj_after_page",True)
        else:
            self.page_lb_style("xfjj_after_page",False)






