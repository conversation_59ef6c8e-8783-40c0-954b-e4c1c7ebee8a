import sys
import json
import random
from datetime import datetime, timedelta

import requests
from PyQt6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout,
                             QWidget, QPushButton, QCheckBox)
from PyQt6.QtWebEngineWidgets import <PERSON><PERSON>ebEngineView
from PyQt6.QtCore import QUrl, QFile, QIODevice


class FinalChartWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        header = {
            "Host": "api.fund.eastmoney.com",
            "Connection": "keep-alive",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "sec-ch-ua-mobile": "?0",
            "Accept": "*/*",
            "Sec-Fetch-Site": "same-site",
            "Sec-Fetch-Mode": "no-cors",
            "Sec-Fetch-Dest": "script",
            "Referer": "https://fund.eastmoney.com/",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; st_si=75994445429365; st_asi=delete; ap_0_13a7d068=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=9; st_psi=20250421150713164-112200312939-5598177333"
        }
        s="011971,014319,001691,000011,001751,009512,610008,610108,002405"
        s_url = s.split(",")
        self.code_param = ""
        for i in s_url[:-1]:
            self.code_param += str(i) + ","
        self.code_param = self.code_param + s_url[-1]
        LJSYLurl = f"https://api.fund.eastmoney.com/FundCompare/LJSYL?bzdm={self.code_param }&c=threemonth&callback=jQuery183004562079219496318_1745496457601&_=1745496457723"
        response = requests.get(LJSYLurl, headers=header, verify=False, )
        response_text = json.loads(response.text.strip("jQuery183022900414814736725_1745219233201(").replace(")", ""))
        print(response_text["Data"])
        self.date = []
        self.t1 = [[] for i in range(9)]

        for i in eval(response_text["Data"])["dataProvider"]:
            print(i)
            self.date.append(i["date"])
            for j in range(9):
                self.t1[j].append(i[s_url[j]])

        #获取最大值，最小值
        max_ll=[]
        min_ll=[]
        for i in self.t1:
            max_ll.append(max(i))
            min_ll.append(min(i))
        max_v=max(max_ll)
        min_v=min(min_ll)
        # 动态生成测试数据（15个点，带%数据）
        self.line_data = [
            {"color": "#FF6384", "points":  self.t1[0]},
            {"color": "#36A2EB", "points":  self.t1[1]},
            {"color": "#FFCE56", "points":  self.t1[2]},
            {"color": "#4BC0C0", "points":  self.t1[3]},
            {"color": "#9966FF", "points":  self.t1[4]},
            {"color": "#FF9F40", "points":  self.t1[5]},
            {"color": "#8AC24A", "points":  self.t1[6]},
            {"color": "#F06292", "points":  self.t1[7]},
            {"color": "#7986CB", "points":  self.t1[8]},
            # {"color": "#E53935", "points":  self.t1[9]}
        ]
        min_value,max_value=self.re_max(min_v,max_v)
        # 配置参数
        self.chart_config = {
            "y_min": min_value,  # Y轴最小值（带%）
            "y_max": max_value,  # Y轴最大值（带%）
            "show_x_labels": False,
            "x_labels": self.date,
            "legends": ["东财银行A", "德邦半导体产业混合发起式A", "南方香港成长灵活配置混合", "华夏大盘精选混合A",
                        "华商信用增强债券A", "天弘添利债券(LOFE", "信澳信用债债券A", "信澳信用债债券C",
                        "光大中高等级债券A", ],
            "unit": "%"  # 数据单位
        }
        self.start_date=self.date[0]
        self.end_date=self.date[-1]

        self.initUI()

    def re_max(self,min_v,max_v,):
        stepSize = (max_v - min_v) / 3
        min1 = min_v - stepSize
        max1 = max_v + stepSize
        sub1 = max_v - min_v
        if 0 < sub1 <= 0.25:
            max1 = round(max1, 1)
            min1 = round(min1, 1)
        elif 0.25 < sub1 <= 0.5:
            max1 = round(max1, 1)
            min1 = round(min1, 1)
        elif 0.5 < sub1 <= 2.5:
            max1 = round(max1, 1)
            min1 = round(min1, 1)
        elif 2.5 < sub1 <=10:
            max1 = int(max1)
            min1 = int(min1)
        elif 10 < sub1 <= 50:
            max1 = int(max1)
            min1 = int(min1)
        elif 50 < sub1 < 10000:
            max1 = int(max1)
            min1 = int(min1)
        return min1,max1

    def initUI(self):
        container = QWidget()
        layout = QVBoxLayout()

        # 控制面板
        control_panel = QWidget()

        # 图表视图
        self.browser = QWebEngineView()

        layout.addWidget(control_panel)
        layout.addWidget(self.browser)
        container.setLayout(layout)
        self.setCentralWidget(container)

        self.load_chart()

        self.setGeometry(100, 100, 1200, 800)
        self.setWindowTitle('百分比数据图表')

    def load_chart(self):
        html_file = QFile("template.html")
        if html_file.open(QIODevice.OpenModeFlag.ReadOnly | QIODevice.OpenModeFlag.Text):
            html_content = html_file.readAll().data().decode('utf-8')
            html_file.close()

            html_content = html_content.replace(
                '/*CONFIG_PLACEHOLDER*/',
                f'const chartConfig = {json.dumps(self.chart_config)};'
            ).replace(
                '/*DATA_PLACEHOLDER*/',
                f'const lineData = {json.dumps(self.line_data)};'
            ).replace("start_date",self.start_date).replace("end_date",self.end_date)

            self.browser.setHtml(html_content, QUrl.fromLocalFile(""))

if __name__ == '__main__':
    app = QApplication(sys.argv)
    win = FinalChartWindow()
    win.show()
    sys.exit(app.exec())