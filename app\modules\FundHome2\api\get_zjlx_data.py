"""
获取大盘资金流向数据的API模块
"""

import requests
import json
import time
from urllib3 import disable_warnings
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
disable_warnings(InsecureRequestWarning)

def get_zjlx_data():
    """
    获取大盘资金流向数据
    返回: (time_list, main_net_flow, super_large_net_flow, large_net_flow, medium_net_flow, small_net_flow)
    """
    # 生成动态时间戳
    timestamp = int(time.time() * 1000)

    url = f"https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?cb=jQuery112306836466859791036_{timestamp}&lmt=0&klt=1&fields1=f1%2Cf2%2Cf3%2Cf7&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61%2Cf62%2Cf63%2Cf64%2Cf65&ut=b2884a393a59ad64002292a3e90d46a5&secid=1.000001&secid2=0.399001&_={timestamp + 1}"

    headers = {
        "Host": "push2.eastmoney.com",
        "Connection": "keep-alive",
        "sec-ch-ua-platform": "\"Windows\"",
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
        "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
        "sec-ch-ua-mobile": "?0",
        "Accept": "*/*",
        "Sec-Fetch-Site": "same-site",
        "Sec-Fetch-Mode": "no-cors",
        "Sec-Fetch-Dest": "script",
        "Referer": "https://data.eastmoney.com/zjlx/dpzjlx.html",
        "Accept-Encoding": "identity",  # 不接受压缩
        "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6"
    }

    try:
        response = requests.get(url, headers=headers, verify=False, timeout=10)
        response.encoding = 'utf-8'

        # 去除JSONP包装，提取JSON数据
        json_str = response.text.strip(f"jQuery112306836466859791036_{timestamp}(").rstrip(");")
        data = json.loads(json_str)

        # 检查数据结构
        if "data" not in data or "klines" not in data["data"]:
            print("数据结构异常")
            return [], [], [], [], [], []

        # 获取klines数据
        klines = data["data"]["klines"]

        if not klines:
            print("没有获取到klines数据")
            return [], [], [], [], [], []

        # 初始化6个列表
        time_list = []
        main_net_flow = []  # 今日主力净流入
        super_large_net_flow = []  # 今日超大单净流入
        large_net_flow = []  # 今日大单净流入
        medium_net_flow = []  # 今日中单净流入
        small_net_flow = []  # 今日小单净流入

        for kline in klines:
            parts = kline.split(",")

            if len(parts) < 6:
                continue

            # 提取时间，只保留时分
            datetime_str = parts[0]
            if " " in datetime_str:
                time_part = datetime_str.split(" ")[1]  # 获取时间部分
                time_list.append(time_part)
            else:
                time_list.append(datetime_str)

            # 提取资金流向数据，转换为亿元单位，保留4位小数
            try:
                main_net_flow.append(round(float(parts[1]) / 100000000, 4))
                super_large_net_flow.append(round(float(parts[2]) / 100000000, 4))
                large_net_flow.append(round(float(parts[3]) / 100000000, 4))
                medium_net_flow.append(round(float(parts[4]) / 100000000, 4))
                small_net_flow.append(round(float(parts[5]) / 100000000, 4))
            except (ValueError, IndexError) as e:
                print(f"数据解析错误: {e}, parts: {parts}")
                continue

        print(f"成功获取资金流向数据，共{len(time_list)}个数据点")
        return time_list,main_net_flow, small_net_flow,  medium_net_flow, large_net_flow,super_large_net_flow

    except requests.RequestException as e:
        print(f"网络请求失败: {e}")
        return [], [], [], [], [], []
    except json.JSONDecodeError as e:
        print(f"JSON解析失败: {e}")
        return [], [], [], [], [], []
    except Exception as e:
        print(f"获取资金流向数据失败: {e}")
        return [], [], [], [], [], []

if __name__ == "__main__":
    # 测试函数
    result = get_zjlx_data()
    print("测试结果:", result)