import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QScrollArea,
    QGridLayout, QLabel, QListWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QColor, QPalette

class ClickableLabel(QLabel):
    clicked = pyqtSignal()

    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # self.setWordWrap(True)
        self.setStyleSheet("""
             QLabel {
             padding: 4px;
                    border: 2px solid #D8D8D8;
                    border-radius: 4px;
                }
        """)
        self.original_palette = self.palette()
        self.is_highlighted = False

    def mousePressEvent(self, event):
        self.clicked.emit()
        super().mousePressEvent(event)

    def set_highlighted(self, highlighted):
        self.is_highlighted = highlighted
        if highlighted:
            palette = self.palette()
            palette.setColor(QPalette.ColorRole.Window, QColor(255, 255, 0))  # 黄色高亮
            palette.setColor(QPalette.ColorRole.WindowText, QColor(0, 0, 0))
            self.setPalette(palette)
            self.setStyleSheet("""
                QLabel {
                padding: 4px;
                    border: 0;
                    border-radius: 4px;
                    background-color: #1890FF;
                    color:white;
                }
            """)
        else:
            self.setPalette(self.original_palette)
            self.setStyleSheet("""
                QLabel {
                    padding: 4px;
                    border: 2px solid #D8D8D8;
                    border-radius: 4px;
                }
            """)

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("板块选择器")
        self.setGeometry(100, 100, 800, 600)

        # 创建主窗口的中心部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)

        # 主布局
        main_layout = QVBoxLayout(central_widget)

        # 滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        scroll_layout.setSpacing(5)
        scroll_layout.setContentsMargins(5, 5, 5, 5)

        # 板块名称列表
        self.sectors =['医疗服务', '铁路公路', '自动化设备', '农化制品', '小金属', '汽车服务', '家电零部件', '医药商业', '航天装备', '地面兵装', '煤炭', '环保', '化妆品', '医疗美容', '光学光电子', '电力设备', '房地产', '社会服务', '股份制银行', '城商行', '证券', '通用设备', '光伏设备', '电池', '计算机设备', '出版', '石油石化', '环保设备', '电子化学品', '食品饮料', '白酒', '纺织服饰', '饲料', '通信服务', '煤炭开采', '个护用品', '物流', '工程咨询服务', '数字媒体', '建筑材料', '装修建材', '机械设备', '计算机', '化学制品', '钢铁', '广告营销', '元件', '包装印刷', '电力', '房屋建设', '专业工程', '电机', '专用设备', '军工电子', '工业金属', 'IT服务', '白色家电', '非白酒', '文娱用品', '传媒', '交通运输', '航空机场', '房地产服务', '专业服务', '银行', '非银金融', '建筑装饰', '农林牧渔', '有色金属', '风电设备', '工程机械', '航空装备', '软件开发', '贵金属', '乘用车', '食品加工', '房地产开发', '通信', '商贸零售', '通信设备', '体育', '保险', '休闲食品', '轻工制造', '中药', '生物制品', '医药生物', '医疗器械', '基础化工', '公用事业', '国有大型银行', '国防军工', '半导体', '化学制药', '家用电器', '小家电', '电子', '环境治理', '汽车', '汽车零部件', '电网设备', '美容护理', '化学原料', '化学纤维', '消费电子', '家居用品', '养殖业', '冶钢原料', '调味发酵品', '国家安防', '汽车热管理', '第三代半导体', '智能穿戴', '云计算', '中字头', '超级真菌', 'LED', '网络安全', '锂电池', '生物疫苗', '特斯拉', '元宇宙', '资源', 'Web3.0', '光刻胶', '尾气治理', '有机硅', '高端装备', '智能驾驶', '氢能源', '农业种植', '中特估', '大宗商品', '人工智能', '在线教育', '储能', '工业4.0', '新型城镇化', '养老产业', '黄金', '安全主题', '油气设服', '基因测序', '能源', '国企改革', '乳业', '职业教育', '新能源车', '智能家居', '通用航空', '内贸流通', '植物照明', '啤酒', '消费', '国资云', '婴童', '可选消费', '化工原料', '5G', '华为', '充电桩', '航天', '毫米波', '工业互联', '超清视频', '东数西算', 'TMT', '垃圾分类', '猪肉', '科技', '新能源', '无人驾驶', '影视', '农牧主题', '数据中心', 'NFT', '游戏', '航母', '地热能', '机器人', '新零售', '新兴科技100', '金融', '绿色电力', 'AIGC', '碳中和', '光通信', '军民融合', 'CRO', '低空经济', '高端制造', '国产软件', '创新药', '材料', '一带一路', '信创', '数字孪生', '新兴产业', '并购重组', '精准医疗', '煤化工', 'DeepSeek', '人形机器人', '算力', '数据要素']

        self.labels = []  # 存储所有QLabel的引用

        # 计算行数和列数，这里假设每行显示19个标签（可根据需要调整）
        cols = 10
        rows = (len(self.sectors) + cols - 1) // cols

        # 添加QLabel到网格布局
        for i, sector in enumerate(self.sectors):
            row = i // cols
            col = i % cols
            label = ClickableLabel(sector)
            label.clicked.connect(self.on_label_clicked)
            scroll_layout.addWidget(label, row, col)
            self.labels.append(label)

        # 设置滚动区域的widget
        scroll_area.setWidget(scroll_widget)

        # 选中的板块列表
        self.selected_sectors = []

        # 显示选中的板块列表
        self.selected_list = QListWidget()
        self.selected_list.setFixedHeight(100)  # 设置固定高度

        # 将滚动区域和选中列表添加到主布局
        main_layout.addWidget(scroll_area)
        main_layout.addWidget(QLabel("选中的板块:"))
        main_layout.addWidget(self.selected_list)

    def on_label_clicked(self):
        label = self.sender()
        sector = label.text()

        if label.is_highlighted:
            # 如果已经高亮，则取消高亮并从选中列表中移除
            label.set_highlighted(False)
            if sector in self.selected_sectors:
                self.selected_sectors.remove(sector)
        else:
            # 如果未高亮，则高亮并添加到选中列表
            label.set_highlighted(True)
            if sector not in self.selected_sectors:
                self.selected_sectors.append(sector)

        # 更新选中列表的显示
        self.update_selected_list()

    def update_selected_list(self):
        self.selected_list.clear()
        self.selected_list.addItems(self.selected_sectors)

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())