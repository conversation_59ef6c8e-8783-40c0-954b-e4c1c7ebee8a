import json
import sys
from PyQt6.QtWidgets import (
    QApplication, QWidget, QGridLayout, QLabel
)
from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor, QPalette
import random

class FundSectorWidget(QWidget):
    def __init__(self, data):
        super().__init__()
        self.data = data
        self.init_ui()

    def init_ui(self):
        grid_layout = QGridLayout()
        grid_layout.setSpacing(2)

        for row_idx, (sector, value) in enumerate(self.data):
            row = row_idx // 10
            col = row_idx % 10

            # 格式化资金流入（万/亿单位）
            formatted_value = self.format_value(value)

            # 创建 QLabel，设置字体大小
            label = QLabel(
                f"<span style='font-size: 14px;'>{sector}</span><br>"
                f"<strong><span style='font-size: 15px;'>{formatted_value}</span></strong>"
            )
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setWordWrap(True)

            # 计算背景色和字体色
            bg_color, font_color = self.calculate_color(value)

            # 设置背景颜色
            palette = label.palette()
            palette.setColor(QPalette.ColorRole.Window, bg_color)
            label.setAutoFillBackground(True)
            label.setPalette(palette)

            # 设置字体颜色
            label.setStyleSheet(f"color: {font_color.name()};padding:4px")
            label.mousePressEvent = lambda event, s=sector, v=value: self.on_label_click(s, v)

            grid_layout.addWidget(label, row, col)

        self.setLayout(grid_layout)
        self.setWindowTitle("基金板块资金流入")
        # 动态调整窗口大小
        self.resize(800, max(600, (len(self.data) // 10 + 1) * 50))

    def on_label_click(self, sector, value):
        """当点击 QLabel 时，输出对应的 sector 和 value"""
        print(f"Clicked: Sector={sector}, Value={value}")

    def format_value(self, value):
        """将资金流入格式化为万/亿单位"""
        if abs(value) < 100000000:  # -1亿 < value < 1亿
            return f"{value / 10000:.2f}万"
        else:  # value <= -1亿 或 value >= 1亿
            return f"{value / 100000000:.2f}亿"

    def calculate_color(self, value):
        """根据资金流入计算颜色，并返回 (背景色, 字体色)"""
        if value > 0:
            # 红色渐变（资金流入越大越红）
            positive_values = [d[1] for d in self.data if d[1] > 0]
            value_max = max(positive_values) if positive_values else 1e-6
            ratio = min(1.0, value / value_max)
            # 从 #FDE1E1（浅红）到 #F54545（深红）
            red = int(253 - (253 - 245) * ratio)
            green = int(229 - (229 - 69) * ratio)
            blue = int(229 - (229 - 69) * ratio)
            bg_color = QColor(red, green, blue)

            # 判断是否需要调整字体颜色
            threshold_red = QColor(251, 180, 180)  # #FBB4B4
            if (red < threshold_red.red() and
                    green < threshold_red.green() and
                    blue < threshold_red.blue()):
                font_color = QColor(255, 255, 255)  # 白色
            else:
                font_color = QColor(210, 0, 0)  # 深红色 #D20000

        elif value < 0:
            # 绿色渐变（资金流入越小越绿）
            negative_values = [abs(d[1]) for d in self.data if d[1] < 0]
            value_max = max(negative_values) if negative_values else 1e-6
            ratio = min(1.0, abs(value) / value_max)
            # 从 #CFEACF（浅绿）到 #139814（深绿）
            red = int(207 - (207 - 19) * ratio)
            green = int(234 - (234 - 152) * ratio)
            blue = int(207 - (207 - 20) * ratio)
            bg_color = QColor(red, green, blue)

            # 判断是否需要调整字体颜色
            threshold_green = QColor(139, 204, 140)  # #8BCC8C
            if (red < threshold_green.red() and
                    green < threshold_green.green() and
                    blue < threshold_green.blue()):
                font_color = QColor(255, 255, 255)  # 白色
            else:
                font_color = QColor(0, 96, 56)  # 深绿色 #006038

        else:
            # 资金流入为0，使用灰色
            bg_color = QColor(243, 243, 243)  # #F3F3F3
            font_color = QColor(0, 0, 0)  # 黑色（默认）

        return bg_color, font_color

if __name__ == "__main__":
    app = QApplication(sys.argv)
    with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSubject\temp\data_zj.txt", "r",encoding="utf8") as f:
        d=f.read()

        data = json.loads(d)
    k_l=[]
    data=data["Data"]
    for i in data:
        k_l.append([i["INDEXNAME"],i["FLOW"]])


    window = FundSectorWidget(k_l)
    window.show()
    sys.exit(app.exec())