#!/usr/bin/env python3
"""
测试增强的Markdown聊天功能
- 自适应高度
- 数学公式支持
- 圆角效果
- 无滚动条
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit, QScrollArea
from PyQt6.QtCore import Qt, QTimer

# 导入我们的MarkdownWebView组件
from app.modules.FundAiChat.FundAiChat import MarkdownWebView


class TestEnhancedMarkdown(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("增强Markdown聊天测试")
        self.setGeometry(100, 100, 900, 700)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAsNeeded)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)
        
        # 创建聊天容器
        self.chat_container = QWidget()
        self.chat_layout = QVBoxLayout(self.chat_container)
        self.chat_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        scroll_area.setWidget(self.chat_container)
        
        layout.addWidget(scroll_area)
        
        # 创建输入框
        self.input_text = QTextEdit()
        self.input_text.setMaximumHeight(100)
        self.input_text.setPlaceholderText("输入Markdown文本进行测试...")
        layout.addWidget(self.input_text)
        
        # 创建测试按钮
        button_layout = QVBoxLayout()
        
        test_button = QPushButton("测试基础Markdown")
        test_button.clicked.connect(self.test_basic_markdown)
        button_layout.addWidget(test_button)
        
        math_button = QPushButton("测试数学公式")
        math_button.clicked.connect(self.test_math_formulas)
        button_layout.addWidget(math_button)
        
        stream_button = QPushButton("测试流式输出")
        stream_button.clicked.connect(self.test_stream_output)
        button_layout.addWidget(stream_button)
        
        clear_button = QPushButton("清空聊天")
        clear_button.clicked.connect(self.clear_chat)
        button_layout.addWidget(clear_button)
        
        layout.addLayout(button_layout)
        
        # 添加初始测试内容
        self.add_test_message()
    
    def add_markdown_message(self, content):
        """添加一个Markdown消息"""
        # 创建容器widget
        message_widget = QWidget()
        message_layout = QVBoxLayout(message_widget)
        message_layout.setContentsMargins(5, 5, 5, 5)
        
        # 创建圆角容器
        rounded_container = QWidget()
        rounded_container.setStyleSheet("""
            QWidget {
                background-color: white;
                border: 1px solid #EAEDF1;
                border-radius: 10px;
            }
            QWidget:hover {
                border: 1px solid #ccc;
                background-color: #F1F9FE;
            }
        """)
        
        container_layout = QVBoxLayout(rounded_container)
        container_layout.setContentsMargins(0, 0, 0, 0)
        
        # 创建MarkdownWebView
        webview = MarkdownWebView()
        webview.setStyleSheet("""
            QWebEngineView {
                border: none;
                background-color: transparent;
            }
        """)
        
        # 设置内容
        webview.update_content(content)
        
        container_layout.addWidget(webview)
        message_layout.addWidget(rounded_container)
        self.chat_layout.addWidget(message_widget)
        
        return webview
    
    def add_test_message(self):
        """添加初始测试消息"""
        content = """# 增强Markdown聊天测试

这是一个测试**自适应高度**和**圆角效果**的Markdown渲染器。

## 功能特性

1. ✅ **自适应高度** - 根据内容自动调整高度
2. ✅ **圆角效果** - 美观的圆角边框
3. ✅ **无滚动条** - 内容完全展示，无需滚动
4. ✅ **数学公式** - 支持LaTeX数学公式

### 示例表格

| 功能 | 状态 | 说明 |
|------|------|------|
| 高度自适应 | ✅ | 内容多少，高度多少 |
| 圆角效果 | ✅ | 美观的UI设计 |
| 数学公式 | ✅ | LaTeX公式支持 |

> 这是一个引用块，用于显示重要信息。

### 代码示例

```python
def calculate_return(principal, rate, years):
    \"\"\"计算投资收益\"\"\"
    return principal * (1 + rate) ** years

# 示例计算
result = calculate_return(10000, 0.08, 5)
print(f"5年后收益: {result:.2f}")
```

**测试完成！** 🎉"""
        
        self.add_markdown_message(content)
    
    def test_basic_markdown(self):
        """测试基础Markdown"""
        content = self.input_text.toPlainText()
        if content.strip():
            self.add_markdown_message(content)
        else:
            # 默认测试内容
            test_content = """## 基础Markdown测试

这是一个**粗体**文本和*斜体*文本的示例。

### 列表测试

- 第一项
- 第二项
  - 子项目A
  - 子项目B
- 第三项

### 代码测试

行内代码：`print("Hello World")`

代码块：
```javascript
function greet(name) {
    return `Hello, ${name}!`;
}
```

### 链接测试

这是一个[链接示例](https://example.com)。

> 这是一个引用块，展示重要信息。"""
            
            self.add_markdown_message(test_content)
    
    def test_math_formulas(self):
        """测试数学公式"""
        math_content = """## 数学公式测试

### 行内公式

这是一个行内公式：$E = mc^2$，爱因斯坦的质能方程。

投资收益计算：$FV = PV \\times (1 + r)^n$

### 块级公式

二次方程的解：

$$x = \\frac{-b \\pm \\sqrt{b^2 - 4ac}}{2a}$$

复利计算公式：

$$A = P\\left(1 + \\frac{r}{n}\\right)^{nt}$$

其中：
- $A$ = 最终金额
- $P$ = 本金
- $r$ = 年利率
- $n$ = 每年复利次数
- $t$ = 投资年数

### 矩阵示例

$$\\begin{pmatrix}
a & b \\\\
c & d
\\end{pmatrix}$$

### 求和公式

$$\\sum_{i=1}^{n} i = \\frac{n(n+1)}{2}$$

**数学公式渲染测试完成！** 📊"""
        
        self.add_markdown_message(math_content)
    
    def test_stream_output(self):
        """测试流式输出"""
        # 创建一个新的消息
        webview = self.add_markdown_message("")
        
        # 模拟流式输出
        stream_content = [
            "# 流式输出测试\n\n",
            "正在生成回答",
            "...\n\n",
            "## 投资建议\n\n",
            "根据当前市场情况，我建议：\n\n",
            "1. **分散投资** - 不要把所有鸡蛋放在一个篮子里\n",
            "2. **长期持有** - 时间是投资的朋友\n",
            "3. **定期定额** - 平均成本法降低风险\n\n",
            "### 推荐基金\n\n",
            "| 基金名称 | 类型 | 风险等级 | 预期收益 |\n",
            "|----------|------|----------|----------|\n",
            "| 易方达蓝筹 | 股票型 | 高 | 8-12% |\n",
            "| 华夏债券A | 债券型 | 低 | 3-5% |\n\n",
            "### 收益计算\n\n",
            "假设投资10万元，年化收益率8%：\n\n",
            "$FV = 100000 \\times (1 + 0.08)^5 = 146933$ 元\n\n",
            "```python\n",
            "# 收益计算代码\n",
            "principal = 100000\n",
            "rate = 0.08\n",
            "years = 5\n",
            "future_value = principal * (1 + rate) ** years\n",
            "print(f'5年后价值: {future_value:.0f}元')\n",
            "```\n\n",
            "> **风险提示**: 投资有风险，入市需谨慎。过往业绩不代表未来表现。\n\n",
            "**流式输出完成！** ✨"
        ]
        
        # 模拟逐步添加内容
        self.stream_index = 0
        self.stream_data = stream_content
        self.stream_webview = webview
        self.timer = QTimer()
        self.timer.timeout.connect(self.add_stream_chunk)
        self.timer.start(300)  # 每300ms添加一块内容
    
    def add_stream_chunk(self):
        """添加流式内容块"""
        if self.stream_index < len(self.stream_data):
            self.stream_webview.append_content(self.stream_data[self.stream_index])
            self.stream_index += 1
        else:
            self.timer.stop()
    
    def clear_chat(self):
        """清空聊天记录"""
        for i in reversed(range(self.chat_layout.count())):
            item = self.chat_layout.itemAt(i)
            if item:
                widget = item.widget()
                if widget:
                    widget.setParent(None)


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestEnhancedMarkdown()
    window.show()
    sys.exit(app.exec())
