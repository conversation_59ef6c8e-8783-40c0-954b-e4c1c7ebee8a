import os

class DataHandle():
    # 类变量
    history_file = os.path.join(os.path.dirname(__file__), "src/fund_history.txt")

    @staticmethod
    def handle_index_selection(index_name: str, index_code: str):
        """处理指数选择，可以在这里添加更多的数据处理逻辑"""
        try:

            # 这里可以添加更多的数据处理逻辑
            # 比如：获取指数数据、更新图表、保存选择状态等

            return {
                "name": index_name,
                "code": index_code,
                "status": "success"
            }

        except Exception as e:
            print(f"DataHandle处理指数选择失败: {e}")
            return {
                "name": index_name,
                "code": index_code,
                "status": "error",
                "error": str(e)
            }



    @staticmethod
    def return_img_url(zs_code:str,zs_time_index:int):
        """净值图"""
        time_list=["","M1","M2","M3","M4",]
        if zs_time_index==0:
            k="r"
        else:
            k="t"
        base_url=f"https://webquotepic.eastmoney.com/GetPic.aspx?imageType={k}&type={time_list[zs_time_index]}&token=44c9d251add88e27b65ed86506f6e5da&nid={zs_code}&timespan=1754308058"
        return base_url

    @staticmethod
    def return_img_url_2(zs_code: str, zs_time_index: int,unit:int,zb_index_1:int,zb_index_2:int):
        """净值图"""
        time_list = ["", "W", "M", "M5", "M15","M30","M60"]
        # k线图指标,ef,formula
        ef = ["", "EXTENDED_MA", "EXTENDED_BOLL", "EXTENDED_SAR", "EXTENDED_BBI", ]
        formula = ["RSI", "KDJ", "MACD", "WR", "DMI", "BIAS", "OBV", "CCI", "ROC"]
        base_url = f"https://webquoteklinepic.eastmoney.com/GetPic.aspx?nid={zs_code}&type={time_list[zs_time_index]}&unitWidth={unit}&ef={ef[zb_index_1]}&formula={formula[zb_index_2]}&AT=1&imageType=KXL&timespan=1754323595"
        return base_url


    def return_zs_info_color(self,start:str,data:list):
        #最高，最低，涨跌幅，涨跌额
        start=eval(start)
        result=[]
        s = []
        for i in data:
            s.append(float(i.strip("%")))
        result=self.compare_zs(s[0],start,result)
        result=self.compare_zs(s[1],start,result)
        result=self.compare_zs(s[2],0,result)
        result=self.compare_zs(s[3],0,result)
        return result


    # @staticmethod
    def compare_zs(self,a,b,arr:list):
        if a>b:
            arr.append(1)
        elif a==b:
            arr.append(0)
        else:
            arr.append(-1)
        return arr

    def trans_bkzj_data(self,bk_name,bk_value):
        # bk_name已经是一个列表，不需要split
        categories = bk_name
        vertical_categories = ['\n'.join(list(category)) for category in categories]
        # 创建分类和数据的配对，然后按数据从高到低排序
        paired_data = list(zip(vertical_categories, bk_value))
        paired_data.sort(key=lambda x: x[1], reverse=True)

        # 分离排序后的分类和数据
        sorted_categories = [item[0] for item in paired_data]
        sorted_data = [item[1] for item in paired_data]
        return {
            'categories': sorted_categories,
            'data': sorted_data
        }

    @staticmethod
    def trans_zjlx_data(time_list, main_net_flow, super_large_net_flow, large_net_flow, medium_net_flow, small_net_flow):
        """
        转换资金流向数据为图表格式
        """
        try:
            chart_data = {
                "time_labels": time_list,
                "datasets": [
                    {"name": "主力净流入", "data": main_net_flow, "color": "#FF6384"},
                    {"name": "超大单净流入", "data": super_large_net_flow, "color": "#36A2EB"},
                    {"name": "大单净流入", "data": large_net_flow, "color": "#FFCE56"},
                    {"name": "中单净流入", "data": medium_net_flow, "color": "#4BC0C0"},
                    {"name": "小单净流入", "data": small_net_flow, "color": "#9966FF"}
                ]
            }
            return chart_data
        except Exception as e:
            print(f"转换资金流向数据失败: {e}")
            return {
                "time_labels": [],
                "datasets": []
            }

    @staticmethod
    def get_latest_zjlx_values(main_net_flow, super_large_net_flow, large_net_flow, medium_net_flow, small_net_flow):
        """
        获取最新的资金流向数值
        返回: [主力净流入, 超大单净流入, 大单净流入, 中单净流入, 小单净流入]
        """
        try:
            values = []
            data_lists = [main_net_flow, super_large_net_flow, large_net_flow, medium_net_flow, small_net_flow]

            for data_list in data_lists:
                if data_list and len(data_list) > 0:
                    values.append(data_list[-1])  # 获取最后一个元素
                else:
                    values.append(0.0)  # 如果没有数据，默认为0

            return values
        except Exception as e:
            print(f"获取最新资金流向数值失败: {e}")
            return [0.0, 0.0, 0.0, 0.0, 0.0]


DataHandle=DataHandle()

