# 流式输出线程
from PyQt6.QtCore import QThread, pyqtSignal
from openai import OpenAI

from app.api.other.setting import API_KEY

# 配置 OpenAI 客户端
client = OpenAI(
    base_url="https://ark.cn-beijing.volces.com/api/v3",  # 替换为你的 API URL
    api_key=API_KEY,  # 替换为你的 API Key 需要加密
)

class StreamResponseThread(QThread):
    stream_signal = pyqtSignal(str)  # 用于发送流式输出的信号

    def __init__(self, model,question, chat_history):
        super().__init__()
        self.question = question
        self.model = model
        self.chat_history = chat_history
        self._is_running = True  # 控制线程是否继续运行


    def run(self):
        # 调用 deepseek-R1 API 实现流式输出
        messages = self.chat_history + [{"role": "user", "content": self.question}]
        stream = client.chat.completions.create(
            model=self.model,  # 替换为你的模型 ID
            messages=messages,
            stream=True,
        )
        for chunk in stream:
            if not self._is_running:  # 如果用户终止回答，停止输出
                break
            if chunk.choices and chunk.choices[0].delta.content:
                self.stream_signal.emit(chunk.choices[0].delta.content)

    def stop(self):
        self._is_running = False  # 终止线程







