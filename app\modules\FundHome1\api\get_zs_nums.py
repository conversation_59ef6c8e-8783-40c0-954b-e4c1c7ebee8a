from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy
import json
from concurrent.futures import ThreadPoolExecutor
import concurrent.futures


class get_zs_nums(QThread):
    finished = pyqtSignal(list, )

    def __init__(self, ):
        super().__init__()
        self.header = {
  "Host": "push2.eastmoney.com",
  "Connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-site",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://quote.eastmoney.com/center/hszs.html",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; _qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; EMFUND4=null; EMFUND5=null; EMFUND6=null; EMFUND0=null; EMFUND9=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND8=07-31 16:04:23@#$%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; st_si=95422376668622; st_asi=delete; fullscreengg=1; fullscreengg2=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=36; st_psi=20250802203829322-112200304021-7812344737; _qimei_i_1=78f87ed4f500"
}
        self.header_2={
  "Host": "push2ex.eastmoney.com",
  "Connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-site",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://quote.eastmoney.com/ztb/detail",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; _qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; EMFUND4=null; EMFUND5=null; EMFUND6=null; EMFUND0=null; EMFUND9=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND8=07-31 16:04:23@#$%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; st_si=95422376668622; st_asi=delete; fullscreengg=1; fullscreengg2=1; _qimei_i_1=7cde5bc8e325; websitepoptg_api_time=1754146049931; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=47; st_psi=20250802224729880-113200304537-0865018881"
}
        self.url="https://push2.eastmoney.com/api/qt/ulist/get?fltt=1&invt=2&cb=jQuery37108145365529349199_1754139823319&fields=f104%2Cf105%2Cf106&secids=1.000001%2C0.399001%2C0.399006%2C0.399330&ut=fa5fd1943c7b386f172d6893dbfba10b&pn=1&np=1&dect=1&pz=20&wbp2u=%7C0%7C0%7C0%7Cweb&_=1754139823320"
        self.url_top_count="https://push2ex.eastmoney.com/getTopicZDTCount?cb=callbackdata7934863&ut=7eea3edcaed734bea9cbfc24409ed989&dpt=wz.ztzt&time=0&_=1754146116526"
    def request_data_1(self):
        try:
            response = requests.get(self.url, headers=self.header, verify=False)
            
            # 去除JSONP包装，提取JSON数据
            json_str = response.text.strip("jQuery37108145365529349199_1754139823319(").rstrip(");")
            data = json.loads(json_str)
            
            # 初始化三个列表
            up_list = []
            down_list = []
            middle_list = []
            
            # 解析diff数据
            diff_list = data["data"]["diff"]
            for item in diff_list:
                up_list.append(item["f104"])     # 上涨家数
                down_list.append(item["f105"])   # 下跌家数
                middle_list.append(item["f106"]) # 平家数
            
            # 求和
            up = sum(up_list)
            down = sum(down_list)
            middle = sum(middle_list)
            
            return [up, down, middle]
            
        except Exception as e:
            print(f"请求数据失败: {e}")
            return [0, 0, 0]

    def request_data_2(self):
        try:
            response = requests.get(self.url_top_count, headers=self.header_2, verify=False)
            
            # 去除JSONP包装，提取JSON数据
            json_str = response.text.strip("callbackdata7934863(").rstrip(");")
            data = json.loads(json_str)
            
            # 获取zdtcount数组
            zdtcount_list = data["data"]["zdtcount"]
            
            # 获取最后一个元素
            last_item = zdtcount_list[-1]
            
            # 提取ztc和dtc
            ztc = last_item["ztc"]
            dtc = last_item["dtc"]
            
            return [ztc, dtc]
            
        except Exception as e:
            print(f"请求数据失败: {e}")
            return 0, 0


    def run(self):
        try:
            with ThreadPoolExecutor(max_workers=2) as executor:
                future_data1 = executor.submit(self.request_data_1)
                future_data2 = executor.submit(self.request_data_2)
                concurrent.futures.wait([future_data1, future_data2])

                up, down, middle = future_data1.result()
                ztc, dtc = future_data2.result()

                result = [up,ztc, down, dtc,middle, ]
                self.finished.emit(result)
                
        except Exception as e:
            print(f"Error in run method: {e}")




