<!DOCTYPE html>
<html>
<head>
    <title>实时资金流向图表</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chartjs-plugin-annotation"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        #chart-container {
            width: 100%;
            height: 600px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            box-sizing: border-box;
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>实时资金流向图表</h1>
    <div id="chart-container">
        <canvas id="line-chart"></canvas>
    </div>

    <script>
        /*DATA_PLACEHOLDER*/
        
        let myChart;
        
        function initChart() {
            const ctx = document.getElementById('line-chart').getContext('2d');
            
            const datasets = chartData.datasets.map(dataset => ({
                label: dataset.name,
                data: dataset.data,
                borderColor: dataset.color,
                backgroundColor: dataset.color + '20',
                borderWidth: 3,  // 增加线条宽度
                pointRadius: 0,
                pointHoverRadius: 5,
                fill: false,
                tension: 0.1
            }));
            
            myChart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartData.time_labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: '资金流向趋势图',
                            font: {
                                size: 16
                            }
                        },
                        legend: {
                            display: true,
                            position: 'top'
                        },
                        tooltip: {
                            mode: 'index',
                            intersect: false,
                            callbacks: {
                                label: function(context) {
                                    return context.dataset.label + ': ' + context.parsed.y.toFixed(4) + '亿';
                                }
                            }
                        },
                        annotation: {
                            annotations: {}
                        }
                    },
                    scales: {
                        x: {
                            display: true,
                            title: {
                                display: true,
                                text: '时间'
                            },
                            ticks: {
                                maxTicksLimit: 20
                            }
                        },
                        y: {
                            display: true,
                            title: {
                                display: true,
                                text: '资金流入(亿元)'
                            },
                            grid: {
                                drawBorder: false
                            }
                        }
                    },
                    interaction: {
                        mode: 'index',
                        intersect: false
                    }
                }
            });
            
            // 添加鼠标移动事件监听
            ctx.canvas.addEventListener('mousemove', function(event) {
                const rect = ctx.canvas.getBoundingClientRect();
                const x = event.clientX - rect.left;
                const y = event.clientY - rect.top;
                
                const canvasPosition = Chart.helpers.getRelativePosition(event, myChart);
                const dataX = myChart.scales.x.getValueForPixel(canvasPosition.x);
                
                if (dataX >= 0 && dataX < chartData.time_labels.length) {
                    const dataIndex = Math.round(dataX);
                    
                    // 更新垂直线
                    myChart.options.plugins.annotation.annotations = {
                        line1: {
                            type: 'line',
                            xMin: dataIndex,
                            xMax: dataIndex,
                            borderColor: 'rgba(255, 99, 132, 0.8)',
                            borderWidth: 1,
                            borderDash: [5, 5]
                        }
                    };
                    myChart.update('none');
                }
            });
            
            // 鼠标离开时清除垂直线
            ctx.canvas.addEventListener('mouseleave', function() {
                myChart.options.plugins.annotation.annotations = {};
                myChart.update('none');
            });
        }
        // 页面加载完成后初始化图表
        document.addEventListener('DOMContentLoaded', initChart);
    </script>
</body>
</html>

