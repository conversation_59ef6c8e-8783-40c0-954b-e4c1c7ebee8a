import asyncio
import concurrent
import json
from concurrent.futures import ThreadPoolExecutor

import requests
from PyQt6.QtCore import QThread, pyqtSignal
class get_company_data(QThread):
    finished = pyqtSignal(list,)
    def __init__(self,):
        super(). __init__()
        self.company_list=[]


        self.headers={
              "Host": "fund.eastmoney.com",
              "Connection": "keep-alive",
              "sec-ch-ua-platform": "\"Windows\"",
              "X-Requested-With": "XMLHttpRequest",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "Accept": "text/javascript, application/javascript, application/ecmascript, application/x-ecmascript, */*; q=0.01",
              "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
              "sec-ch-ua-mobile": "?0",
              "Sec-Fetch-Site": "same-origin",
              "Sec-Fetch-Mode": "cors",
              "Sec-Fetch-Dest": "empty",
              "Referer": "https://fund.eastmoney.com/company/default.html",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=e1bacded5f5a28dd248104caf6e543f1; _adsame_fullscreen_20308=1; st_si=32446072620882; st_asi=delete; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=2; st_psi=20250701135903414-112200312943-7940331382; ASP.NET_SessionId=ywyq5uxltuuktyyhphqxa5b1"
            }
        self.company_url="https://fund.eastmoney.com/Data/FundRankScale.aspx?_=1751349549721"

    def return_company_data(self):
        response = requests.get(self.company_url, headers=self.headers,verify=False)
        data = response.text.strip("var json={datas:").replace("}","")
        for i in eval(data):
            temp=[
                i[0],#代码
                i[1],#名称
                i[2],#成立时间
                i[8],#天相评级
                i[7],#全部管理规模（亿元）
                i[3],#全部基金数
                i[4],#总经理
                i[-1].split(" ")[0].replace("/","-"),#最新日期
            ]
            self.company_list.append(temp)


    def run(self):

        try:
            self.return_company_data()
            self.finished.emit(self.company_list)
        except Exception as e:
            print(f"run Error: {e}""")






