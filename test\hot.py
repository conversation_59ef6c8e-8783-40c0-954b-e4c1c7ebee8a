import json

import requests

url="https://searchadapter.eastmoney.com/api/HotKeyword/GetBatch?cb=jQuery35109401063130308724_1743079285885&count=20&stockCount=30&token=D43BF722C8E33BDC906FB84D85E326E8&_=1743079285887"
data=requests.get(url).text
data=data.strip("jQuery35109401063130308724_1743079285885(").replace(")","")
data=json.loads(data)
Currency=data["Data"]["Currency"]
Stock=data["Data"]["Stock"]
Currency_l=[]
Stock_l=[]
for i in Currency:
    Currency_l.append(i.get("KeyPhrase"))
for j in Stock:
    Stock_l.append(j.get("Name")+"["+j.get("Code")+"]")
print(Currency_l)