from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy
import json


class get_bkzj_data(QThread):
    finished = pyqtSignal(list, list,)

    def __init__(self, bk_type_index,key_index):
        super().__init__()
        self.header = {
            "Host": "data.eastmoney.com",
            "Connection": "keep-alive",
            "sec-ch-ua-platform": "\"Windows\"",
            "X-Requested-With": "XMLHttpRequest",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
            "Accept": "*/*",
            "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
            "sec-ch-ua-mobile": "?0",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://data.eastmoney.com/bkzj/hy.html",
            "Accept-Encoding": "gzip, deflate, br, zstd",
            "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
            "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; nid_id=568160114; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; EMFUND0=null; EMFUND4=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND5=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND6=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND8=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND9=08-04 12:35:13@#$%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; st_si=29313470527222; st_asi=delete; fullscreengg=1; fullscreengg2=1; _qimei_i_1=78df2afdce5b; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=16; st_psi=20250807140836345-113300300820-5630491407"
        }
        key_list=["f62","f164","f174"]
        bk_type=bk_type_index+1
        # 1,2,3 地域，行业，板块
        self.key=key_list[key_index]
        self.url=f"https://data.eastmoney.com/dataapi/bkzj/getbkzj?key={self.key}&code=m%3A90%2Bt%3A{bk_type}"

    def request_data(self):
        """获取真实的板块资金流向数据"""
        try:
            response = requests.get(self.url, headers=self.header, verify=False)
            data = response.json()

            # 获取diff数据列表
            diff_list = data["data"]["diff"]

            bk_name = []
            bk_value = []

            for item in diff_list:
                # 获取板块名称
                bk_name.append(item["f14"])
                value_in_yi = round(item[self.key] / 100000000, 1)
                bk_value.append(value_in_yi)
            return bk_name, bk_value

        except Exception as e:
            print(f"请求数据失败: {e}")
            return [], []

    def run(self):
        try:
            bk_name, bk_value = self.request_data()
            self.finished.emit(bk_name, bk_value)
        except Exception as e:
            print(f"Error in run method: {e}")





