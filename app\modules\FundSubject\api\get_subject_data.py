import json

import requests
from PyQt6.QtCore import QThread, pyqtSignal


class get_subject_data(QThread):
    finished = pyqtSignal(list)

    def __init__(self,type,st,time):
        super().__init__()
        self.headers = {
              "Host": "api.fund.eastmoney.com",
              "Connection": "keep-alive",
              "sec-ch-ua-platform": "\"Windows\"",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
              "sec-ch-ua-mobile": "?0",
              "Accept": "*/*",
              "Sec-Fetch-Site": "same-site",
              "Sec-Fetch-Mode": "no-cors",
              "Sec-Fetch-Dest": "script",
              "Referer": "https://fund.eastmoney.com/",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; ap_0_13a7d068=1; st_si=14019251600570; st_asi=delete; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=4; st_psi=20250512122756543-112200312942-5849709688"
            }
        self.type = type
        self.st = st
        self.time = time
        self.base_url=f"https://api.fund.eastmoney.com//ztjj/GetZTJJListNew?callback=jQuery18307353276434162012_1747024076549&tt={self.type}&dt={self.st}&st={self.time}&_=1747024076648"


    def get_subject_data(self):
        try:
            print(self.base_url)
            response = requests.get(self.base_url, headers=self.headers,verify=False)
            data = json.loads(response.text.strip("jQuery18307353276434162012_1747024076549(").rstrip(")"))
            result_list=[]
            for i in data["Data"]:
                if i[self.time] is not None:
                    result_list.append([i["INDEXNAME"],i[self.time]])
            return result_list
        except Exception as e:
            print(f"get_subject_data:{e}")

    def run(self):
        data = self.get_subject_data()
        self.finished.emit(data)



