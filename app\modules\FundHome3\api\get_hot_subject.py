from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy
import json


class get_hot_subject(QThread):
    finished = pyqtSignal(list, list,list )

    def __init__(self, ):
        super().__init__()
        self.header ={
              "Host": "api.fund.eastmoney.com",
              "Connection": "keep-alive",
              "sec-ch-ua-platform": "\"Windows\"",
              "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
              "sec-ch-ua": "\"Chromium\";v=\"136\", \"Microsoft Edge\";v=\"136\", \"Not.A/Brand\";v=\"99\"",
              "sec-ch-ua-mobile": "?0",
              "Accept": "*/*",
              "Sec-Fetch-Site": "same-site",
              "Sec-Fetch-Mode": "no-cors",
              "Sec-Fetch-Dest": "script",
              "Referer": "https://fund.eastmoney.com/",
              "Accept-Encoding": "gzip, deflate, br, zstd",
              "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
              "Cookie": "qgqp_b_id=50d74afee65419b05e9120f0df53c69f; ap_0_13a7d068=1; st_si=14019251600570; st_asi=delete; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=4; st_psi=20250512122756543-112200312942-5849709688"
            }

        self.url="https://api.fund.eastmoney.com//ztjj/GetZTJJListNew?callback=jQuery1830656951630887095_1754634598888&tt=0&dt=syl&st=Y&_=1754634598960"

    def request_data(self):

        try:
            response = requests.get(self.url, headers=self.header, verify=False)
            data = json.loads(response.text.strip("jQuery1830656951630887095_1754634598888(").rstrip(")"))
            print(data)
            subject_code=[]
            subject_name=[]
            subject_value=[]
            k=0
            for i in data["Data"]:
                if i["Y"] is not None and k<5:
                    subject_code.append(i["INDEXCODE"])
                    subject_name.append(i["INDEXNAME"])
                    subject_value.append(i["Y"])
                    k+=1
            return subject_code,subject_name,subject_value
        except Exception as e:
            print(f"get_hot_subject 请求数据失败: {e}")
            return [], [], []



    def run(self):
        try:
            subject_code,subject_name,subject_value= self.request_data()
            self.finished.emit(subject_code,subject_name,subject_value)
        except Exception as e:
            print(f"Error in run method: {e}")





