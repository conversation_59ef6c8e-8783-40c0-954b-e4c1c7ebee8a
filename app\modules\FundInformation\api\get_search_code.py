import json

import requests
from PyQt6.QtCore import QThread, pyqtSignal


class get_search_code(QThread):
    finished = pyqtSignal(list, list)

    def __init__(self,keyword):
        super().__init__()
        self.keyword = keyword
        self.name_list=[]
        self.search_result_list = []
        self.base_url = f"https://search-codetable.eastmoney.com/codetable/search/web?client=web&clientType=webSuggest&clientVersion=lastest&cb=jQuery35108576508717970958_1743838794116&keyword={self.keyword}&pageIndex=1&pageSize=10&securityFilter=&_=1743838794166"


    def request_data(self):
        data = requests.get(self.base_url, verify=False).text
        data = json.loads(data.strip("jQuery35108576508717970958_1743838794116(").replace(")", ""))
        search_r = data["result"]
        if len(search_r) > 0:
            for i in search_r:
                self.search_result_list.append(
                    f"{i["securityTypeName"]}     {i["code"]}    {i["shortName"]}    {i["pinyin"]}")
                self.name_list.append(i["shortName"])
        else:
            print(f"查看全部 {self.keyword} 搜索结果")
        # print(search_result_list)
        # return search_result_list, self.name_list

    def run(self):
        try:
            self.request_data()
            self.finished.emit(self.search_result_list,self.name_list, )

        except Exception as E:
            print()

#
# get_search_c=get_search_c()
# print(get_search_c.return_code_l("军工"))
