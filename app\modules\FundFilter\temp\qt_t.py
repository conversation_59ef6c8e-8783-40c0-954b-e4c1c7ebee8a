from PyQt6.QtWidgets import (QApplication, QMainWindow, QWidget, QVBoxLayout,
                             QHBoxLayout, QLabel, QPushButton, QScrollArea)
from PyQt6.QtCore import Qt, pyqtSignal
from functools import partial


class FundSelector(QMainWindow):
    def __init__(self, fund_data):
        super().__init__()
        self.fund_data = fund_data
        self.selected_funds = set()  # 存储选中的基金名称
        self.current_letter = None  # 当前选中的字母
        self.init_ui()

    def init_ui(self):
        self.setWindowTitle("基金选择器")
        self.resize(800, 600)

        main_widget = QWidget()
        layout = QVBoxLayout()

        # 1. 字母标签行
        self.letter_labels = self.create_letter_labels()
        layout.addLayout(self.letter_labels)

        # 2. 基金按钮展示区
        self.fund_display = self.create_fund_display()
        layout.addWidget(self.fund_display)

        main_widget.setLayout(layout)
        self.setCentralWidget(main_widget)

        # 默认显示A字母基金
        self.show_funds_for_letter('A')

    def create_letter_labels(self):
        """创建字母标签行"""
        letters = ['A', 'B', 'C', 'D', 'F', 'G', 'H', 'J', 'K', 'L', 'M',
                   'N', 'P', 'Q', 'R', 'S', 'T', 'W', 'X', 'Y', 'Z']

        container = QHBoxLayout()
        for letter in letters:
            label = QLabel(letter)
            label.setAlignment(Qt.AlignmentFlag.AlignCenter)
            label.setStyleSheet("""
                QLabel {
                    padding: 10px;
                    font-weight: bold;
                    border: 1px solid #ccc;
                    min-width: 30px;
                }
                QLabel:hover {
                    background-color: #f0f0f0;
                }
            """)
            label.mousePressEvent = partial(self.letter_clicked, letter)
            container.addWidget(label)

        return container

    def create_fund_display(self):
        """创建基金按钮展示区"""
        container = QWidget()
        layout = QVBoxLayout()

        # 滚动控制按钮
        btn_row = QHBoxLayout()
        self.left_scroll = QPushButton("◀")
        self.right_scroll = QPushButton("▶")

        self.left_scroll.clicked.connect(self.scroll_left)
        self.right_scroll.clicked.connect(self.scroll_right)

        btn_row.addWidget(self.left_scroll)
        btn_row.addStretch()
        btn_row.addWidget(self.right_scroll)

        # 基金按钮区域
        self.scroll_area = QScrollArea()
        self.scroll_area.setWidgetResizable(True)

        self.fund_container = QWidget()
        self.fund_layout = QHBoxLayout()
        self.fund_container.setLayout(self.fund_layout)

        self.scroll_area.setWidget(self.fund_container)

        layout.addLayout(btn_row)
        layout.addWidget(self.scroll_area)
        container.setLayout(layout)

        return container

    def letter_clicked(self, letter, event):
        """字母标签点击事件"""
        self.current_letter = letter
        self.show_funds_for_letter(letter)

    def show_funds_for_letter(self, letter):
        """显示指定字母对应的基金"""
        # 清空现有按钮
        while self.fund_layout.count():
            item = self.fund_layout.takeAt(0)
            widget = item.widget()
            if widget:
                widget.deleteLater()

        # 添加新按钮
        if letter in self.fund_data:
            for fund_code, fund_name in self.fund_data[letter]:
                btn = QPushButton(fund_name)
                btn.setCheckable(True)
                btn.setStyleSheet("""
                    QPushButton {
                        padding: 8px;
                        margin: 2px;
                        border: 1px solid #ddd;
                    }
                    QPushButton:checked {
                        background-color: #1890ff;
                        color: white;
                    }
                """)
                btn.clicked.connect(partial(self.fund_button_clicked, fund_name))
                self.fund_layout.addWidget(btn)

        # 添加伸缩项使按钮左对齐
        self.fund_layout.addStretch()

    def fund_button_clicked(self, fund_name):
        """基金按钮点击事件"""
        sender = self.sender()
        if sender.isChecked():
            self.selected_funds.add(fund_name)
        else:
            self.selected_funds.discard(fund_name)
        print("当前选中基金:", self.selected_funds)

    def scroll_left(self):
        """向左滚动"""
        scroll_bar = self.scroll_area.horizontalScrollBar()
        scroll_bar.setValue(scroll_bar.value() - 100)

    def scroll_right(self):
        """向右滚动"""
        scroll_bar = self.scroll_area.horizontalScrollBar()
        scroll_bar.setValue(scroll_bar.value() + 100)


if __name__ == "__main__":
    # 示例数据
    fund_data = {'A': [['81608035', '安联'], ['80163340', '安信']], 'B': [['80000226', '博时'], ['80538609', '渤海汇金'], ['80560392', '博道'], ['81067205', '贝莱德管理'], ['80280039', '北京京管泰富'], ['81102344', '百嘉'], ['80365985', '北信瑞丰'], ['80000236', '宝盈'], ['80560400', '博远']], 'C': [['80000239', '长城'], ['80045894', '诚通证券'], ['80161341', '财通'], ['80139382', '长安'], ['80965167', '淳厚'], ['80380794', '创金合信'], ['80000082', '长城证券'], ['80404701', '财通资管'], ['80000165', '财达证券'], ['80404004', '长江证券(上海)资管'], ['80000140', '财信证券']], 'D': [['80145102', '东方红资产管理'], ['80000081', '东莞证券'], ['80205268', '东海'], ['80175511', '德邦'], ['80560388', '东方阿尔法'], ['80560384', '东财'], ['80000069', '东海证券'], ['80434808', '德邦证券资管'], ['80114781', '东兴证券'], ['80703549', '东兴'], ['80000083', '第一创业'], ['80048161', '东吴'], ['80701749', '达诚'], ['80489094', '东证融汇证券资产管理'], ['80000225', '大成'], ['80042861', '东方'], ['80000031', '东吴证券']], 'F': [['80174741', '方正富邦'], ['80000221', '富国'], ['81079752', '富达(中国)'], ['80000068', '方正证券'], ['80488954', '富荣'], ['80128562', '富安达'], ['80560383', '蜂巢']], 'G': [['80044515', '国海富兰克林'], ['80000233', '国投瑞银'], ['80156175', '国泰君安资管'], ['80357951', '广发资产管理'], ['81735243', '国信证券资产管理'], ['80355783', '国寿安保'], ['80043374', '国联安'], ['81645984', '国联证券资产管理'], ['80048088', '光大保德信'], ['80084302', '国投证券'], ['80000028', '国联民生'], ['80000095', '国都证券'], ['10001055', '国海证券'], ['80975669', '国新国证'], ['80000224', '国泰'], ['80092743', '国新证券股份'], ['80102419', '国金'], ['80000007', '国信证券'], ['80560389', '国融'], ['80548351', '格林'], ['81052915', '国投证券资产管理'], ['80000248', '广发'], ['80341238', '国联'], ['80064225', '工银瑞信'], ['10001043', '国元证券']], 'H': [['80199117', '华润元大'], ['80391977', '华泰证券(上海)资产管理'], ['80201857', '华宸未来'], ['80055334', '华泰柏瑞'], ['80037023', '华富'], ['80000238', '宏利'], ['80000049', '华鑫证券'], ['81645210', '华安证券资产管理'], ['81678136', '汇百川'], ['80067635', '汇丰晋信'], ['80000228', '华安'], ['80053204', '华商'], ['80385906', '红土创新'], ['80523667', '华泰保兴'], ['80560379', '弘毅远方'], ['80000064', '华安证券'], ['81252907', '华西'], ['80424273', '泓德'], ['80664533', '汇泉'], ['80560396', '合煦智远'], ['80560380', '恒越'], ['80924817', '惠升'], ['80498278', '汇安'], ['80508384', '恒生前海'], ['80000222', '华夏'], ['80205263', '红塔红土'], ['80000045', '恒泰证券'], ['80000202', '华创证券'], ['80053708', '汇添富'], ['80000250', '华宝'], ['80000246', '海富通']], 'J': [['80000223', '嘉实'], ['80065990', '建信'], ['80086876', '金元顺安'], ['80064562', '交银施罗德'], ['80384640', '九泰'], ['80000251', '景顺长城'], ['80365987', '嘉合'], ['80446423', '金信'], ['80000245', '金鹰'], ['80205264', '江信']], 'K': [['80560381', '凯石']], 'L': [['81067208', '路博迈(中国)'], ['81423074', '联博']], 'M': [['80050229', '摩根资产管理'], ['80036797', '摩根士丹利'], ['80106677', '民生加银'], ['80664536', '明亚']], 'N': [['80068180', '诺德'], ['80000051', '南京证券'], ['80000220', '南方'], ['80092233', '农银汇理'], ['80555446', '南华'], ['80049689', '诺安']], 'P': [['80000037', '平安证券'], ['80168726', '平安'], ['80091787', '浦银安盛'], ['80522693', '鹏扬'], ['80000230', '鹏华']], 'Q': [['80468996', '前海联合'], ['80280038', '前海开源'], ['81275389', '泉果']], 'R': [['81046799', '瑞达'], ['80000231', '融通'], ['80061431', '人保资产'], ['80672691', '睿远']], 'S': [['80045188', '申万菱信'], ['80000155', '上海证券'], ['81603559', '苏新'], ['80404011', '申万宏源证券'], ['80184574', '上海海通证券资产管理'], ['80366080', '上银'], ['80959746', '尚正'], ['80192219', '上海光大证券资产管理'], ['81466193', '申万宏源证券资产管理'], ['81423076', '施罗德(中国)'], ['81248979', '山证(上海)资产管理'], ['80000080', '山西证券']], 'T': [['80294346', '太平'], ['80000124', '天风证券'], ['80041198', '天弘'], ['80000247', '泰信'], ['80050806', '太平洋'], ['81246345', '泰康'], ['80000252', '天治'], ['80560408', '同泰'], ['81102050', '天风(上海)证券资产管理']], 'W': [['80000240', '万家'], ['81573597', '万联资管']], 'X': [['80351991', '鑫元'], ['80147736', '西部利得'], ['80000076', '湘财证券'], ['80074234', '信达澳亚'], ['80000249', '新华'], ['80368700', '兴银管理'], ['80452130', '新沃'], ['80374411', '兴证资管'], ['81102343', '兴华'], ['80036742', '兴证全球'], ['80501166', '先锋'], ['81021264', '兴合'], ['80280397', '湘财'], ['80092742', '信达证券'], ['10000296', '西南证券'], ['80280395', '兴业'], ['80000139', '西部证券']], 'Y': [['80046522', '益民'], ['80000229', '易方达'], ['80356155', '永赢'], ['80280036', '圆信永丰'], ['80369323', '银河金汇证券'], ['80000237', '银河'], ['80000118', '粤开证券'], ['80000235', '银华'], ['80175498', '英大'], ['80560390', '易米']], 'Z': [['80000227', '长盛'], ['80000243', '长信'], ['80066470', '中信保诚'], ['80046614', '中海'], ['80971976', '中信证券资产管理'], ['80000218', '中原证券'], ['80560407', '朱雀'], ['80431710', '招商证券资管'], ['80455765', '中科沃土'], ['80365986', '中金'], ['80351345', '中加'], ['80355113', '中信建投'], ['80066058', '中信建投'], ['80000200', '中银证券'], ['80403111', '浙商证券资管'], ['80075936', '中邮'], ['80065113', '中欧'], ['80016241', '中金公司'], ['80560391', '中庚'], ['80508391', '中航'], ['10000018', '中信证券'], ['80048752', '中银'], ['80066522', '中金财富'], ['80381452', '中泰证券(上海)资管'], ['80036782', '招商'], ['80156777', '浙商']]}


    app = QApplication([])
    window = FundSelector(fund_data)
    window.show()
    app.exec()