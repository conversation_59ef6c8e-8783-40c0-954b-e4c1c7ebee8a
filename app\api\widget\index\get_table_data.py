import json
import math

import requests

class Table_data:
    def __init__(self):
        self.page_url_list=[]
        self.table_name=""

    def get_table_data(self,table_item):
        match (table_item):
            case "zs":
                self.page_url_l = ["https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37108984705702929345_1741865246156&fs=m%3A2&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf5%2Cf6%2Cf7%2Cf15%2Cf18%2Cf16%2Cf17%2Cf10&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&_=1741865246312",
                                   "https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37101592046045304214_1741865253788&fs=m%3A1%2Bt%3A1&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf5%2Cf6%2Cf7%2Cf15%2Cf18%2Cf16%2Cf17%2Cf10&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&_=1741865253793",
                                   "https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery371067168272126367_1741865323399&fs=m%3A0%2Bt%3A5&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf5%2Cf6%2Cf7%2Cf15%2Cf18%2Cf16%2Cf17%2Cf10&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&_=1741865323403",""]
                headers = ['代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量(手)', '成交额', '振幅', '最高', '最低','今开','昨收' ]
                btn_name = ["中证系列指数", "上证系列指数", "深圳系列指数", ]
                blue_pos=[1,2]
                gr_pos=[4,5]
                self.table_name = "zs"
            case "qz":
                self.page_url_l = ["https://futsseapi.eastmoney.com/list/115?callbackName=jQuery37101231601946064611_1741912262085&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741912262090",
                                    "https://futsseapi.eastmoney.com/list/225?callbackName=jQuery37107993860468508374_1741916700365&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741916700389",
                                    "https://futsseapi.eastmoney.com/list/220?callbackName=jQuery37109328292741254196_1741926939240&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741926939250",
                                    "https://futsseapi.eastmoney.com/list/114?callbackName=jQuery371037809900008308706_1741927713413&field=dm%2Csc%2Cname%2Cp%2Czdf%2Czsjd%2Czde%2Co%2Czjsj%2Ch%2Cl%2Cvol%2Ccje%2Cwp%2Cnp%2Cccl&token=58b2fa8f54638b60b87d69b31969089c&orderBy=zdf&sort=desc&pageSize=20&pageIndex=0&blockName=callback&_=1741927713417"]
                headers = ['代码', '名称', '最新价', '涨跌额', '涨跌幅', '今开', '最高', '最低','昨结' ,'成交量', '成交额', '买盘（外盘）','卖盘（内盘）','持仓量']
                btn_name = ["郑商所", "广期所", "中金所", "大商所"]
                blue_pos = [1, 2]
                gr_pos = [4, 5]
                self.table_name = "qz"

            case "qq":
                self.page_url_l = ["https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37106903001718821398_1742017746552&fs=m%3A10&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf5%2Cf6%2Cf108%2Cf161%2Cf330%2Cf162%2Cf163%2Cf28%2Cf17&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&_=1742017746556",
                                  "https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37106903001718821398_1742017746552&fs=m%3A12&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf5%2Cf6%2Cf108%2Cf161%2Cf330%2Cf162%2Cf163%2Cf28%2Cf17&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&_=1742017746702",
                                  "https://quote.eastmoney.com/center/gridlist.html#options_cffex_all",
                                  "https://quote.eastmoney.com/center/gridlist.html#options_shfe_all"]
                headers = ['代码', '名称', '最新价', '涨跌额', '涨跌幅', '成交量', '成交额', '持仓量', '行权价', '剩余日','日增', '昨结', '今开']

                btn_name = ["上交所","深交所","中金所","上期所"]
                blue_pos = [1, 2]
                gr_pos = [4, 5]
                self.table_name = "qq"

            case "bk":
                self.page_url1 = ["https://quote.eastmoney.com/center/boardlist.html#industry_board",
                                  "https://quote.eastmoney.com/center/boardlist.html#concept_board",
                                  "https://quote.eastmoney.com/center/boardlist.html#region_board"]
                headers = ['板块名称', '最新价', '涨跌额', '涨跌幅', '总市值', '换手率', '上涨家数', '下跌家数','领涨股票', '股票涨跌幅']
                data_l = ['test1', 'test2', 'test3', '']
                btn_name = ["行业板块", "概念板块", "地域板块"]
            case "ph":
                self.page_url1 = ["https://quote.eastmoney.com/center/gridlist.html#hs_a_board",
                                  "https://quote.eastmoney.com/center/gridlist.html#sh_a_board",
                                  "https://quote.eastmoney.com/center/gridlist.html#sz_a_board",
                                  "https://quote.eastmoney.com/center/gridlist.html#bj_a_board"]
                headers = ['代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量(手)', '成交额', '振幅', '最高', '最低', '今开','昨收', '量比', '换手率', '市盈率（动态）', '市净率']
                data_l = ['test1', 'test2', 'test3', 'test4']
                btn_name = ["沪深京A股","上证A股","深证A股","北证A股"]
            case "xg":
                self.page_url1 = ["https://quote.eastmoney.com/center/gridlist.html#newshares"]
                headers = ['代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量(手)', '成交额', '振幅', '最高', '最低', '今开','昨收', '量比', '换手率', '市盈率（动态）', '市净率']
                data_l = ['test1',"","",""]
                btn_name = ["个股-新发股"]
            case "cyb":
                self.page_url1 = ["https://quote.eastmoney.com/center/gridlist.html#gem_board",
                                  "https://quote.eastmoney.com/center/gridlist.html#gem_board_zcz",
                                  "https://quote.eastmoney.com/center/gridlist.html#gem_board_hzz"]
                headers = ['代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量(手)', '成交额', '振幅', '最高', '最低', '今开',
                           '昨收', '量比', '换手率', '市盈率（动态）', '市净率']
                data_l = ['test1', 'test2', 'test3',""]
                btn_name = ["全部创业板","注册制创业板","核准制创业板"]
            case "kcb":
                self.page_url1 = ["https://quote.eastmoney.com/center/gridlist.html#kcb_board"]
                headers = ['代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量(手)', '成交额', '振幅', '最高', '最低', '今开',
                           '昨收', '量比', '换手率', '市盈率（动态）', '市净率']
                data_l = ["test1", "", "", ""]
                btn_name = ["个股-科创板"]
            case "jj":
                self.page_url_l = ["https://quote.eastmoney.com/center/gridlist.html#fund_close_end",
                                   "https://quote.eastmoney.com/center/gridlist.html#fund_etf",
                                   "https://quote.eastmoney.com/center/gridlist.html#fund_lof"]
                headers = ['代码', '名称', '最新价', '涨跌额', '涨跌幅', '成交量', '成交额', '开盘价', '最高价', '最低价',
                           '昨收']
                data_l = ['test1', 'test2', 'test3', '']
                btn_name = ["封闭基金行情", "EFT基金行情", "LOF基金行情"]
            case "gg":
                self.page_url_l = ["https://quote.eastmoney.com/center/gridlist.html#hk_stateowned",
                                   "https://quote.eastmoney.com/center/gridlist.html#hk_redchips",
                                   "https://quote.eastmoney.com/center/gridlist.html#hk_bluechips",
                                   "https://quote.eastmoney.com/center/gridlist.html?sr=0#hk_gem"]
                headers = ['代码', '名称', '最新价', '涨跌额', '涨跌幅', '今开', '最高', '最低', '昨收', '成交量（股）',
                           '成交额', ]
                data_l = ['test1', 'test2', 'test3', 'test4']
                btn_name = ["国企股行情", "红筹股行情", "蓝筹股行情", "创业板行情"]
            case "hs":
                self.page_url_l = ["https://quote.eastmoney.com/center/gridlist.html#sh_hk_board",
                                   "https://quote.eastmoney.com/center/gridlist.html#sz_hk_board",
                                   "https://quote.eastmoney.com/center/gridlist.html#hk_sh_stocks",
                                   "https://quote.eastmoney.com/center/gridlist.html#hk_sz_stocks"]
                headers = ['代码', '名称', '最新价', '涨跌幅', '涨跌额', '成交量(手)', '成交额', '振幅', '最高', '最低', '今开',
                           '昨收', '量比', '换手率', '市盈率（动态）', '市净率', "上市时间"]
                # headers = ['名称', 'H股股票', '最新价(HKD)', '涨跌幅', 'A股代码', '最新价(RMB)', '涨跌幅', '比价(A/H)',
                #            '溢价（A/H）%']
                data_l = ['test1', 'test2', 'test3', '']
                btn_name = ["沪股通(港>沪)", "深股通(港>深)","港股通(沪>港)", "港股通(深>港)"]
            case "mg":
                self.page_url_l = ["https://quote.eastmoney.com/center/gridlist.html#us_wellknown",
                                   "https://quote.eastmoney.com/center/gridlist.html#us_chinese",
                                   "https://quote.eastmoney.com/center/gridlist.html#us_chinese_internet",
                                   "https://quote.eastmoney.com/center/gridlist.html#us_technology"]
                headers = ['名称', '最新价(美元)', '涨跌额', '涨跌幅', '开盘价', '成交额', '最高价', '最低价', '昨收价',
                           '总市值 (美元)', '市盈率']
                data_l = ['test1', 'test2', 'test3', 'test4']
                btn_name = ["知名美股涨幅榜", "中概股涨幅榜", "互联网中国榜", "科技类涨幅榜"]
            case "qh":
                self.page_url_l = ["https://quote.eastmoney.com/center/gridlist.html#futures_finance",
                                   "https://quote.eastmoney.com/center/gridlist.html#futures_energy",
                                   "https://quote.eastmoney.com/center/gridlist.html#futures_metal",
                                   "https://quote.eastmoney.com/center/gridlist.html#futures_farmproduce"]
                headers = ['代码', '名称', '最新价', '涨跌额', '涨跌幅', '今开', '最高', '最低', '昨结', '成交量', '买盘(外盘)',
                           '卖盘(内盘)', '持仓量']
                data_l = ['test1', 'test2', 'test3', 'test4']
                btn_name = ["金融期货", "能源化工", "金属钢材", "农产品食品原料"]
            case "hj":
                self.page_url_l = ["https://quote.eastmoney.com/center/gridlist.html#nobalmetal_futures",
                                   "https://quote.eastmoney.com/center/gridlist.html#gold_sh_futures",
                                   "https://quote.eastmoney.com/center/gridlist.html#gold_sh_spotgoods",
                                   "https://quote.eastmoney.com/center/gridlist.html#nobalmetal_spotgoods"]
                headers = ['代码', '品种', '最新价', '涨跌额', '涨跌幅', '今开', '最高', '最低', '昨结', '更新时间', ]
                data_l = ['test1', 'test2', 'test3', 'test4']
                btn_name = ["国际贵金属期货", "上海黄金期货", "上海黄金现货", "国际贵金属现货"]
            case  "wh":
                self.page_url_l = ["https://quote.eastmoney.com/center/gridlist.html#forex_cny",
                                   "https://quote.eastmoney.com/center/gridlist.html#forex_basic",
                                   "https://quote.eastmoney.com/center/gridlist.html#forex_cross",
                                   ]
                headers = ['代码', '名称', '最新价', '涨跌额', '涨跌幅', '今开', '最高', '最低', '昨收']
                data_l = ['test1', 'test2', 'test3', '']
                btn_name = ["人民币中间价", "基本汇率", "交叉汇率"]
        self.page_url_list=self.page_url_l
        return headers,len(headers),btn_name,blue_pos,gr_pos

    def return_index_tb(self,index,page):
        print("0909")
        url=self.page_url_list[index]

        # with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\js.txt","w",encoding="utf8")as f:
        #     f.write(data.text)
        # with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\js.txt", "r", encoding="utf8") as f:
        #     data1 = f.read()
        data_rr=[]
        match (self.table_name):
            case "zs":
                data_rr=self.table_zs(url,page)
            case "qz":
                data_rr = self.table_qz(url,page)
            case "qq":
                data_rr = self.table_qq(url, page)
        print("***")
        return data_rr

    def trans_num(self,num):
        if num=="-":
            return "-"
        if num < 10000:
            return str(round(num, 2))  # 保留2位小数
        elif 10000 <= num < 100_000_000:  # 1万到1亿
            simplified = round(num / 10_000, 2)  # 四舍五入并保留2位小数
            return f"{simplified}万"
        elif 100_000_000 <= num < 1_000_000_000_000:  # 1亿到10000亿
            simplified = round(num / 100_000_000, 2)  # 四舍五入并保留2位小数
            return f"{simplified}亿"
        else:  # 大于10000亿
            simplified = round(num / 1_000_000_000_000, 2)  # 四舍五入并保留2位小数
            return f"{simplified}万亿"



    def table_zs(self,url,c_page):
        data1 = requests.get(url.replace("pn=1",f"pn={c_page}")).text
        data_list = []
        r_data = []
        dict_m = json.loads(data1.strip("jQuery37104737975312937226_1741851173190(").replace(');', ''))["data"]["diff"]
        page=json.loads(data1.strip("jQuery37104737975312937226_1741851173190(").replace(');', ''))["data"]['total']
        page = math.ceil(page / 20)
        print()
        for i in dict_m:
            data_list.append(list(i.values()))
        for i in data_list:
            r_data.append(
                [i[8], i[10], "{:.2f}".format(i[1] / 100), "{:.2f}%".format(i[2] / 100),
                 "{:.2f}".format(i[3] / 100),
                 self.trans_num(i[4]), self.trans_num(i[5]), "{:.2f}%".format(i[6] / 100), "{:.2f}".format(i[11] / 100),
                 "{:.2f}".format(i[12] / 100),
                 "{:.2f}".format(i[13] / 100), "{:.2f}".format(i[14] / 100)])
        return r_data,len(r_data),page

    def table_qz(self,url,c_page):
        data1 = requests.get(url.replace("pageIndex=0", f"pageIndex={c_page-1}")).text
        data_list = []
        r_data = []
        print(url.replace("pageIndex=0", f"pageIndex={c_page-1}"))
        dict_m = json.loads(data1.strip("jQuery37101231601946064611_1741912262085(").replace(')', ''))["list"]
        page = json.loads(data1.strip("jQuery37101231601946064611_1741912262085(").replace(')', ''))["total"]
        # dict_m = eval(data1.strip("jQuery37101231601946064611_1741912262085(").replace(")", ""))["list"]
        page = math.ceil(page / 20)
        for i in dict_m:
            data_list.append(list(i.values()))
        # print(data_list[0][12])
        def convert_(obj):
            v=""
            if obj=="-":
                v="-"
            else:
                v="{}%".format(obj)
            return v
        for i in data_list:
            r_data.append(
                [
                    i[2],  # 代码
                    i[10],  # 名称
                    i[7],
                    i[12],
                    convert_(i[13]),
                    i[6],
                    i[1],
                    i[4],
                    i[-2],
                  self.trans_num(i[9]),  # 成交量
                  self.trans_num(i[-1]),  # 成交额
                    i[11],
                    i[0],
                    i[5],
                ])
                # [i[2],  # 代码
                #  i[10],  # 名称
                #  "{:.1f}".format(float(i[7])),  # 最新价
                #  "{:.1f}".format(float(i[12])),  # 涨跌额
                #  "{:.2f}%".format(float(i[13])),  # 涨跌幅
                #  "{:.1f}".format(float(i[6])),  # 今开
                #  "{:.1f}".format(float(i[1])),  # 最高
                #  "{:.1f}".format(float(i[4])),  # 最低
                #  "{:.1f}".format(float(i[-2])),  # 昨结
                #  self.trans_num(i[9]),  # 成交量
                #  self.trans_num(i[-1]),  # 成交额
                #  "{}".format(int(i[11])),  # 买盘
                #  "{}".format(int(i[0])),  # 卖盘
                #  "{}".format(int(i[5]))])  # 持仓量
        return r_data,len(r_data),page

    def table_qq(self,url,c_page):
        data1 = requests.get(url.replace("pn=1", f"pn={c_page}")).text
        data_list = []
        r_data = []
        dict_m = json.loads(data1.strip("jQuery37106903001718821398_1742017746552(").replace(');', ''))["data"]["diff"]
        page = json.loads(data1.strip("jQuery37106903001718821398_1742017746552(").replace(');', ''))["data"]['total']
        page = math.ceil(page / 20)
        for i in dict_m:
            data_list.append(list(i.values()))
        for i in data_list:
            r_data.append([
                i[6]  # 代码
                , i[8],  # 名称
                "{:.4f}".format(i[1] / 10000),  # 最新价
                "{:.4f}".format(i[3] / 10000),  # 涨跌额
                "{:.2f}%".format(i[2] / 100),  # 涨跌幅
                self.trans_num(i[4]),  # 成交量
                self.trans_num(i[5]),  # 成交额
                self.trans_num(i[-6]),  # 持仓量
                "{:.3f}".format(i[-4] / 1000),  # 行权价
                i[-3],  # 剩余日
                i[-2],  # 日增
                "{:.4f}".format(i[-7] / 10000),  # 昨结
                "{:.4f}".format(i[-8] / 10000)])  # 昨结
        return r_data, len(r_data), page


    def covert_(self,obj,hold=0,percent=False):
        v=""
        if isinstance(obj, str):  # 判断是否为字符串
            if obj=="-":
                v=""
            else:
                v=obj
        elif isinstance(obj, float) and hold==1:
            # 判断是否为浮点数
            v= "{:.1f}".format(obj)
        elif isinstance(obj, float) and hold==2:
            if percent:
                v = "{:.2f}%".format(obj)
            # 判断是否为浮点数
            else:v= "{:.2f}".format(obj)
        elif isinstance(obj,int):
            v= "{}".format(int(obj))
        return v

table_data=Table_data()