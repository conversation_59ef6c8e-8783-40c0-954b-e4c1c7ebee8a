import sys
import os

import pandas as pd
import requests
import json
import tushare as ts
def get_stock_data():
    # # 设置你的 Token （这里方便以后的Demo放在了TXT文件中）
    with open('../../txt/tushareToken.txt', 'r', encoding='utf-8') as file:
        token = file.read().strip()
        ts.set_token(token)
    pro = ts.pro_api()

    # 获取某支股票的每日行情，示例为 '600519.SH'
    df = pro.daily(ts_code='600519.SH',
                   start_date='20240101', end_date='20241003')

    # 将日期设置为索引并按时间排序
    df.index = pd.to_datetime(df['trade_date'])
    df = df.sort_index()
    print(df)

    # 提取需要的列（日期、开盘、收盘、最高、最低）
    stock_data = df[['open', 'close', 'high', 'low']]

    # 生成 K 线图所需的数组
    kline_data = []
    for row in stock_data.itertuples():
        kline_data.append([row.open, row.close, row.low, row.high])
    print(kline_data)
    # 日期
    dates = stock_data.index.strftime('%Y-%m-%d').tolist()

    # 生成关键点
    point_data = [{
        "name": 'highest value',
        "type": 'max',
        "valueDim": 'highest'
    },
        {
            "name": 'lowest value',
            "type": 'min',
            "valueDim": 'lowest'
        }]

    # 生成平均值
    # markLine = self.get_markline_data(stock_data)
    print(stock_data)

    return {
        'dates': dates,
        'kline_data': kline_data,
        'point_data': point_data,
        # 'markLine': markLine,
    }
print(get_stock_data())

