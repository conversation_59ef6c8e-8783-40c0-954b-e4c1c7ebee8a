from pypinyin import pinyin, Style

def get_pinyin_initial(name):
    """获取拼音首字母并转为大写"""
    initials = pinyin(name, style=Style.FIRST_LETTER)

    return str(initials[0][0]).upper()

def list_to_dict(data):
    """将列表转换为字典"""
    result = {}
    for code, name in data:
        initial = get_pinyin_initial(name)
        if initial not in result:
            result[initial] = []
        result[initial].append([code, name])
    return result

# 示例数据
data = [
    ["81608035", "安联基金"],
    ["80163340", "安信基金"],
    ["80000226", "博时基金"]
]

# 转换为字典
result_dict = list_to_dict(data)
print(result_dict)