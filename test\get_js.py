#获取指定网址的数据
import json

import requests
url="https://push2.eastmoney.com/api/qt/clist/get?np=1&fltt=1&invt=2&cb=jQuery37108984705702929345_1741865246156&fs=m%3A2&fields=f12%2Cf13%2Cf14%2Cf1%2Cf2%2Cf4%2Cf3%2Cf152%2Cf5%2Cf6%2Cf7%2Cf15%2Cf18%2Cf16%2Cf17%2Cf10&fid=f3&pn=1&pz=20&po=1&dect=1&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&_=1741865246312"
data=requests.get(url).text
# with open("../app/api/txt/js.txt","w",encoding="utf8")as f:
#     f.write(data.text)
#
# with open("../app/api/txt/js.txt", "r", encoding="utf8")as f:
#     data1=f.read()
data_list=[]
dict_m= json.loads(data.strip("jQuery37104737975312937226_1741851173190(").replace(');', ''))["data"]['total']
print(dict_m)
def trans_num(num):
    if num < 10000:
        return str(round(num, 2))  # 保留2位小数
    elif 10000 <= num < 100_000_000:  # 1万到1亿
        simplified = round(num / 10_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}万"
    elif 100_000_000 <= num < 1_000_000_000_000:  # 1亿到10000亿
        simplified = round(num / 100_000_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}亿"
    else:  # 大于10000亿
        simplified = round(num / 1_000_000_000_000, 2)  # 四舍五入并保留2位小数
        return f"{simplified}万亿"
# for i in dict_m:
#     data_list.append(list(i.values()))
# # print(data_list)
#
# def t1():
#     r_data = []
#     for i in data_list:
#         r_data.append([i[6],i[8],"{:.2f}".format(i[1]/100),"{:.2f}".format(i[3]/100),"{:.2f}%".format(i[2]/100),trans_num(i[4]),trans_num(i[5]),"{:.2f}".format(i[12]/100),"{:.2f}".format(i[11]/100),"{:.2f}".format(i[9]/100),"{:.2f}".format(i[10]/100)])
#         return r_data
# def t2():
#     r_data = []
#     for i in data_list:
#         r_data.append(
#             [i[8], i[10], "{:.2f}".format(i[1] / 100), "{:.2f}%".format(i[2] / 100), "{:.2f}".format(i[3] / 100),
#              trans_num(i[4]), trans_num(i[5]), "{:.2f}%".format(i[6] / 100), "{:.2f}".format(i[11] / 100),"{:.2f}".format(i[12] / 100),
#              "{:.2f}".format(i[13] / 100), "{:.2f}".format(i[14] / 100)])
#     return r_data
# r_data=t2()
# rows=len(r_data)
# cols=len(r_data[0])
# print(rows,cols)
# for row in range(rows):
#     for col in range(cols):
#         print(r_data[row][col],end=",")
#     print()