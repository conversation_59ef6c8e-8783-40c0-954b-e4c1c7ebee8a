#!/usr/bin/env python3
"""
简单测试脚本，验证Markdown功能
"""

try:
    # 测试基本导入
    from app.modules.FundAiChat.FundAiChat import MarkdownWebView
    print("✅ MarkdownWebView导入成功")
    
    # 测试markdown导入
    import markdown
    print("✅ markdown库导入成功")
    
    # 测试markdown转换
    test_md = "# 测试\n\n这是**粗体**文本。"
    html_result = markdown.markdown(test_md)
    print(f"✅ Markdown转换成功: {len(html_result)} 字符")
    
    # 测试PyQt6 WebEngine
    from PyQt6.QtWebEngineWidgets import QWebEngineView
    print("✅ QWebEngineView导入成功")
    
    print("\n🎉 所有基础功能测试通过！")
    print("\n📝 修改总结:")
    print("1. 新增MarkdownWebView组件用于渲染Markdown")
    print("2. 修改add_ai_message_stream方法使用WebView替代QLabel")
    print("3. 支持流式输出的实时Markdown渲染")
    print("4. 保持原有的UI样式和交互逻辑")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
