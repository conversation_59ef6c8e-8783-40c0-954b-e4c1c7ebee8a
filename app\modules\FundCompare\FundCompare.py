from functools import partial

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON>er<PERSON>iew, QTableWidgetItem, QWidget, QHBoxLayout, QPushButton, QLineEdit, QMessageBox

from app.common.api.code_name import FundCodeName
from app.modules.FundCompare.api.return_match_input import match_fund_code
from app.modules.FundCompare.src import style
from app.modules.FundCompare.DataHandle import DataHandle
from app.modules.FundCompare.api.get_compare_ import get_compare_data


class FundCompare:
    def __init__(self, ui):
        self.ui = ui
        self.init_setup()

    def init_setup(self):  # 初始化变量
        self.show_jjbj_status = False
        self.jjbj_listWidget_status=False
        self.jjbj_selectWidget_status=False
        self.compare_file_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundCompare\src\select_fund.txt"
        self.select_other_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSelection\src\select_\other"
        self.select_default_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundSelection\src\select_\default\默认.txt"
        self.jjbj_graph_seq = 0
        # self.code_list = ["011971", "014319", "001691", "000011", "001751", "009512", "610008", "610108", "002405",
        #                   "004253"]
        self.code_list = self.get_compare_pool()
        self.today = DataHandle.get_today_date()
        self.jjbj_formatted_dates = DataHandle.get_past_dates()
        # self.code_list=["011971","014319","001691","000011",]
        # self.code_list=["011971","014319","001691"]
        self.ui.compare_widget.hide()
        self.ui.jjbj_listWidget.hide()
        self.ui.jjbj_select_widget_a.hide()

        self.ui.jjbj_return_lb.mousePressEvent = self.toggle_jjbj_index
        self.ui.jj_compare_lb.mousePressEvent=self.show_compare_widget
        self.ui.jjbj_lineEdit.mousePressEvent=self.custom_mouse_press_jjbj
        self.ui.jjbj_listWidget.itemClicked.connect(self.jjbj_listWidget_select)
        self.ui.jjbj_clear_lb.mousePressEvent = self.clear_compare_pool
        self.ui.compare_pool_tableWidget.horizontalHeader().sectionClicked.connect(self.on_header_clicked)
        self.ui.jjbj_select_btn.mousePressEvent = self.toggle_select_
        self.ui.jjbj_grouplistWidget.itemClicked.connect(self.jjbj_grouplistWidget_select)

    def toggle_select_(self,event):
        if self.jjbj_selectWidget_status:
            self.ui.jjbj_select_widget_a.hide()
            self.jjbj_selectWidget_status=False
            self.ui.jjbj_select_btn.setText("从自选基金添加")
        else:
            self.ui.jjbj_select_widget_a.show()
            self.load_select_fund_group()
            self.jjbj_selectWidget_status = True
            self.ui.jjbj_select_btn.setText("完成添加")

    def load_select_fund_group(self):
        try:
            self.select_fund_group=DataHandle.get_fund_group_data()
            self.ui.jjbj_fund_groups.clear()
            self.ui.jjbj_fund_groups.addItems(self.select_fund_group)
            self.ui.jjbj_fund_groups.currentTextChanged.connect(self.load_group_fund_code)
            with open(self.select_default_path, "r", encoding="utf-8") as f:
                code_name_list = f.readlines()
            self.load_default_fund_group(code_name_list)
        except Exception as e:
            print(f"load_select_fund_group:{e}")

    def load_default_fund_group(self,code_name_list):
        code_name_list = [i.strip() for i in code_name_list]
        self.ui.jjbj_grouplistWidget.clear()
        for i in code_name_list:
            self.ui.jjbj_grouplistWidget.addItem(str(i))
    def load_group_fund_code(self,group_name):
        try:
            if group_name=="默认":
                with open(self.select_default_path, "r", encoding="utf-8") as f:
                    code_name_list=f.readlines()
            else:
                with open(f"{self.select_other_path}\\{group_name}.txt", "r", encoding="utf-8") as f:
                    code_name_list=f.readlines()
            self.load_default_fund_group(code_name_list)
        except Exception as e:
            print(f"load_group_fund_code:{e}")

    def jjbj_grouplistWidget_select(self,item):
        item=item.text()
        code = item.split("-")[0].strip()
        self.add_fund_code_check(code)
    def add_fund_code_check(self,code):
        try:
            if len(self.code_list)<10 :
                if code not in self.code_list:
                    self.code_list.append(code)
                    self.save_compare_pool()
                    self.load_jjbj_JBXX(code_list=self.code_list, jjbj_graph_seq=self.jjbj_graph_seq)
                else:
                    QMessageBox.information(self.ui.compare_widget, "基金比较池提示", "无法添加重复的基金")
            else:
                QMessageBox.information(self.ui.compare_widget, "基金比较池提示", "最多只能选择10只基金")
        except Exception as e:
            print(f"add_fund_code_check:{e}")

    def on_header_clicked(self, section):
        """当表头被点击时触发"""
        self.update_first_column_data()
        self.load_jjbj_JBXX(code_list=self.code_list, jjbj_graph_seq=self.jjbj_graph_seq)
    def update_first_column_data(self):
        """获取表格第一列数据并保存为列表"""
        data_list = []
        for row in range(self.ui.compare_pool_tableWidget.rowCount()):
            item = self.ui.compare_pool_tableWidget.item(row, 0)  # 第一列的索引是0
            if item is not None:
                data_list.append(item.text())
            else:
                data_list.append("")
        self.code_list=data_list
        self.save_compare_pool()


    def clear_compare_pool(self,event):
        self.code_list=[]
        self.save_compare_pool()
        self.load_JBXX_table(data1=[], )
        self.load_YJPJ_table(data1=[], data2=[], data3=[], data4=[])
        self.load_ZCPZ_table(data1=[], data2=[], data3=[], data4=[])
        self.ui.jjbj_len_pool.setText(f"当前比较池为空")
        with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\modules\FundCompare\src\none.html","r",encoding="utf-8")as f:
            html_content = f.read()
        self.ui.jjbj_webEngineView_1.setHtml(html_content)
        self.ui.jjbj_webEngineView_2.setHtml(html_content)

    def jjbj_listWidget_select(self,item):
        try:
            item=item.text()
            code = item.split("-")[0].strip()
            self.add_fund_code_check(code)
            self.ui.jjbj_listWidget.hide()
            self.ui.jjbj_lineEdit.setText("")
            self.jjbj_listWidget_status =False
        except Exception as e:
            print(f"jjbj_listWidget_select:{e}")

    def custom_mouse_press_jjbj(self,event):
        try:
            super(QLineEdit,self.ui.jjbj_lineEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
            self.show_jjbj_listWidget()
        except Exception as e:
            print(e)

    def show_jjbj_listWidget(self):
        try:
            if not self.jjbj_listWidget_status:
                self.jjbj_listWidget_status = True
            self.ui.jjbj_lineEdit.textChanged.connect(self.get_fund_code_name)
            self.ui.jjbj_listWidget.show()
            self.ui.jjbj_lineEdit.setText("")
            self.get_fund_code_name("")
        except Exception as e:
            print(e)

    def get_fund_code_name(self,text):
        try:
            self.ui.jjbj_listWidget.clear()
            data_list =match_fund_code.return_match_list(input_str=text)
            for i in data_list:
                self.ui.jjbj_listWidget.addItem(i)
        except Exception as e:
            print(e)


    def get_compare_pool(self):
        with open(self.compare_file_path, "r", encoding="utf-8") as f:
            code_list =[x.strip() for x in f.readlines()]
        return code_list

    def save_compare_pool(self):
        with open(self.compare_file_path, "w", encoding="utf-8") as f:
            f.write("\n".join(self.code_list))
        self.code_list=self.get_compare_pool()

    def show_compare_widget(self,event):
        try:
            #数据处理
            start_date = self.jjbj_formatted_dates["近1周"]
            self.show_jjbj_status=True

            self.ui.compare_widget.show()
            self.load_jjbj_JBXX(code_list=self.code_list, jjbj_graph_seq=self.jjbj_graph_seq)
            self.ui.jjbj_select_time.currentTextChanged.connect(self.jjbj_date_combox_select)
            self.ui.jjbj_update_btn.mousePressEvent=self.jjbj_update_graph_date
            self.ui.jjbj_time.setText(f"时间段：从 {start_date} 到 {self.today}")
        except Exception as e:
            print(e)

    def load_jjbj_JBXX(self,code_list,jjbj_graph_seq):
        try:
            self.worker_thread_jjbj=get_compare_data(code_list=code_list,graph_seq=jjbj_graph_seq)
            self.worker_thread_jjbj.finished.connect(self.task_finished_jjbj)
            self.worker_thread_jjbj.start()
        except Exception as e:
            print(e)

    def task_finished_jjbj(self,JBXX_data_list,YJPJ_data_list,ZCPZ_data_list,GRAPH_data_list,):
        try:
            self.JBXX_data=JBXX_data_list
            self.YJPJ_data=YJPJ_data_list
            self.ZCPZ_data=ZCPZ_data_list

            self.jdsy_data=self.YJPJ_data[0]
            self.lsjdsy_data = self.YJPJ_data[1]
            self.dt_data = self.YJPJ_data[2]
            self.jjpj_data = self.YJPJ_data[3]

            self.zcpz_data=self.ZCPZ_data[0]
            self.hypz_data=self.ZCPZ_data[1]
            self.gpcc_data=self.ZCPZ_data[2]
            self.zqcc_data=self.ZCPZ_data[3]

            self.LJSYL_data = GRAPH_data_list[0]
            self.TLPM_data = GRAPH_data_list[1]
            self.load_JBXX_table(data1=self.JBXX_data, )
            self.load_YJPJ_table(data1=self.jdsy_data, data2=self.lsjdsy_data, data3=self.dt_data, data4=self.jjpj_data)
            self.load_ZCPZ_table(data1=self.zcpz_data, data2=self.hypz_data, data3=self.gpcc_data,
                                 data4=self.zqcc_data)
            self.ui.jjbj_len_pool.setText(f"当前比较池（{len(self.JBXX_data)}）")
            self.ui.jjbj_webEngineView_1.setHtml(self.LJSYL_data)
            self.ui.jjbj_webEngineView_2.setHtml( self.TLPM_data)
        except Exception as e:
            print(e)

    def toggle_jjbj_index(self,event):
        if self.show_jjbj_status:
            self.ui.compare_widget.hide()
            self.show_jjbj_status=False
        else:
            self.ui.compare_widget.show()
            self.show_jjbj_status = True

    def jjbj_update_graph_date(self, event):
        #修改日期后，刷新网页图
        self.load_jjbj_JBXX(code_list=self.code_list, jjbj_graph_seq=self.jjbj_graph_seq)

    def jjbj_date_combox_select(self, text):
        match (text):
            case "近1周":
                self.jjbj_graph_seq = 0
            case "近1月":
                self.jjbj_graph_seq = 1
            case "近3月":
                self.jjbj_graph_seq = 2
            case "近6月":
                self.jjbj_graph_seq = 3
            case "近1年":
                self.jjbj_graph_seq = 4
            case "近2年":
                self.jjbj_graph_seq = 5
            case "近3年":
                self.jjbj_graph_seq = 6
        start_date = self.jjbj_formatted_dates[text]
        self.ui.jjbj_time.setText(f"时间段：从 {start_date} 到 {self.today }")

    def load_data_to_JBXX_table(self, data1, ):
        self.ui.compare_pool_tableWidget.setRowCount(len(data1))
        for row_idx, row_data in enumerate(data1):
            for col_idx, cell_data in enumerate(row_data):
                if col_idx==8:
                    item = QTableWidgetItem(" ，".join(str(cell_data).split(" ")))
                else:
                    item = QTableWidgetItem(str(cell_data))
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                if col_idx in [0, 1, ]:
                    item.setForeground(QColor("#294EBC"))
                if col_idx == 6 and cell_data != "--":  # "#009900","#FF0018",
                    if float(str(cell_data).strip("%")) > 0:
                        item.setForeground(QColor("#FF0018"))
                    elif float(str(cell_data).strip("%")) < 0:
                        item.setForeground(QColor("#009900"))
                    else:
                        item.setForeground(QColor("black"))

                self.ui.compare_pool_tableWidget.setItem(row_idx, col_idx, item)
            # 操作列按钮容器
            btn_container = QWidget()
            btn_layout = QHBoxLayout()
            btn_layout.setContentsMargins(0, 0, 0, 0)
            btn_layout.setSpacing(5)

            btn_container_2 = QWidget()
            btn_layout_2 = QHBoxLayout()
            btn_layout_2.setContentsMargins(0, 0, 0, 0)
            btn_layout_2.setSpacing(5)

            add_btn = QPushButton("加自选")
            add_btn.setProperty("row", row_idx)
            # add_btn.clicked.connect(self.move_up)
            btn_layout_2.addWidget(add_btn)

            buy_btn = QPushButton("购买")
            buy_btn.setProperty("row", row_idx)
            # buy_btn.clicked.connect(self.move_up)
            btn_layout_2.addWidget(buy_btn)

            # 上移按钮（第一行禁用）
            up_btn = QPushButton("↑")
            up_btn.clicked.connect(partial(self.move_up, row=row_idx))
            up_btn.setEnabled(row_idx > 0)
            btn_layout.addWidget(up_btn)

            # 下移按钮（最后一行禁用）
            down_btn = QPushButton("↓")
            down_btn.setProperty("row", row_idx)
            down_btn.clicked.connect(partial(self.move_down, row=row_idx))
            down_btn.setEnabled(row_idx < len(data1) - 1)
            btn_layout.addWidget(down_btn)

            # 删除按钮
            del_btn = QPushButton("×")
            del_btn.setProperty("row", row_idx)
            del_btn.clicked.connect(partial(self.delete_row, row=row_idx))
            del_btn.setStyleSheet("color: red;")  # 红色删除按钮
            btn_layout.addWidget(del_btn)

            btn_container.setLayout(btn_layout)
            btn_container_2.setLayout(btn_layout_2)
            self.ui.compare_pool_tableWidget.setCellWidget(row_idx, len(row_data), btn_container, )
            self.ui.compare_pool_tableWidget.setCellWidget(row_idx, len(row_data) + 1, btn_container_2, )

    def control_row(self,row,status):
        match status:
            case "up":
                self.code_list[row],  self.code_list[row - 1] = self.code_list[row - 1], self.code_list[row]
            case "down":
                self.code_list[row],  self.code_list[row + 1] = self.code_list[row +1], self.code_list[row]
            case "delete":
                self.code_list.pop(row)
        self.load_jjbj_JBXX(code_list=self.code_list, jjbj_graph_seq=self.jjbj_graph_seq)


    def move_up(self,row):
        """上移当前行"""
        try:
            if row > 0:
                self.control_row(row,"up")
        except Exception as e:
            print(e)

    def move_down(self,row):
        """下移当前行"""
        if row < len(self.code_list) - 1:
            self.control_row(row,"down")
    def delete_row(self,row):
        """删除当前行（带确认对话框）"""
        self.control_row(row,"delete")
        self.ui.jjbj_len_pool.setText(f"当前比较池（{len(self.code_list)}）")


    def jjbj_table_mb(self, table_name, headers, data, color_start, color_end, color_status, ):
        try:
            getattr(self.ui, f"{table_name}").setColumnCount(len(headers))
            getattr(self.ui, f"{table_name}").setHorizontalHeaderLabels(headers)
            getattr(self.ui, f"{table_name}").horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            getattr(self.ui, f"{table_name}").horizontalHeader().setStyleSheet(style.QTableWidget_JBXX_style)
            getattr(self.ui, f"{table_name}").setRowCount(len(data))

            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    match (color_status):
                        case "1":
                            if col_idx in [0, 1, ]:
                                item.setForeground(QColor("#294EBC"))
                            if col_idx in [i for i in range(color_start, color_end + 1)]:  # "#009900","#FF0018",
                                if str(cell_data) != "--":
                                    if float(str(cell_data).strip("%")) > 0:
                                        item.setForeground(QColor("#FF0018"))
                                    elif float(str(cell_data).strip("%")) < 0:
                                        item.setForeground(QColor("#009900"))
                                    else:
                                        item.setForeground(QColor("black"))
                        case "2":
                            if col_idx in [0, 1, ]:
                                item.setForeground(QColor("#294EBC"))
                            if col_idx in [i for i in range(color_start, color_end + 1)]:  # "#009900","#FF0018",
                                if str(cell_data) == "暂无评级":
                                    item.setForeground(QColor("gray"))
                        case "3":
                            if col_idx in [i for i in range(color_start, color_end + 1)]:
                                if cell_data != "--":
                                    item.setForeground(QColor("#294EBC"))
                                    # item.setFlags(item.flags() | Qt.ItemFlag.ItemIsEnabled)  # 确保单元格可
                    getattr(self.ui, f"{table_name}").setItem(row_idx, col_idx, item)
        except Exception as e:
            print(f"jjbj_table_mb：{e}")

    def load_JBXX_table(self, data1):
        try:
            headers = ["基金代码", "基金名称", "类型", "日期", "最新单位净值", "累计净值", "日增长率", "上期单位净值",
                       "基金经理", "基金公司", "比较池操作", "其他"]
            self.ui.compare_pool_tableWidget.setColumnCount(len(headers))
            self.ui.compare_pool_tableWidget.setHorizontalHeaderLabels(headers)
            self.ui.compare_pool_tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            self.ui.compare_pool_tableWidget.horizontalHeader().setStyleSheet(style.QHeaderView_style)
            self.load_data_to_JBXX_table(data1)
        except Exception as e:
            print(e)

    def load_YJPJ_table(self, data1, data2, data3, data4):
        try:
            headers1 = ["基金代码", "基金名称", "成立日期", "今年来", "近1周", "近1月", "近3月", "近6月", "近1年",
                        "近2年", "近3年", "近5年", "成立来"]
            headers2 = ["基金代码", "基金名称", "2024年", "2023年", "2022年", "2021年", "2020年"]
            headers3 = ["基金代码", "基金名称", "近1年", "近2年", "近3年", "近5年", ]
            headers4 = ["基金代码", "基金名称", "海通证券", "招商证券", "上海证券", "济安金信", "晨星评级"]

            self.jjbj_table_mb(table_name="jdsy_tableWidget", headers=headers1, data=data1, color_start=3, color_end=12,
                               color_status="1")
            self.jjbj_table_mb(table_name="lsndsy_tableWidget", headers=headers2, data=data2, color_start=2,
                               color_end=6, color_status="1")
            self.jjbj_table_mb(table_name="dtsy_tableWidget", headers=headers3, data=data3, color_start=2, color_end=5,
                               color_status="1")
            self.jjbj_table_mb(table_name="jjpj_tableWidget", headers=headers4, data=data4, color_start=2, color_end=6,
                               color_status="2")
        except Exception as e:
            print(e)

    def load_ZCPZ_table(self, data1, data2, data3, data4):
        try:
            headers1 = ["基金代码", "基金名称", "份额规模 (亿份)", "股票占净比", "债券占净比", "现金占净比",
                        "前10持股集中度"]
            headers2 = ["基金代码", "基金名称", "制造业", "金融业", "房地产业", "信息技术业", "农林牧渔业", "采掘业",
                        "批发零售业", "交通运输业", "建筑业", "社会服务业"]
            headers3 = ["基金代码", "基金名称", "第1持仓", "第2持仓", "第3持仓", "第4持仓", "第5持仓", "第6持仓",
                        "第7持仓", "第8持仓", "第9持仓", "第10持仓", ]
            headers4 = ["基金代码", "基金名称", "第1持仓", "第2持仓", "第3持仓", "第4持仓", "第5持仓"]

            self.jjbj_table_mb(table_name="zcpz_tableWidget", headers=headers1, data=data1, color_start=3, color_end=12,
                               color_status="2")
            self.jjbj_table_mb(table_name="hypz_tableWidget", headers=headers2, data=data2, color_start=2, color_end=6,
                               color_status="2")
            self.jjbj_table_mb(table_name="gpcc_tableWidget", headers=headers3, data=data3, color_start=2, color_end=11,
                               color_status="3")
            self.jjbj_table_mb(table_name="zqcc_tableWidget", headers=headers4, data=data4, color_start=2, color_end=6,
                               color_status="2")
        except Exception as e:
            print(e)



