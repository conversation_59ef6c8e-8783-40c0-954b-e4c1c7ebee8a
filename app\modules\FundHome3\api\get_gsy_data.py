from PyQt6.QtCore import QThread, pyqtSignal, QFile, QIODevice
import requests
import copy
import json


class get_gsy_data(QThread):
    finished = pyqtSignal(list, list,list)

    def __init__(self,):
        super().__init__()
        self.header ={
  "Host": "fund.eastmoney.com",
  "Connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-origin",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://fund.eastmoney.com/trade/hh.html",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; nid_id=533447622; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; st_si=32259024499477; st_asi=delete; _adsame_fullscreen_20308=1; ASP.NET_SessionId=4kqyltpuqz5b1gm03pbzinwk; FundWebTradeUserInfo=JTdCJTIyQ3VzdG9tZXJObyUyMjolMjIlMjIsJTIyQ3VzdG9tZXJOYW1lJTIyOiUyMiUyMiwlMjJWaXBMZXZlbCUyMjolMjIlMjIsJTIyTFRva2VuJTIyOiUyMiUyMiwlMjJJc1Zpc2l0b3IlMjI6JTIyJTIyLCUyMlJpc2slMjI6JTIyJTIyLCUyMlN1cnZleURheSUyMjowLCUyMklzQXVkaXROZWVkUG9wJTIyOnRydWUlN0Q%3D; _adsame_fullscreen_18503=1; EMFUND0=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND1=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND2=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND3=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND4=08-04%2012%3A35%3A13@%23%24%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; EMFUND5=08-07%2023%3A04%3A55@%23%24%u4E2D%u4FE1%u5EFA%u6295%u5317%u4EA4%u6240%u7CBE%u9009%u4E24%u5E74%u5B9A%u5F00%u6DF7%u5408A@%23%24016303; EMFUND6=08-07%2023%3A10%3A30@%23%24%u65B0%u534E%u5229%u7387%u503A%u503A%u5238E@%23%24016295; EMFUND7=08-08%2011%3A27%3A07@%23%24%u5FB7%u90A6%u946B%u661F%u4EF7%u503C%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408A@%23%24001412; EMFUND8=08-08%2011%3A31%3A38@%23%24%u534E%u590F%u5317%u4EA4%u6240%u7CBE%u9009%u4E24%u5E74%u5B9A%u5F00%u6DF7%u5408%u53D1%u8D77%u5F0F@%23%24014283; EMFUND9=08-08 11:34:38@#$%u6C47%u6DFB%u5BCC%u5065%u5EB7%u751F%u6D3B%u4E00%u5E74%u6301%u6709%u6DF7%u5408A@%23%24011826; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=9; st_psi=2025080811345424-112200305283-0362542170; _qimei_i_1=73e266f4921d"
}

        self.url=f"https://fund.eastmoney.com/data/fundtradenewapi.aspx?ft=hh&sc=1n&st=desc&pi=1&pn=100&cp=&ct=&cd=&ms=&fr=&plevel=&fst=&ftype=&fr1=&fl=0&isab=1"

    def request_data(self):
        try:
            response = requests.get(self.url, headers=self.header, verify=False)
            response.encoding = 'utf-8'
            
            # 提取方括号内的数据
            response_text = response.text
            start = response_text.find('[')
            end = response_text.rfind(']')
            
            if start == -1 or end == -1:
                print("未找到数据数组")
                return [], [], []
                
            # 提取数组字符串并转换为Python列表
            array_str = response_text[start:end+1]
            data_list = eval(array_str)  # 或使用 ast.literal_eval(array_str)
            
            gsy_fund_code = []
            gsy_fund_name = []  
            gsy_fund_value = []
            
            processed_names = set()  # 用于去重，避免A型和C型重复
            
            for item in data_list:
                parts = item.split("|")
                
                # 检查索引-1是否为1（可购买）
                if parts[-7] != "1":
                    continue
                
                fund_code = parts[0]
                fund_name = parts[1]
                fund_value = parts[10]  # 索引10对应涨幅
                
                # 去除A型和C型后缀进行去重
                base_name = fund_name
                if base_name.endswith('A') or base_name.endswith('C'):
                    base_name = fund_name[:-1]
                
                # 如果已经处理过相同的基金名称，跳过
                if base_name in processed_names:
                    continue
                
                processed_names.add(base_name)
                gsy_fund_code.append(fund_code)
                gsy_fund_name.append(fund_name)
                gsy_fund_value.append(fund_value)
                
                # 只需要3个
                if len(gsy_fund_code) >= 3:
                    break
            
            return gsy_fund_code, gsy_fund_name, gsy_fund_value
        except Exception as e:
            print(f"请求数据失败: {e}")
            return [], [], []



    def run(self):
        try:
            gsy_fund_code, gsy_fund_name, gsy_fund_value= self.request_data()
            self.finished.emit(gsy_fund_code, gsy_fund_name, gsy_fund_value)
        except Exception as e:
            print(f"Error in run method: {e}")





