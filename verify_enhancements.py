#!/usr/bin/env python3
"""
验证增强功能的脚本
"""

try:
    print("🔍 验证增强的Markdown聊天功能...")
    
    # 1. 验证基本导入
    from app.modules.FundAiChat.FundAiChat import MarkdownWebView
    print("✅ MarkdownWebView导入成功")
    
    # 2. 验证PyQt6组件
    from PyQt6.QtWidgets import QSizePolicy
    from PyQt6.QtCore import QTimer
    from PyQt6.QtWebEngineCore import QWebEngineSettings
    print("✅ PyQt6增强组件导入成功")
    
    # 3. 验证markdown扩展
    import markdown
    test_md = """
# 测试标题

这是**粗体**和*斜体*文本。

| 列1 | 列2 |
|-----|-----|
| A   | B   |

```python
print("代码块测试")
```

> 引用块测试
"""
    
    html_result = markdown.markdown(test_md, extensions=['tables', 'fenced_code', 'nl2br'])
    print(f"✅ Markdown转换成功: {len(html_result)} 字符")
    
    # 4. 验证数学公式支持
    try:
        import mdx_math
        print("✅ 数学公式扩展可用")
    except ImportError:
        print("⚠️  数学公式扩展不可用，但MathJax仍可工作")
    
    print("\n📋 功能增强总结:")
    print("1. ✅ 自适应高度 - WebView根据内容自动调整高度")
    print("2. ✅ 圆角效果 - 使用容器widget实现圆角边框")
    print("3. ✅ 无滚动条 - 禁用WebView滚动条")
    print("4. ✅ 数学公式 - 集成MathJax支持LaTeX公式")
    print("5. ✅ 防止压缩 - 每个回答独立高度，不会被压缩")
    
    print("\n🎯 主要改进:")
    print("- setSizePolicy(Expanding, Minimum) - 自适应高度策略")
    print("- setFixedHeight() - 根据内容动态设置高度")
    print("- 圆角容器 - 外层widget实现圆角效果")
    print("- MathJax集成 - 支持行内和块级数学公式")
    print("- JavaScript高度检测 - 实时获取内容高度")
    
    print("\n🧪 测试建议:")
    print("1. 运行 test_enhanced_markdown.py 查看完整效果")
    print("2. 测试不同长度的内容，验证高度自适应")
    print("3. 测试数学公式: $E=mc^2$ 和 $$\\sum_{i=1}^n i$$")
    print("4. 测试流式输出，确保实时更新正常")
    
    print("\n🎉 所有增强功能验证通过！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
except Exception as e:
    print(f"❌ 其他错误: {e}")
