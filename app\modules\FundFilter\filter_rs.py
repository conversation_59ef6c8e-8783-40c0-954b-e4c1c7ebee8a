from functools import partial
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton
from app.modules.FundFilter.DataHandle_filter import DataHandle_filter
from app.modules.FundFilter.src import style


class filter_rs:
    def __init__(self, ui):
        self.ui = ui
        self.init_filter_rs()

    def init_filter_rs(self):
        # 初始化数据结构，每个标签对应一个字典
        self.filter_rs_dict = [{"lb": 0} for _ in range(8)]  # 8个标签
        self.select_temp = None
        self.filter_rs_lb_list = []
        self.selcet_lb_index = None  # 当前选中的标签索引

        # 初始化标签
        for i in range(1, 9):
            label = getattr(self.ui, f"filter_rs_{i}")
            label.setStyleSheet(style.QLabel_ft_normal)
            label.mousePressEvent = partial(self.label_clicked, index=i)

        # 初始化下拉框和"不限"标签
        self.ui.filter_rs_select.currentTextChanged.connect(self.rs_select_change)
        self.ui.filter_rs_none.setStyleSheet(style.QLabel_ft_selected)
        self.ui.filter_rs_none.mousePressEvent = self.clear_all_selections

    def label_clicked(self, event, index):
        try:
            # 更新当前选中的标签索引
            self.selcet_lb_index = index - 1  # 转换为0-based索引

            # 更新UI显示
            self.update_label_styles(index)

            # 设置下拉框的当前值
            current_value = self.filter_rs_dict[self.selcet_lb_index]["lb"]
            self.ui.filter_rs_select.setCurrentIndex(current_value)

            # 更新标题文本
            self.select_lb = getattr(self.ui, f"filter_rs_{index}").text()
            self.ui.filter_rs_lb.setText(f"{self.select_lb}排名限制：")

        except Exception as e:
            print(f"label_clicked error: {e}")

    def update_label_styles(self, current_index=None):
        """更新所有标签的样式"""
        for i in range(1, 9):
            label = getattr(self.ui, f"filter_rs_{i}")
            # 当前选中的标签或已设置排名的标签高亮
            # if (i == current_index) or (self.filter_rs_dict[i - 1]["lb"] != 0):
            if self.filter_rs_dict[i - 1]["lb"] != 0:
                label.setStyleSheet(style.QLabel_ft_selected)
                if i not in self.filter_rs_lb_list:
                    self.filter_rs_lb_list.append(i)
            else:
                label.setStyleSheet(style.QLabel_ft_normal)
                if i in self.filter_rs_lb_list:
                    self.filter_rs_lb_list.remove(i)
        self.update_rs_none_status()

    def rs_select_change(self, text):
        try:
            if self.selcet_lb_index is None:
                return
            # 转换文本为数值
            rank_mapping = ["不限", "前10名", "前20名", "前50名", "前100名"]
            self.select_temp = rank_mapping.index(text)

            # 更新当前标签的数据
            self.filter_rs_dict[self.selcet_lb_index]["lb"] = self.select_temp

            # 更新UI
            self.update_label_styles(self.selcet_lb_index + 1)

            print("当前筛选设置:", self.filter_rs_dict)
            print("高亮标签列表:", self.filter_rs_lb_list)
            print("当前筛选设置:", self.trans_data())
            data=self.trans_data()
            result=DataHandle_filter.return_all_filter_data(self.ui.filter_all_lb.toPlainText(), data, "业绩")
            self.ui.filter_all_lb.setPlainText(result)
        except Exception as e:
            print(f"rs_select_change error: {e}")

    def clear_all_selections(self, event=None):
        """清空所有选择"""
        for i in range(len(self.filter_rs_dict)):
            self.filter_rs_dict[i]["lb"] = 0

        self.filter_rs_lb_list = []
        self.update_label_styles()
        self.ui.filter_rs_select.setCurrentIndex(0)
        self.ui.filter_rs_none.setStyleSheet(style.QLabel_ft_selected)
        self.ui.filter_rs_lb.setText("不设排名限制")
        data = self.trans_data()
        result = DataHandle_filter.return_all_filter_data(self.ui.filter_all_lb.toPlainText(), data, "业绩")
        self.ui.filter_all_lb.setPlainText(result)

    def update_rs_none_status(self):
        """更新'不限'标签的状态"""
        if all(item["lb"] == 0 for item in self.filter_rs_dict):
            self.ui.filter_rs_none.setStyleSheet(style.QLabel_ft_selected)
        else:
            self.ui.filter_rs_none.setStyleSheet(style.QLabel_ft_normal)

    def return_rs_list(self):
        lb_index_list=sorted([i-1 for i in self.filter_rs_lb_list])
        sel_index_list=[]
        if len(lb_index_list)!=len(self.filter_rs_lb_list):
            return [],[]
        for i in self.filter_rs_dict:
            if  i["lb"]!=0:
                sel_index_list.append(i["lb"])
        return lb_index_list,sel_index_list

    def trans_data(self,):
        lb_index_list,sel_index_list =self.return_rs_list()
        yj_type=["近1周","近1月","近3月","近6月","今年以来","近1年","近2年","近3年"]
        rank_mapping = ["不限", "前10名", "前20名", "前50名", "前100名"]
        result=[[yj_type[i] for i in lb_index_list],[rank_mapping[i] for i in sel_index_list]]
        return result
