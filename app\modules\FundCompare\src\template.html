<!DOCTYPE html>
<html>
<head>
    <title>百分比数据图表</title>
    <script src="http://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="http://cdn.jsdelivr.net/npm/luxon@3.0.1"></script>
    <script src="http://cdn.jsdelivr.net/npm/chartjs-adapter-luxon@1.2.0"></script>
    <style>
        body {
            margin: 0;
            padding: 5px;
            font-family: 'Arial', sans-serif;
        }
        #chart-container {
            width: 490px;
            height:360px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
    </style>
</head>
<body>
    <div id="chart-container">
        <canvas id="line-chart"></canvas>
        <p style="float: left;margin-top: 1px;font-size: 13px" >start_date</p>
    <p style="float: right;margin-top: 1px;font-size: 13px">end_date</p>
    </div>


    <script>
        /*CONFIG_PLACEHOLDER*/
        /*DATA_PLACEHOLDER*/

        let chart;

        function formatPercentage(value) {
            return value + chartConfig.unit;
        }

        function initChart() {
            const ctx = document.getElementById('line-chart').getContext('2d');

            // 准备数据集（实心图标）
            const datasets = lineData.map((line, index) => ({
                label: chartConfig.legends[index],
                data: line.points,
                borderColor: line.color,
                backgroundColor: line.color, // 实心填充
                borderWidth: 1,
                pointRadius: 0,          // 完全隐藏数据点
                pointHoverRadius: 0,
                pointBackgroundColor: line.color,
                fill: false,
                tension: 0.1
            }));

            chart = new Chart(ctx, {
                type: 'line',
                data: {
                    labels: chartConfig.x_labels,
                    datasets: datasets
                },
                options: getChartOptions()
            });
        }

        function getChartOptions() {
            return {
                responsive: true,
                maintainAspectRatio: false,
                scales: {
                    y: {
                        min: chartConfig.y_min,
                        max: chartConfig.y_max,
                        ticks: {
                            callback: function(value) {
                                return formatPercentage(value);
                            }
                        }
                    },
                    x: {
                        display: chartConfig.show_x_labels,
                        grid: {
                            display: false
                        },
                        ticks: {
                            maxRotation: 45,
                            minRotation: 45
                        }
                    }
                },
                plugins: {
                    legend: {
                        position: 'top',
                        labels: {
                            usePointStyle: true, // 使用点样式
                            pointStyle: 'circle', // 实心圆
                            boxWidth: 12,
                            padding: 20,
                            font: {
                                size: 12
                            }
                        }
                    },
                    tooltip: {
                        callbacks: {
                            label: function(context) {
                                return context.dataset.label + ': ' + formatPercentage(context.raw);
                            }
                        },
                        displayColors: true,
                        usePointStyle: true
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            };
        }

        function updateChart(newData, newConfig) {
            // 更新数据
            chart.data.datasets.forEach((dataset, index) => {
                dataset.data = newData[index].points;
            });

            // 更新配置
            chart.options.scales.y.min = newConfig.y_min;
            chart.options.scales.y.max = newConfig.y_max;
            chart.options.scales.x.display = newConfig.show_x_labels;

            chart.update();
        }

        function toggleXLabels(show) {
            chart.options.scales.x.display = show;
            chart.update();
        }

        document.addEventListener('DOMContentLoaded', initChart);
    </script>
</body>
</html>