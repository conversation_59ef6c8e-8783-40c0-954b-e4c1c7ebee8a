import time
from functools import partial

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton, QHeaderView, QTableWidgetItem, QMessageBox

from app.modules.FundFilter.DataHandle_filter import DataHandle_filter
from app.modules.FundFilter.api.get_filter_fund_data import get_filter_fund_data
from app.modules.FundFilter.filter_cp import filter_cp
from app.modules.FundFilter.filter_more import filter_more
from app.modules.FundFilter.filter_rs import filter_rs
# from app.modules.FundFilter.filter_cp import Filter_cp
from app.modules.FundFilter.src import style


class FundFilter:
    def __init__(self, ui):
        self.ui = ui
        self.init_setup()

    def init_setup(self):
        self.ft_filter_name = []
        self.total_filter_dict=[
            [],#基金类型
            [],#基金公司
            [{"近一周":0},{"近一月": 0},{"近三月": 0},{"近六月": 0},{"今年以来": 0},{"近1年": 0},{"近2年": 0},{"近3年": 0}],#基金业绩
            [],#基金板块
            [{"晨星评级":0},{"上证评级":0},{"招商评级":0},{"济安评级":0},],#机构评级
            [],#基金规模
            [],#成立年限
        ]
        self.filter_show_status = True

        self.jj_filter_status = False
        self.ui.jj_filter_widget.hide()
        self.ui.jj_filter_lb.mousePressEvent = self.show_jj_filter_widget
        self.ui.jj_filter_return_lb.mousePressEvent = self.toggle_widget_index
        self.ui.filter_query_btn.mousePressEvent = self.query_data
        self.ui.filter_clear_lb.mousePressEvent = self.clear_all_filter
        self.ui.filter_close.mousePressEvent = self.toggle_filter_widget
        self.ui.filter_page_goto.mousePressEvent = self.page_goto
        # self.current_page=1

        #基金类型选择
        self.init_filter_ft()
        # self.init_filter_cp()
        self.init_cp=filter_cp(self.ui)
        self.init_rs=filter_rs(self.ui)
        self.init_more=filter_more(self.ui)
        self.ui.filter_query_btn.setEnabled(False)
        self.is_loading=False


    def toggle_filter_widget(self,event):
        if self.filter_show_status:
            self.ui.all_filter_widget.hide()
            self.init_more.hide_filter_more(event)
            self.filter_show_status=False
            self.ui.filter_close.setText("展开")
            self.modify_widget_position("top")
        else:
            self.ui.all_filter_widget.show()
            self.filter_show_status=True
            self.ui.filter_close.setText("收起")
            self.modify_widget_position("bottom")

    def modify_widget_position(self,status):
        if status=="top":
            # 改变总widget的位置和大小
            # self.ui.filter_all_table_widget.move(0,70)
            self.ui.filter_all_table_widget.setGeometry(0, 84, 1711, 931)
            # 改变tabel widget的尺寸
            self.ui.filter_tableWidget.setGeometry(0, 45, 1711, 886)
        else:
            #改变总widget的位置和大小
            # self.ui.filter_all_table_widget.move(0,70)
            self.ui.filter_all_table_widget.setGeometry(0, 275, 1711, 741)
            #改变tabel widget的尺寸
            self.ui.filter_tableWidget.setGeometry(0, 45, 1711, 691)




    def clear_all_filter(self,event):
        self.clear_filter_ft(event)
        self.init_cp.clear_filter_cp(event)
        self.init_rs.clear_all_selections(event)
        self.init_more.clear_all_selections()
        self.ui.filter_all_lb.setPlainText("")


    def init_filter_ft(self):
        """初始化标签过滤器"""
        self.filter_ft_list = []
        for i in range(2, 8):
            label = getattr(self.ui, f"filter_ft_{i}")
            label.setStyleSheet(style.QLabel_ft_normal)
            label.mousePressEvent = partial(self.filter_ft_list_change, index=i)
        # 特殊处理1号标签
        self.ui.filter_ft_1.mousePressEvent = self.clear_filter_ft
        self.ui.filter_ft_1.setStyleSheet(style.QLabel_ft_selected)  # 默认选中第一个

    def clear_filter_ft(self, event):
        self.filter_ft_list.clear()
        for i in range(2, 8):
            getattr(self.ui, f"filter_ft_{i}").setStyleSheet(style.QLabel_ft_normal)
        self.ui.filter_ft_1.setStyleSheet(style.QLabel_ft_selected)
        self.total_filter_dict[0]=[]
        # self.update_all_filter()
        l = ["股票型", "混合型", "债券型", "指数型", "QDII", "FOF"]
        result = DataHandle_filter.return_all_filter_data(self.ui.filter_all_lb.toPlainText(),
                                                          [l[i - 2] for i in self.filter_ft_list], "类型")
        self.ui.filter_all_lb.setPlainText(result)


    def ft_filter_change(self, index):
        try:
            current_label = getattr(self.ui, f"filter_ft_{index}")
            # 情况1：当前是最后一个选中标签且再次点击它
            if len(self.filter_ft_list) == 1 and index in self.filter_ft_list:
                self.clear_filter_ft(None)  # 直接调用清空方法
                return
            if current_label.styleSheet() == style.QLabel_ft_normal:
                current_label.setStyleSheet(style.QLabel_ft_selected)
                self.filter_ft_list.append(index)
            else:
                current_label.setStyleSheet(style.QLabel_ft_normal)
                self.filter_ft_list.remove(index)
            l = ["股票型", "混合型", "债券型", "指数型", "QDII", "FOF"]
            # self.total_filter_dict[0] = [l[i - 2] for i in self.filter_ft_list]
            print([l[i - 2] for i in self.filter_ft_list])
            result = DataHandle_filter.return_all_filter_data(self.ui.filter_all_lb.toPlainText(), [l[i - 2] for i in self.filter_ft_list], "类型")
            self.ui.filter_all_lb.setPlainText(result)
            self.update_ft1_status()
        except Exception as e:
            print(f"ft_filter_change error:{e}")
        # self.update_all_filter()

    def update_ft1_status(self):
        if not self.filter_ft_list:  # 如果列表为空
            self.ui.filter_ft_1.setStyleSheet(style.QLabel_ft_selected)
        else:
            self.ui.filter_ft_1.setStyleSheet(style.QLabel_ft_normal)



    def filter_ft_list_change(self, event, index):
        self.ft_filter_change(index)

        print("当前选中列表:", self.filter_ft_list)


    def show_jj_filter_widget(self,event):
        self.jj_filter_status=True
        self.ui.jj_filter_widget.show()
        self.query_data(event=None)

    def toggle_widget_index(self,event):
        if self.jj_filter_status:
            self.jj_filter_status = False
            self.ui.jj_filter_widget.hide()
        else:
            self.jj_filter_status = True
            self.ui.jj_filter_widget.show()

    def get_query_params(self):
        # 获取对应的数据
        ft_data = [i - 2 for i in self.filter_ft_list]
        cp_data = self.init_cp.return_cp_list()
        rs_data = self.init_rs.return_rs_list()
        tp_data = self.init_more.return_more_total_data()[0]
        rt_data = self.init_more.return_more_total_data()[1]
        se_data = self.init_more.return_more_total_data()[2]
        nx_data = self.init_more.return_more_total_data()[3]
        print(DataHandle_filter.trans_url_data(ft_data, cp_data, rs_data, tp_data, rt_data, se_data, nx_data))
        return DataHandle_filter.trans_url_data(ft_data, cp_data, rs_data, tp_data, rt_data, se_data, nx_data)
    def query_data(self,event):
        try:
            self.current_page=1
            ft_data, cp_data, rs_data, tp_data, rt_data, se_data, nx_data= self.get_query_params()
            self.load_fund_filter(ft_data,cp_data,rs_data,tp_data,rt_data,se_data,nx_data,self.current_page)
        except Exception as e:
            print(f"query_data error:{e}")

    def page_query_data(self):
        if self.is_loading:
            return  # 如果正在加载，直接返回
        self.is_loading = True  # 锁定，防止重复请求
        try:
            ft_data, cp_data, rs_data, tp_data, rt_data, se_data, nx_data = self.get_query_params()
            self.load_fund_filter(ft_data, cp_data, rs_data, tp_data, rt_data, se_data, nx_data, self.current_page)
        except Exception as e:
            print(f"page_query_data error:{e}")
            self.is_loading = False  # 出错时也要解锁

    #填充表格数据
    def load_fund_filter(self,ft_data,cp_data,rs_data,tp_data,rt_data,se_data,nx_data,page):
        try:
            self.start_time=time.time()
            self.ui.filter_time_lb.setText(f"正在加载数据中...")
            self.ui.filter_count_lb.setText(f"正在查找符合要求的基金")
            self.ui.filter_query_btn.setEnabled(False)
            self.worker_thread=get_filter_fund_data(ft_data,cp_data,rs_data,tp_data,rt_data,se_data,nx_data,page)
            self.worker_thread.finished.connect(self.task_finished_fund_filter)
            self.worker_thread.start()
        except Exception as e:
            print(f"load_table_data error:{e}")

    def task_finished_fund_filter(self,table_data,other_data):
        self.filter_results=table_data
        self.load_data_to_table(self.filter_results)
        all_pages=int(other_data[0])
        data_len=other_data[1]
        self.end_time=time.time()
        self.init_page_status(all_pages)
        self.ui.filter_time_lb.setText(f"本次筛选耗时：{(self.end_time-self.start_time):.2f}s")
        self.ui.filter_count_lb.setText(f"共找到 {data_len} 只基金符合您的要求：")
        self.ui.filter_query_btn.setEnabled(True)
        self.is_loading = False


    def load_data_to_table(self, data):
        headers = ["基金代码", "基金名称", "基金简拼","基金类型", "日期", "单位净值", "日增长率","近1周","近1月","近3月","近6月","今年以来","近1年","近2年","近3年",
                   "成立来"]
        self.ui.filter_tableWidget.setColumnCount(len(headers))
        self.ui.filter_tableWidget.setHorizontalHeaderLabels(headers)
        self.ui.filter_tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
        self.ui.filter_tableWidget.horizontalHeader().setStyleSheet(style.QHeaderView_style)
        self.ui.filter_tableWidget.setRowCount(len(data))
        for row_idx, row_data in enumerate(data):
            for col_idx, cell_data in enumerate(row_data):
                item = QTableWidgetItem(str(cell_data))
                item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                if col_idx in [0, 1, ]:
                    item.setForeground(QColor("#294EBC"))
                if col_idx in range(6, 16) and cell_data != "--":  # "#009900","#FF0018",
                    if float(str(cell_data).strip("%")) > 0:
                        item.setForeground(QColor("#FF0018"))
                    elif float(str(cell_data).strip("%")) < 0:
                        item.setForeground(QColor("#009900"))
                    else:
                        item.setForeground(QColor("black"))
                self.ui.filter_tableWidget.setItem(row_idx, col_idx, item)
                # 设置行高
        row_height = 42  # 设置行高为 30 像素
        for row in range(self.ui.filter_tableWidget.rowCount()):
            self.ui.filter_tableWidget.setRowHeight(row, row_height)
        start = (self.current_page - 1) * 20 + 1
        end = start +len(data)
        self.ui.filter_tableWidget.setVerticalHeaderLabels([str(i) for i in range(start, end)])

    def init_page_status(self,all_pages):
        self.total_pages = all_pages
        if all_pages==0:
            self.ui.filter_total_pages.setText(f"/{all_pages}")
            self.ui.filter_current_page.setText(f"{self.current_page}")
            self.ui.filter_pre_page.setEnabled(False)
            self.ui.filter_after_page.setEnabled(False)
            self.ui.filter_pre_page.setStyleSheet(style.QPushButton_page_no)
            self.ui.filter_after_page.setStyleSheet(style.QPushButton_page_no)
        else:
            self.ui.filter_total_pages.setText(f"/{all_pages}")
            self.ui.filter_current_page.setText(f"{self.current_page}")
            self.ui.filter_pre_page.setEnabled(self.current_page > 1)  # 初始状态：第 1 页时禁用
            self.ui.filter_pre_page.clicked.connect(self.go_to_previous_page)
            self.ui.filter_after_page.setEnabled(self.current_page <all_pages)  # 初始状态：最后一页时禁用
            self.ui.filter_after_page.clicked.connect(self.go_to_next_page)

    def go_to_previous_page(self):
        if self.current_page > 1 and not self.is_loading:
            self.current_page -= 1
            self.update_ui()
            self.page_query_data()  # 触发数据请求

    def go_to_next_page(self):
        if self.current_page < self.total_pages and not self.is_loading:
            self.current_page += 1
            self.update_ui()
            self.page_query_data()  # 触发数据请求

    def update_ui(self):
        self.ui.filter_total_pages.setText(f"/{self.total_pages}")
        self.ui.filter_current_page.setText(f"{self.current_page}")

        # 更新按钮状态
        self.ui.filter_pre_page.setEnabled(self.current_page > 1)
        self.ui.filter_pre_page.setStyleSheet(
            style.QPushButton_page_yes if self.current_page > 1 else style.QPushButton_page_no)

        self.ui.filter_after_page.setEnabled(self.current_page < self.total_pages)
        self.ui.filter_after_page.setStyleSheet(
            style.QPushButton_page_yes if self.current_page < self.total_pages else style.QPushButton_page_no)

    def page_goto(self,event):
        page_=self.ui.filter_page_lineEdit.text()
        if page_:
            page_=int(page_)
            if page_>0 and page_<=self.total_pages and not self.is_loading:
                self.current_page=page_
                self.update_ui()
                self.page_query_data()
                self.ui.filter_page_lineEdit.clear()
            else:
                QMessageBox.information(self.ui.jj_filter_widget, "提示", "请输入正确的页码")
        else:
            QMessageBox.information(self.ui.jj_filter_widget, "提示", "请输入正确的页码")












