#!/usr/bin/env python3
"""
测试Markdown聊天功能的简单脚本
"""

import sys
from PyQt6.QtWidgets import QApplication, QMainWindow, QVBoxLayout, QWidget, QPushButton, QTextEdit
from PyQt6.QtCore import Qt

# 导入我们的MarkdownWebView组件
from app.modules.FundAiChat.FundAiChat import MarkdownWebView


class TestMarkdownChat(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Markdown聊天测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建布局
        layout = QVBoxLayout(central_widget)
        
        # 创建Markdown渲染器
        self.markdown_view = MarkdownWebView()
        layout.addWidget(self.markdown_view)
        
        # 创建输入框
        self.input_text = QTextEdit()
        self.input_text.setMaximumHeight(100)
        self.input_text.setPlaceholderText("输入Markdown文本进行测试...")
        layout.addWidget(self.input_text)
        
        # 创建测试按钮
        test_button = QPushButton("测试Markdown渲染")
        test_button.clicked.connect(self.test_markdown)
        layout.addWidget(test_button)
        
        # 创建流式测试按钮
        stream_button = QPushButton("测试流式输出")
        stream_button.clicked.connect(self.test_stream)
        layout.addWidget(stream_button)
        
        # 设置初始测试内容
        self.set_initial_content()
    
    def set_initial_content(self):
        """设置初始测试内容"""
        test_markdown = """# AI助手回答示例

这是一个**Markdown渲染**的测试。

## 功能特性

1. **粗体文本**
2. *斜体文本*
3. `代码片段`

### 代码块示例

```python
def hello_world():
    print("Hello, World!")
    return "AI聊天功能已启用"
```

### 表格示例

| 功能 | 状态 | 描述 |
|------|------|------|
| Markdown渲染 | ✅ | 支持基本Markdown语法 |
| 流式输出 | ✅ | 支持实时更新内容 |
| 代码高亮 | ✅ | 支持代码块语法高亮 |

> 这是一个引用块，用于显示重要信息。

### 列表示例

- 支持无序列表
- 支持有序列表
- 支持嵌套列表
  - 子项目1
  - 子项目2

**总结**: Markdown渲染功能已成功集成到AI聊天中！
"""
        self.markdown_view.update_content(test_markdown)
    
    def test_markdown(self):
        """测试Markdown渲染"""
        markdown_text = self.input_text.toPlainText()
        if markdown_text.strip():
            self.markdown_view.update_content(markdown_text)
        else:
            self.set_initial_content()
    
    def test_stream(self):
        """测试流式输出"""
        # 清空内容
        self.markdown_view.update_content("")
        
        # 模拟流式输出
        stream_content = [
            "# 流式输出测试\n\n",
            "正在生成回答",
            "...\n\n",
            "## 第一部分\n\n",
            "这是**流式输出**的第一部分内容。\n\n",
            "## 第二部分\n\n",
            "```python\n",
            "# 代码示例\n",
            "def stream_response():\n",
            "    return '流式输出成功'\n",
            "```\n\n",
            "## 总结\n\n",
            "流式Markdown渲染功能正常工作！"
        ]
        
        # 模拟逐步添加内容
        import time
        from PyQt6.QtCore import QTimer
        
        self.stream_index = 0
        self.stream_data = stream_content
        self.timer = QTimer()
        self.timer.timeout.connect(self.add_stream_chunk)
        self.timer.start(500)  # 每500ms添加一块内容
    
    def add_stream_chunk(self):
        """添加流式内容块"""
        if self.stream_index < len(self.stream_data):
            self.markdown_view.append_content(self.stream_data[self.stream_index])
            self.stream_index += 1
        else:
            self.timer.stop()


if __name__ == '__main__':
    app = QApplication(sys.argv)
    window = TestMarkdownChat()
    window.show()
    sys.exit(app.exec())
