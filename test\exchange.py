code=[]
name=[]
import requests
from bs4 import BeautifulSoup
from rapidfuzz import process, fuzz
url="http://www.cnhuilv.com/currency/"
headers={
    "User-Agent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/135.0.0.0 Safari/537.36 Edg/135.0.0.0"
}
data=requests.get(url).text
# 解析HTML内容
soup = BeautifulSoup(data, 'html.parser')

# 查找所有<tr>标签
rows = soup.find_all('tr', )
code_ex=['AFN', 'BIF','BTC','BYR','CDF','CLF','CNH','CUP','CYP','DEM',
         'ECS','ERN','FRF','IEP','IQD','IRR','ITL','KRW','LTL','LVL', 'LYD',
         'MMK', 'MRO','MXV','RUB', 'SDG', 'SIT', 'SOS', 'STD', 'SYP', 'VEF',
         'XCD', 'XDR','XAG', 'XAU', 'XCP', 'XPD', 'XPT','YER','ZWL']
add_code=["GGP","IMP","JEP"]
# 遍历每一行并提取CNY和人民币
for row in rows:
    # 提取第一个<td>中的CNY
    currency_code_tag = row.find('td').find('span',)
    if currency_code_tag:
        currency_code = currency_code_tag.find_next_sibling(text=True).strip()
    else:
        currency_code = None
    currency_name_tag = row.find('td').find_next_sibling().find_next_sibling('td').find('a')
    if currency_name_tag:
        currency_name = currency_name_tag.get('title')
    else:
        currency_name = None

    if currency_code and currency_name:
        if currency_code not in code_ex:
            code.append(currency_code)
            name.append(currency_name)
            # print(f"货币代码: {currency_code}, 货币名称: {currency_name}")

# # 初始化一个字典用于存储按首字母归类的结果
# classified_currencies = {}
#
# # 遍历每个货币代码
# for code in code:
#     # 获取货币代码的首字母并转换为大写
#     first_letter = code[0].upper()
#     # 如果该首字母不在字典中，初始化一个空列表
#     if first_letter not in classified_currencies:
#         classified_currencies[first_letter] = []
#     # 将货币代码添加到对应首字母的列表中
#     classified_currencies[first_letter].append(code)
#
# # 按首字母 A - Z 排序
# sorted_classified_currencies = dict(sorted(classified_currencies.items()))
#
# # 输出结果
# for letter, codes in sorted_classified_currencies.items():
#     print(f"{letter}: {codes}")
print(code)
print(name)
# 创建一个字典，将代码与名称对应起来
currency_dict = dict(zip(code, name))
currency_dict_re = dict(zip(name, code))


"""
使用rapidfuzz根据输入的查询字符串搜索匹配的货币代码和名称。

:param query: 用户输入的查询字符串
:return: 匹配的货币代码和名称的列表
"""
l=input("请输入：")


def search_currency1(input_code):
    """
    根据输入的货币代码进行模糊匹配，返回所有包含输入字符串的货币代码和名称。

    :param input_code: 用户输入的货币代码（大写）
    :return: 匹配的货币列表
    """
    # 确保输入为大写
    input_code = input_code.upper()

    # 过滤出包含输入字符串的货币代码
    matches = {code: name for code, name in currency_dict.items() if input_code in code}

    return matches
def search_currency(input_code):
    """
    根据输入的货币代码进行模糊匹配，返回所有包含输入字符串的货币代码和名称。

    :param input_code: 用户输入的货币代码（大写）
    :return: 匹配的货币列表
    """
    # 确保输入为大写
    input_str = input_code.upper()

    # 过滤出包含输入字符串的货币代码
    matches = {name: code for name, code in currency_dict_re.items() if input_str in name}

    return matches
results = search_currency(l)

# if results:
#     print("匹配结果：")
#     for code, name in results.items():
#         print(f"代码: {code}, 名称: {name}")
# else:
#     print("未找到匹配的货币。")

if results:
    print("匹配结果：")
    for name, code in results.items():
        print(f"代码: {code}, 名称: {name}")
else:
    print("未找到匹配的货币。")

