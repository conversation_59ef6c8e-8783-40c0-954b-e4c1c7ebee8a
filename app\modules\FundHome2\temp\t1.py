import json

import requests
code="100.NDX"
header = {
    "Host": "push2.eastmoney.com",
    "Connection": "keep-alive",
    "sec-ch-ua-platform": "\"Windows\"",
    "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
    "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
    "sec-ch-ua-mobile": "?0",
    "Accept": "*/*",
    "Sec-Fetch-Site": "same-site",
    "Sec-Fetch-Mode": "no-cors",
    "Sec-Fetch-Dest": "script",
    "Referer": "https://quote.eastmoney.com/gb/zsNDX.html",
    "Accept-Encoding": "gzip, deflate, br, zstd",
    "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
    "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; websitepoptg_api_time=1754238273039; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; EMFUND0=null; EMFUND4=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND5=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND6=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND8=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND9=08-04 12:35:13@#$%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; st_si=13300841178050; fullscreengg=1; fullscreengg2=1; st_asi=delete; _qimei_i_1=46bd45ffc33a; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=190; st_psi=20250805164924550-113200301354-2086618608"
}
url = f"https://push2.eastmoney.com/api/qt/stock/get?invt=2&fltt=1&cb=jQuery35109620139585685238_1754383780373&fields=f58%2Cf107%2Cf57%2Cf43%2Cf59%2Cf169%2Cf170%2Cf152%2Cf46%2Cf60%2Cf44%2Cf45%2Cf47%2Cf48%2Cf19%2Cf532%2Cf39%2Cf161%2Cf49%2Cf171%2Cf50%2Cf86%2Cf600%2Cf601%2Cf154%2Cf84%2Cf85%2Cf168%2Cf108%2Cf116%2Cf167%2Cf164%2Cf92%2Cf71%2Cf117%2Cf292%2Cf113%2Cf114%2Cf115%2Cf119%2Cf120%2Cf121%2Cf122%2Cf296&secid={code}&ut=fa5fd1943c7b386f172d6893dbfba10b&wbp2u=%7C0%7C0%7C0%7Cweb&dect=1&_=1754383780374"


def request_data():
    try:
        response = requests.get(url, headers=header, verify=False)

        # 去除JSONP包装，提取JSON数据
        json_str = response.text.strip("jQuery35109620139585685238_1754383780373(").rstrip(");")
        data = json.loads(json_str)
        print(data)

        # 获取data字段
        stock_data = data.get("data", {})
        # print(stock_data)

        # 按顺序提取数据：今开，昨收，最高，最低，涨跌幅，涨跌额，换手，振幅
        fields = ["f46", "f60", "f44", "f45", "f170", "f169", "f168", "f171"]
        result = []
        i=0
        for field in fields:
            value = stock_data.get(field, "")
            if value == "-" or value is None:
                result.append("")
            elif i in [4, 6, 7]:
                result.append(f"{value / 100}%")
                # 小数点向前移动2位
            else:
                result.append(f"{value / 100}")
            i+=1

        return result

    except Exception as e:
        print(f"请求数据失败: {e}")
        return [""] * 8  # 返回8个空字符串

print(request_data())
