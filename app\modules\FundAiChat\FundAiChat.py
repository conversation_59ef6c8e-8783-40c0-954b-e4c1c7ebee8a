from functools import partial
from PyQt6.QtCore import Qt, QUrl, QTimer
from PyQt6.QtGui import QPixmap, QIcon, QColor, QPalette
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QMessageBox, QGridLayout, QSizePolicy
from PyQt6.QtWebEngineWidgets import QWebEngineView
from PyQt6.QtWebEngineCore import QWebEngineSettings
import markdown
import os

from app.api.widget.chat.chat_api import StreamResponseThread
from main_w import Ui_MainWindow


class MarkdownWebView(QWebEngineView):
    """用于渲染Markdown的Web视图组件，支持自适应高度和数学公式"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.markdown_content = ""
        self.setup_webview()
        self.setup_html_template()

    def setup_webview(self):
        """配置WebView设置"""
        # 设置大小策略为自适应
        self.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

        # 禁用滚动条
        self.settings().setAttribute(QWebEngineSettings.WebAttribute.ShowScrollBars, False)

        # 设置初始最小高度
        self.setMinimumHeight(50)

        # 连接页面加载完成信号，用于调整高度
        self.loadFinished.connect(self.adjust_height)

        # 设置页面背景透明
        self.page().setBackgroundColor(Qt.GlobalColor.transparent)

    def setup_html_template(self):
        """设置HTML模板，包含MathJax支持"""
        self.html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">

            <!-- MathJax配置 -->
            <script>
                window.MathJax = {{
                    tex: {{
                        inlineMath: [['$', '$'], ['\\(', '\\)']],
                        displayMath: [['$$', '$$'], ['\\[', '\\]']],
                        processEscapes: true,
                        processEnvironments: true
                    }},
                    options: {{
                        skipHtmlTags: ['script', 'noscript', 'style', 'textarea', 'pre']
                    }}
                }};
            </script>
            <script src="https://polyfill.io/v3/polyfill.min.js?features=es6"></script>
            <script id="MathJax-script" async src="https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js"></script>

            <style>
                * {{
                    box-sizing: border-box;
                }}

                html, body {{
                    margin: 0;
                    padding: 0;
                    overflow: hidden;
                    background: transparent;
                }}

                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #222222;
                    background-color: white;
                    margin: 0;
                    padding: 15px;
                    border-radius: 10px;
                    min-height: fit-content;
                }}

                h1, h2, h3, h4, h5, h6 {{
                    color: #333;
                    margin-top: 1.5em;
                    margin-bottom: 0.5em;
                }}

                h1:first-child, h2:first-child, h3:first-child,
                h4:first-child, h5:first-child, h6:first-child {{
                    margin-top: 0;
                }}

                p {{
                    margin-bottom: 1em;
                }}

                p:last-child {{
                    margin-bottom: 0;
                }}

                code {{
                    background-color: #f4f4f4;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: 'Courier New', Courier, monospace;
                    font-size: 0.9em;
                }}

                pre {{
                    background-color: #f8f8f8;
                    border: 1px solid #e1e1e1;
                    border-radius: 5px;
                    padding: 15px;
                    overflow-x: auto;
                    margin: 1em 0;
                }}

                pre code {{
                    background-color: transparent;
                    padding: 0;
                    font-size: 0.9em;
                }}

                blockquote {{
                    border-left: 4px solid #ddd;
                    margin: 1em 0;
                    padding-left: 16px;
                    color: #666;
                    font-style: italic;
                }}

                ul, ol {{
                    padding-left: 20px;
                    margin: 1em 0;
                }}

                li {{
                    margin-bottom: 0.5em;
                }}

                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 1em 0;
                }}

                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}

                th {{
                    background-color: #f2f2f2;
                    font-weight: bold;
                }}

                a {{
                    color: #0066cc;
                    text-decoration: none;
                }}

                a:hover {{
                    text-decoration: underline;
                }}

                /* 数学公式样式 */
                .MathJax {{
                    font-size: 1em !important;
                }}

                /* 确保内容不会超出容器 */
                img {{
                    max-width: 100%;
                    height: auto;
                }}

                /* Pygments代码高亮样式 */
                .highlight {{
                    background-color: #f8f8f8;
                    border: 1px solid #e1e1e1;
                    border-radius: 5px;
                    padding: 15px;
                    margin: 1em 0;
                    overflow-x: auto;
                    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
                    font-size: 0.9em;
                    line-height: 1.4;
                }}

                .highlight pre {{
                    margin: 0;
                    padding: 0;
                    background: transparent;
                    border: none;
                    overflow: visible;
                }}

                /* 代码高亮颜色主题 */
                .highlight .hll {{ background-color: #ffffcc }}
                .highlight .c {{ color: #8f5902; font-style: italic }} /* Comment */
                .highlight .err {{ color: #a40000; border: 1px solid #ef2929 }} /* Error */
                .highlight .g {{ color: #000000 }} /* Generic */
                .highlight .k {{ color: #204a87; font-weight: bold }} /* Keyword */
                .highlight .l {{ color: #000000 }} /* Literal */
                .highlight .n {{ color: #000000 }} /* Name */
                .highlight .o {{ color: #ce5c00; font-weight: bold }} /* Operator */
                .highlight .x {{ color: #000000 }} /* Other */
                .highlight .p {{ color: #000000; font-weight: bold }} /* Punctuation */
                .highlight .ch {{ color: #8f5902; font-style: italic }} /* Comment.Hashbang */
                .highlight .cm {{ color: #8f5902; font-style: italic }} /* Comment.Multiline */
                .highlight .cp {{ color: #8f5902; font-style: italic }} /* Comment.Preproc */
                .highlight .cpf {{ color: #8f5902; font-style: italic }} /* Comment.PreprocFile */
                .highlight .c1 {{ color: #8f5902; font-style: italic }} /* Comment.Single */
                .highlight .cs {{ color: #8f5902; font-style: italic }} /* Comment.Special */
                .highlight .gd {{ color: #a40000 }} /* Generic.Deleted */
                .highlight .ge {{ color: #000000; font-style: italic }} /* Generic.Emph */
                .highlight .gr {{ color: #ef2929 }} /* Generic.Error */
                .highlight .gh {{ color: #000080; font-weight: bold }} /* Generic.Heading */
                .highlight .gi {{ color: #00A000 }} /* Generic.Inserted */
                .highlight .go {{ color: #000000; font-style: italic }} /* Generic.Output */
                .highlight .gp {{ color: #8f5902 }} /* Generic.Prompt */
                .highlight .gs {{ color: #000000; font-weight: bold }} /* Generic.Strong */
                .highlight .gu {{ color: #800080; font-weight: bold }} /* Generic.Subheading */
                .highlight .gt {{ color: #a40000; font-weight: bold }} /* Generic.Traceback */
                .highlight .kc {{ color: #204a87; font-weight: bold }} /* Keyword.Constant */
                .highlight .kd {{ color: #204a87; font-weight: bold }} /* Keyword.Declaration */
                .highlight .kn {{ color: #204a87; font-weight: bold }} /* Keyword.Namespace */
                .highlight .kp {{ color: #204a87; font-weight: bold }} /* Keyword.Pseudo */
                .highlight .kr {{ color: #204a87; font-weight: bold }} /* Keyword.Reserved */
                .highlight .kt {{ color: #204a87; font-weight: bold }} /* Keyword.Type */
                .highlight .ld {{ color: #000000 }} /* Literal.Date */
                .highlight .m {{ color: #0000cf; font-weight: bold }} /* Literal.Number */
                .highlight .s {{ color: #4e9a06 }} /* Literal.String */
                .highlight .na {{ color: #c4a000 }} /* Name.Attribute */
                .highlight .nb {{ color: #204a87 }} /* Name.Builtin */
                .highlight .nc {{ color: #000000 }} /* Name.Class */
                .highlight .no {{ color: #000000 }} /* Name.Constant */
                .highlight .nd {{ color: #5c35cc; font-weight: bold }} /* Name.Decorator */
                .highlight .ni {{ color: #ce5c00 }} /* Name.Entity */
                .highlight .ne {{ color: #cc0000; font-weight: bold }} /* Name.Exception */
                .highlight .nf {{ color: #000000 }} /* Name.Function */
                .highlight .nl {{ color: #f57900 }} /* Name.Label */
                .highlight .nn {{ color: #000000 }} /* Name.Namespace */
                .highlight .nx {{ color: #000000 }} /* Name.Other */
                .highlight .py {{ color: #000000 }} /* Name.Property */
                .highlight .nt {{ color: #204a87; font-weight: bold }} /* Name.Tag */
                .highlight .nv {{ color: #000000 }} /* Name.Variable */
                .highlight .ow {{ color: #204a87; font-weight: bold }} /* Operator.Word */
                .highlight .w {{ color: #f8f8f8; text-decoration: underline }} /* Text.Whitespace */
                .highlight .mb {{ color: #0000cf; font-weight: bold }} /* Literal.Number.Bin */
                .highlight .mf {{ color: #0000cf; font-weight: bold }} /* Literal.Number.Float */
                .highlight .mh {{ color: #0000cf; font-weight: bold }} /* Literal.Number.Hex */
                .highlight .mi {{ color: #0000cf; font-weight: bold }} /* Literal.Number.Integer */
                .highlight .mo {{ color: #0000cf; font-weight: bold }} /* Literal.Number.Oct */
                .highlight .sa {{ color: #4e9a06 }} /* Literal.String.Affix */
                .highlight .sb {{ color: #4e9a06 }} /* Literal.String.Backtick */
                .highlight .sc {{ color: #4e9a06 }} /* Literal.String.Char */
                .highlight .dl {{ color: #4e9a06 }} /* Literal.String.Delimiter */
                .highlight .sd {{ color: #8f5902; font-style: italic }} /* Literal.String.Doc */
                .highlight .s2 {{ color: #4e9a06 }} /* Literal.String.Double */
                .highlight .se {{ color: #4e9a06 }} /* Literal.String.Escape */
                .highlight .sh {{ color: #4e9a06 }} /* Literal.String.Heredoc */
                .highlight .si {{ color: #4e9a06 }} /* Literal.String.Interpol */
                .highlight .sx {{ color: #4e9a06 }} /* Literal.String.Other */
                .highlight .sr {{ color: #4e9a06 }} /* Literal.String.Regex */
                .highlight .s1 {{ color: #4e9a06 }} /* Literal.String.Single */
                .highlight .ss {{ color: #4e9a06 }} /* Literal.String.Symbol */
                .highlight .bp {{ color: #3465a4 }} /* Name.Builtin.Pseudo */
                .highlight .fm {{ color: #000000 }} /* Name.Function.Magic */
                .highlight .vc {{ color: #000000 }} /* Name.Variable.Class */
                .highlight .vg {{ color: #000000 }} /* Name.Variable.Global */
                .highlight .vi {{ color: #000000 }} /* Name.Variable.Instance */
                .highlight .vm {{ color: #000000 }} /* Name.Variable.Magic */
                .highlight .il {{ color: #0000cf; font-weight: bold }} /* Literal.Number.Integer.Long */
            </style>
        </head>
        <body>
            <div id="content">{content}</div>

            <script>
                // 内容更新后重新渲染数学公式
                function updateMathJax() {{
                    if (window.MathJax && window.MathJax.typesetPromise) {{
                        window.MathJax.typesetPromise().then(() => {{
                            // 通知父窗口调整高度
                            window.setTimeout(() => {{
                                const height = document.body.scrollHeight;
                                console.log('Content height:', height);
                            }}, 100);
                        }});
                    }}
                }}

                // 页面加载完成后执行
                document.addEventListener('DOMContentLoaded', updateMathJax);
            </script>
        </body>
        </html>
        """

    def adjust_height(self):
        """根据内容调整WebView高度"""
        # 使用JavaScript获取内容高度
        self.page().runJavaScript(
            "document.body.scrollHeight;",
            self.set_height_from_content
        )

    def set_height_from_content(self, height):
        """根据内容高度设置WebView高度"""
        if height and height > 0:
            # 添加一些额外的边距
            new_height = int(height) + 20
            # 设置最小和最大高度限制
            new_height = max(50, min(new_height, 2000))
            self.setFixedHeight(new_height)

            # 通知父组件需要滚动到底部
            if hasattr(self, 'scroll_to_bottom_callback'):
                self.scroll_to_bottom_callback()

    def update_content(self, markdown_text):
        """更新Markdown内容"""
        self.markdown_content = markdown_text
        # 将Markdown转换为HTML，使用基本扩展
        try:
            html_content = markdown.markdown(
                markdown_text,
                extensions=['tables', 'fenced_code', 'nl2br', 'toc','codehilite'],
                extension_configs={
                    'codehilite': {
                        'use_pygments': True,  # 使用 Pygments 高亮
                        'css_class': 'highlight',  # 自定义 CSS 类名
                    },
                }
            )
        except:
            # 如果扩展不可用，使用基本转换
            html_content = markdown.markdown(markdown_text)

        # 插入到HTML模板中
        full_html = self.html_template.format(content=html_content)
        self.setHtml(full_html)

        # 延迟调整高度，等待内容渲染完成
        QTimer.singleShot(700, self.adjust_height)

    def append_content(self, chunk):
        """追加内容（用于流式输出）"""
        self.markdown_content += chunk
        self.update_content(self.markdown_content)

        # 内容更新后触发滚动
        if hasattr(self, 'scroll_to_bottom_callback'):
            QTimer.singleShot(100, self.scroll_to_bottom_callback)


class FundAiChat:
    def __init__(self, ui:Ui_MainWindow):

        self.ui = ui
        self.init_setup()

    def init_setup(self):  # 初始化变量
        self.ui.chat_widget.hide()

        self.img_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"  # 图片地址
        self.model_name = "deepseek-v3-241226"
        self.chat_history = []  # 保存聊天记录
        self.current_thread = None  # 当前流式输出线程
        self.ui.comboBox_7.currentIndexChanged.connect(self.change_chat_model)
        self.chat_container = QWidget()  # 17
        self.chat_layout = QVBoxLayout(self.chat_container)  # 34
        self.chat_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.ui.scrollArea_2.setWidget(self.chat_container)
        self.pre_send_status = True
        self.ui.chat_bg_lb.setPixmap(QPixmap(self.img_path + r'\chat_bg.png').scaled(1720, 1000))
        icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png")  # 替换为你的图片路径
        self.ui.chat_send_btn.setIcon(icon)
        self.ui.chat_send_btn.setIconSize(
            QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png").size())  # 设置图标大小
        self.ui.chat_send_btn.setToolTip("发送")
        # self.ui.chat_input.setPlaceholderText("请输入您的问题...")
        self.ui.chat_send_btn.clicked.connect(self.toggle_send_stop)  # 绑定切换按钮事件
        self.ui.chat_clear_btn.clicked.connect(self.clear_context)


    def change_chat_model(self):
        if self.ui.comboBox_7.currentIndex() == 0:
            self.model_name = "deepseek-v3-241226"
        elif self.ui.comboBox_7.currentIndex() == 1:
            self.model_name = "deepseek-r1-250120"
        elif self.ui.comboBox_7.currentIndex() == 2:
            self.model_name = "deepseek-r1-distill-qwen-7b-250120"
        elif self.ui.comboBox_7.currentIndex() == 3:
            self.model_name = "deepseek-r1-distill-qwen-32b-250120"
        elif self.ui.comboBox_7.currentIndex() == 4:
            self.model_name = "doubao-1-5-pro-256k-250115"
        elif self.ui.comboBox_7.currentIndex() == 5:
            self.model_name = "doubao-1-5-pro-32k-250115"

    def toggle_send_stop(self):
        if self.pre_send_status:
            self.ui.chat_send_btn.setToolTip("停止")
            self.send_question()
            self.pre_send_status = False
        else:
            self.pre_send_status = True
            self.stop_response()
            self.ui.chat_send_btn.setToolTip("发送")

    def send_question(self):
        try:
            question = self.ui.chat_input.toPlainText().strip()
            if not question:
                QMessageBox.warning(self.ui.chatai_widget, "提示", "请输入问题！")
                return
            self.add_user_message(question)  # 添加用户问题到聊天窗口
            # 启动流式输出线程
            self.current_thread = StreamResponseThread(self.model_name, question, self.chat_history)
            self.current_thread.stream_signal.connect(self.add_ai_message_stream)
            self.current_thread.finished.connect(self.thread_finished)
            self.current_thread.start()
            self.ui.chat_input.clear()  # 清空输入框
            self.toggle_send_stop_img(True)
        except Exception as e:
            print(e)

    def stop_response(self):
        # 终止当前流式输出线程
        if self.current_thread and self.current_thread.isRunning():
            self.current_thread.stop()
            self.current_thread.wait()
        self.toggle_send_stop_img(False)

    def add_user_message(self, message):
        # 创建用户问题的 widget
        user_widget = QWidget()
        user_layout = QVBoxLayout(user_widget)
        user_label = QLabel(f"用户：{message}")
        user_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        user_label.setStyleSheet("""
                                    color: #0A0A0A; 
                                    font-weight: bold;
                                    background-color:#F4F5FF;
                                    border-radius:10px;
                                    border:1px solid #9281FF;
                                    padding:5px;
                                    font-size:17px;
                                """
                                 )
        user_layout.addWidget(user_label)
        self.chat_layout.addWidget(user_widget)
        self.chat_history.append({"role": "user", "content": message})

    def add_ai_message_stream(self, chunk):
        try:
            # 获取最后一个 AI 回答的 widget，如果没有则新建
            if not self.chat_history or not self.chat_history[-1]["role"] == "assistant":
                self.chat_history.append({"role": "assistant", "content": ""})

                # 创建AI回答的容器widget
                ai_widget = QWidget()
                ai_widget.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)
                ai_layout = QVBoxLayout(ai_widget)
                ai_layout.setContentsMargins(5, 5, 5, 5)
                ai_layout.setSpacing(0)

                # 创建圆角容器来实现圆角效果
                rounded_container = QWidget()
                rounded_container.setStyleSheet("""
                    QWidget {
                        background-color: white;
                        border: 1px solid #EAEDF1;
                        border-radius: 10px;
                    }
                    QWidget:hover {
                        border: 1px solid #ccc;
                        background-color: #F1F9FE;
                    }
                """)
                rounded_container.setSizePolicy(QSizePolicy.Policy.Expanding, QSizePolicy.Policy.Minimum)

                # 创建圆角容器的布局
                container_layout = QVBoxLayout(rounded_container)
                container_layout.setContentsMargins(0, 0, 0, 0)
                container_layout.setSpacing(0)

                # 创建Markdown渲染的Web视图
                self.ai_webview = MarkdownWebView()
                self.ai_webview.setStyleSheet("""
                    QWebEngineView {
                        border: none;
                        background-color: transparent;
                    }
                """)

                # 将WebView添加到圆角容器中
                container_layout.addWidget(self.ai_webview)

                # 将圆角容器添加到AI widget中
                ai_layout.addWidget(rounded_container)

                # 将AI widget添加到聊天布局中
                self.chat_layout.addWidget(ai_widget)

                # 存储当前的WebView引用，用于后续的流式更新
                self.current_ai_webview = self.ai_webview
            else:
                # 获取当前正在更新的WebView组件
                self.ai_webview = self.current_ai_webview

            # 追加流式输出内容到Markdown渲染器
            self.ai_webview.append_content(chunk)
            self.chat_history[-1]["content"] += chunk  # 更新聊天历史

            # 设置滚动回调
            self.ai_webview.scroll_to_bottom_callback = self.scroll_to_bottom

            # 确保滚动到最新内容 - 多重保障
            self.scroll_to_bottom()

        except Exception as e:
            print(f"Error in add_ai_message_stream: {e}")

    def scroll_to_bottom(self):
        """强制滚动到底部的方法"""
        try:
            # 方法1: 使用ensureWidgetVisible确保最后一个widget可见
            if self.chat_layout.count() > 0:
                last_widget = self.chat_layout.itemAt(self.chat_layout.count() - 1).widget()
                if last_widget:
                    self.ui.scrollArea_2.ensureWidgetVisible(last_widget)

            # 方法2: 直接设置滚动条到最大值
            scroll_bar = self.ui.scrollArea_2.verticalScrollBar()
            if scroll_bar:
                # 使用QTimer延迟执行，确保内容已经渲染完成
                QTimer.singleShot(50, lambda: scroll_bar.setValue(scroll_bar.maximum()))
                QTimer.singleShot(200, lambda: scroll_bar.setValue(scroll_bar.maximum()))
                QTimer.singleShot(500, lambda: scroll_bar.setValue(scroll_bar.maximum()))

        except Exception as e:
            print(f"Error in scroll_to_bottom: {e}")

    def clear_context(self):
        # 清除聊天记录
        for i in reversed(range(self.chat_layout.count())):
            self.chat_layout.itemAt(i).widget().setParent(None)
        self.chat_history = []

    def thread_finished(self):
        # 线程结束后的操作
        self.current_thread = None
        self.toggle_send_stop_img(False)

        # 确保最终滚动到底部
        QTimer.singleShot(500, self.scroll_to_bottom)
        QTimer.singleShot(1000, self.scroll_to_bottom)

    def toggle_send_stop_img(self, status):
        if status:
            icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\stop_16.png")  # 替换为你的图片路径
            self.ui.chat_send_btn.setIcon(icon)
            self.ui.chat_send_btn.setIconSize(
                QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\stop_16.png").size())  # 设置图标大小
            self.pre_send_status = False
            self.ui.chat_send_btn.setToolTip("停止")
        else:
            icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png")  # 替换为你的图片路径
            self.ui.chat_send_btn.setIcon(icon)
            self.ui.chat_send_btn.setIconSize(
                QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png").size())  # 设置图标大小
            self.pre_send_status = True
            self.ui.chat_send_btn.setToolTip("发送")