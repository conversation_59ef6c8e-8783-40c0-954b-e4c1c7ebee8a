from functools import partial
from PyQt6.QtCore import Qt, QUrl
from PyQt6.QtGui import QPixmap, QIcon, QColor, QPalette
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QLabel, QMessageBox, QGridLayout
from PyQt6.QtWebEngineWidgets import QWebEngineView
import markdown
import os

from app.api.widget.chat.chat_api import StreamResponseThread
from main_w import Ui_MainWindow


class MarkdownWebView(QWebEngineView):
    """用于渲染Markdown的Web视图组件"""

    def __init__(self, parent=None):
        super().__init__(parent)
        self.setMinimumHeight(100)
        self.markdown_content = ""
        self.setup_html_template()

    def setup_html_template(self):
        """设置HTML模板"""
        self.html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body {{
                    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
                    line-height: 1.6;
                    color: #222222;
                    background-color: white;
                    margin: 0;
                    padding: 10px;
                    border-radius: 10px;
                }}

                h1, h2, h3, h4, h5, h6 {{
                    color: #333;
                    margin-top: 1.5em;
                    margin-bottom: 0.5em;
                }}

                p {{
                    margin-bottom: 1em;
                }}

                code {{
                    background-color: #f4f4f4;
                    padding: 2px 4px;
                    border-radius: 3px;
                    font-family: 'Courier New', Courier, monospace;
                }}

                pre {{
                    background-color: #f8f8f8;
                    border: 1px solid #e1e1e1;
                    border-radius: 5px;
                    padding: 10px;
                    overflow-x: auto;
                }}

                pre code {{
                    background-color: transparent;
                    padding: 0;
                }}

                blockquote {{
                    border-left: 4px solid #ddd;
                    margin: 0;
                    padding-left: 16px;
                    color: #666;
                }}

                ul, ol {{
                    padding-left: 20px;
                }}

                li {{
                    margin-bottom: 0.5em;
                }}

                table {{
                    border-collapse: collapse;
                    width: 100%;
                    margin: 1em 0;
                }}

                th, td {{
                    border: 1px solid #ddd;
                    padding: 8px;
                    text-align: left;
                }}

                th {{
                    background-color: #f2f2f2;
                    font-weight: bold;
                }}

                a {{
                    color: #0066cc;
                    text-decoration: none;
                }}

                a:hover {{
                    text-decoration: underline;
                }}
            </style>
        </head>
        <body>
            {content}
        </body>
        </html>
        """

    def update_content(self, markdown_text):
        """更新Markdown内容"""
        self.markdown_content = markdown_text
        # 将Markdown转换为HTML，使用基本扩展
        try:
            html_content = markdown.markdown(
                markdown_text,
                extensions=['tables', 'fenced_code', 'nl2br']
            )
        except:
            # 如果扩展不可用，使用基本转换
            html_content = markdown.markdown(markdown_text)

        # 插入到HTML模板中
        full_html = self.html_template.format(content=html_content)
        self.setHtml(full_html)

    def append_content(self, chunk):
        """追加内容（用于流式输出）"""
        self.markdown_content += chunk
        self.update_content(self.markdown_content)


class FundAiChat:
    def __init__(self, ui:Ui_MainWindow):

        self.ui = ui
        self.init_setup()

    def init_setup(self):  # 初始化变量
        self.ui.chat_widget.hide()

        self.img_path = r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"  # 图片地址
        self.model_name = "deepseek-v3-241226"
        self.chat_history = []  # 保存聊天记录
        self.current_thread = None  # 当前流式输出线程
        self.ui.comboBox_7.currentIndexChanged.connect(self.change_chat_model)
        self.chat_container = QWidget()  # 17
        self.chat_layout = QVBoxLayout(self.chat_container)  # 34
        self.chat_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.ui.scrollArea_2.setWidget(self.chat_container)
        self.pre_send_status = True
        self.ui.chat_bg_lb.setPixmap(QPixmap(self.img_path + r'\chat_bg.png').scaled(1720, 1000))
        icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png")  # 替换为你的图片路径
        self.ui.chat_send_btn.setIcon(icon)
        self.ui.chat_send_btn.setIconSize(
            QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png").size())  # 设置图标大小
        self.ui.chat_send_btn.setToolTip("发送")
        # self.ui.chat_input.setPlaceholderText("请输入您的问题...")
        self.ui.chat_send_btn.clicked.connect(self.toggle_send_stop)  # 绑定切换按钮事件
        self.ui.chat_clear_btn.clicked.connect(self.clear_context)


    def change_chat_model(self):
        if self.ui.comboBox_7.currentIndex() == 0:
            self.model_name = "deepseek-v3-241226"
        elif self.ui.comboBox_7.currentIndex() == 1:
            self.model_name = "deepseek-r1-250120"
        elif self.ui.comboBox_7.currentIndex() == 2:
            self.model_name = "deepseek-r1-distill-qwen-7b-250120"
        elif self.ui.comboBox_7.currentIndex() == 3:
            self.model_name = "deepseek-r1-distill-qwen-32b-250120"
        elif self.ui.comboBox_7.currentIndex() == 4:
            self.model_name = "doubao-1-5-pro-256k-250115"
        elif self.ui.comboBox_7.currentIndex() == 5:
            self.model_name = "doubao-1-5-pro-32k-250115"

    def toggle_send_stop(self):
        if self.pre_send_status:
            self.ui.chat_send_btn.setToolTip("停止")
            self.send_question()
            self.pre_send_status = False
        else:
            self.pre_send_status = True
            self.stop_response()
            self.ui.chat_send_btn.setToolTip("发送")

    def send_question(self):
        try:
            question = self.ui.chat_input.toPlainText().strip()
            if not question:
                QMessageBox.warning(self.ui.chatai_widget, "提示", "请输入问题！")
                return
            self.add_user_message(question)  # 添加用户问题到聊天窗口
            # 启动流式输出线程
            self.current_thread = StreamResponseThread(self.model_name, question, self.chat_history)
            self.current_thread.stream_signal.connect(self.add_ai_message_stream)
            self.current_thread.finished.connect(self.thread_finished)
            self.current_thread.start()
            self.ui.chat_input.clear()  # 清空输入框
            self.toggle_send_stop_img(True)
        except Exception as e:
            print(e)

    def stop_response(self):
        # 终止当前流式输出线程
        if self.current_thread and self.current_thread.isRunning():
            self.current_thread.stop()
            self.current_thread.wait()
        self.toggle_send_stop_img(False)

    def add_user_message(self, message):
        # 创建用户问题的 widget
        user_widget = QWidget()
        user_layout = QVBoxLayout(user_widget)
        user_label = QLabel(f"用户：{message}")
        user_label.setAlignment(Qt.AlignmentFlag.AlignRight)
        user_label.setStyleSheet("""
                                    color: #0A0A0A; 
                                    font-weight: bold;
                                    background-color:#F4F5FF;
                                    border-radius:10px;
                                    border:1px solid #9281FF;
                                    padding:5px;
                                    font-size:17px;
                                """
                                 )
        user_layout.addWidget(user_label)
        self.chat_layout.addWidget(user_widget)
        self.chat_history.append({"role": "user", "content": message})

    def add_ai_message_stream(self, chunk):
        try:
            # 获取最后一个 AI 回答的 widget，如果没有则新建
            if not self.chat_history or not self.chat_history[-1]["role"] == "assistant":
                self.chat_history.append({"role": "assistant", "content": ""})
                ai_widget = QWidget()
                ai_widget.setMinimumHeight(150)  # 设置最小高度
                ai_layout = QVBoxLayout(ai_widget)
                ai_layout.setContentsMargins(5, 5, 5, 5)  # 设置边距

                # 创建Markdown渲染的Web视图
                self.ai_webview = MarkdownWebView()
                self.ai_webview.setStyleSheet("""
                    QWebEngineView {
                        border: 1px solid #EAEDF1;
                        border-radius: 10px;
                        background-color: white;
                    }
                    QWebEngineView:hover {
                        border: 1px solid #ccc;
                    }
                """)

                ai_layout.addWidget(self.ai_webview)
                self.chat_layout.addWidget(ai_widget)
            else:
                # 获取现有的WebView组件
                ai_widget = self.chat_layout.itemAt(self.chat_layout.count() - 1).widget()
                self.ai_webview = ai_widget.layout().itemAt(0).widget()

            # 追加流式输出内容到Markdown渲染器
            self.ai_webview.append_content(chunk)
            self.chat_history[-1]["content"] += chunk  # 更新聊天历史
            self.ui.scrollArea_2.ensureWidgetVisible(self.ai_webview)
        except Exception as e:
            print(f"Error in add_ai_message_stream: {e}")

    def clear_context(self):
        # 清除聊天记录
        for i in reversed(range(self.chat_layout.count())):
            self.chat_layout.itemAt(i).widget().setParent(None)
        self.chat_history = []

    def thread_finished(self):
        # 线程结束后的操作
        self.current_thread = None
        self.toggle_send_stop_img(False)

    def toggle_send_stop_img(self, status):
        if status:
            icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\stop_16.png")  # 替换为你的图片路径
            self.ui.chat_send_btn.setIcon(icon)
            self.ui.chat_send_btn.setIconSize(
                QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\stop_16.png").size())  # 设置图标大小
            self.pre_send_status = False
            self.ui.chat_send_btn.setToolTip("停止")
        else:
            icon = QIcon(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png")  # 替换为你的图片路径
            self.ui.chat_send_btn.setIcon(icon)
            self.ui.chat_send_btn.setIconSize(
                QPixmap(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img\send_bg32.png").size())  # 设置图标大小
            self.pre_send_status = True
            self.ui.chat_send_btn.setToolTip("发送")