url="https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?cb=jQuery112306836466859791036_1754545642554&lmt=0&klt=1&fields1=f1%2Cf2%2Cf3%2Cf7&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61%2Cf62%2Cf63%2Cf64%2Cf65&ut=b2884a393a59ad64002292a3e90d46a5&secid=1.000001&secid2=0.399001&_=1754545642555"
headers={
  "Host": "push2.eastmoney.com",
  "Connection": "keep-alive",
  "sec-ch-ua-platform": "\"Windows\"",
  "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
  "sec-ch-ua": "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"",
  "sec-ch-ua-mobile": "?0",
  "Accept": "*/*",
  "Sec-Fetch-Site": "same-site",
  "Sec-Fetch-Mode": "no-cors",
  "Sec-Fetch-Dest": "script",
  "Referer": "https://data.eastmoney.com/zjlx/dpzjlx.html",
  "Accept-Encoding": "gzip, deflate, br, zstd",
  "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
  "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; EMFUND1=null; EMFUND2=null; EMFUND3=null; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; EMFUND0=null; EMFUND4=07-31%2012%3A45%3A43@%23%24%u666F%u987A%u957F%u57CE%u5185%u9700%u589E%u957F%u6DF7%u5408A@%23%24260104; EMFUND5=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND6=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND7=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND8=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND9=08-04 12:35:13@#$%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; st_si=36092087756892; st_asi=delete; _qimei_i_1=66b24e83cb10; fullscreengg=1; fullscreengg2=1; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=6; st_psi=20250807171723350-113300300871-1872523555"
}
url="https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?cb=jQuery112306836466859791036_1754545642554&lmt=0&klt=1&fields1=f1%2Cf2%2Cf3%2Cf7&fields2=f51%2Cf52%2Cf53%2Cf54%2Cf55%2Cf56%2Cf57%2Cf58%2Cf59%2Cf60%2Cf61%2Cf62%2Cf63%2Cf64%2Cf65&ut=b2884a393a59ad64002292a3e90d46a5&secid=1.000001&secid2=0.399001&_=1754545642555"

def request_data():
    import requests
    import json
    
    # 修改headers，移除压缩相关的Accept-Encoding
    headers_modified = headers.copy()
    headers_modified["Accept-Encoding"] = "identity"  # 不接受压缩

    try:
        response = requests.get(url, headers=headers_modified, verify=False)
        response.encoding = 'utf-8'  # 手动设置编码
        print(response.text)
        
        # 去除JSONP包装，提取JSON数据
        json_str = response.text.strip("jQuery112306836466859791036_1754545642554(").rstrip(");")
        data = json.loads(json_str)
        
        # 获取klines数据
        klines = data["data"]["klines"]
        
        # 初始化6个列表
        time_list = []
        main_net_flow = []  # 今日主力净流入
        super_large_net_flow = []  # 今日超大单净流入
        large_net_flow = []  # 今日大单净流入
        medium_net_flow = []  # 今日中单净流入
        small_net_flow = []  # 今日小单净流入
        
        for kline in klines:
            parts = kline.split(",")
            
            # 提取时间，只保留时分
            datetime_str = parts[0]
            time_part = datetime_str.split(" ")[1]  # 获取时间部分
            time_list.append(time_part)
            
            # 提取资金流向数据，转换为亿元单位，保留4位小数
            main_net_flow.append(round(float(parts[1]) / 100000000, 4))
            super_large_net_flow.append(round(float(parts[2]) / 100000000, 4))
            large_net_flow.append(round(float(parts[3]) / 100000000, 4))
            medium_net_flow.append(round(float(parts[4]) / 100000000, 4))
            small_net_flow.append(round(float(parts[5]) / 100000000, 4))
        
        return time_list, main_net_flow, super_large_net_flow, large_net_flow, medium_net_flow, small_net_flow
        
    except Exception as e:
        print(f"请求数据失败: {e}")
        return [], [], [], [], [], []

print(request_data())



