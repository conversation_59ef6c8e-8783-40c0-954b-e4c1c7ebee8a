# 数据api
from app.modules.FundAiChat.FundAiChat import FundAiChat
from app.modules.FundCalculator.FundCalculator import FundCalculator
from app.modules.FundDTPH.FundDTPH import FundDTPH
from app.modules.FundInformation.FundInformation import FundInformation
from app.api.widget.index.get_table_data import table_data

from app.common.api.code_company import FundCompany
from app.modules.FundCompany_.FundCompany_ import FundCompany_
from app.modules.FundCompare.FundCompare import FundCompare
from app.modules.FundFilter.FundFilter import FundFilter
from app.modules.FundHome1.FundHome1 import FundHome1
from app.modules.FundHome2.FundHome2 import FundHome2
from app.modules.FundHome3.FundHome3 import FundHome3
from app.modules.FundKFJJ.FundKFJJ import FundKFJJ
from app.modules.FundNewFound.FundNewFound import FundNewFound
from app.modules.FundSelection.FundSelection import FundSelection
from app.modules.FundSubject.FundSubject import FundSubject
from app.modules.Fundhb.FundHb import FundHb
# from showZJLX import ZJP<PERSON><PERSON>indow
from main_w import Ui_MainWindow
from app.api.other.wrapp import calculate_time
from PyQt6.QtCore import Qt
#基础模块
import asyncio, time
from datetime import datetime, timedelta, date
from functools import partial
import concurrent.futures
#函数
from PyQt6.QtNetwork import *
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *
from PyQt6.QtWidgets import QGraphicsDropShadowEffect
# from PyQt6.QtWebEngineCore import QWebEnginePage
import urllib3
urllib3.disable_warnings()

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.ui: Ui_MainWindow = Ui_MainWindow()  # 添加类型注释
        self.ui.setupUi(self)

        # init_hot_widget_status(self)
        # init_news_widget_status(self)
        # init_top_table_widget_status(self)






        #顶部widget切换
        self.top_widget_index=0#顶部widget切换索引
        # self.show_hide_top_widget(self.top_widget_index)
        self.ui.left_widget_change.mousePressEvent=self.change_left_widget
        self.ui.right_widget_change.mousePressEvent=self.change_right_widget

        #顶部样式
        # for i in range(1,5):getattr(self.ui,f"top_img_{i}").setPixmap(QPixmap(self.img_path+fr'\top{i}.png'))# top_bar 左边图片+文字
        # self.top_m_png.setPixmap(QPixmap(self.img_path + r'\line_plot_transparent.png').scaled(300, 100))#top_moneny 收益图片

        # self.zx_widget.hide()  # 资讯页面隐藏
        # self.chatai_widget.hide()  #顶部chatai隐藏

        # self.total_tb_widget.hide()#top表格隐藏
        # # self.chat_widget.hide()#chat界面隐藏

        #TODO top_table

        # for name in ["zs", "qz", "qq","bk","ph","xg","cyb","kcb","jj","gg","hs","mg","qh","hj","wh"]:  # 添加你需要的所有名称
        #     label = getattr(self, f"index_top_{name}")
        #     label.mousePressEvent = partial(self.handle_mouse_pressself, name=name)
        # self.top_table_return.mousePressEvent=self.toggle_table_index_widget
        # self.before_page_lb.mousePressEvent=self.sub_page
        # self.next_page_lb.mousePressEvent=self.add_page
        # self.tail_page_lb.mousePressEvent=self.tail_page
        # self.first_page_lb.mousePressEvent=self.first_page
        # self.goto_page_lb.mousePressEvent=self.goto_page

        # 首页
        # self.scrollArea.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)#index_scroll，
        # self.scrollArea_2.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOff)#chat_scroll，

        #基金比较
        # self.fund_compare=FundCompare(self.ui)
        #基金自选
        # self.fund_selection=FundSelection(self.ui)
        #基金超市
        # self.fund_filter=FundFilter(self.ui)
        #新发基金
        # self.fund_new_found=FundNewFound(self.ui)
        #主题基金
        # self.fund_subject=FundSubject(self.ui)
        #基金公司
        # self.fund_company=FundCompany_(self.ui)

        #首页-历史记录-搜索-7*24消息-top_1
        self.fund_home_1=FundHome1(self.ui)
        #首页-图表-板块top_2
        self.fund_home_2=FundHome2(self.ui)
        #首页-推荐基金-主题
        self.fund_home_3=FundHome3(self.ui)

        # 改写完成

        #货币基金
        self.fund_hb=FundHb(self.ui)
        #计算器
        self.fund_calculator=FundCalculator(self.ui)
        #资讯
        self.fund_information=FundInformation(self.ui)
        #开放基金
        self.fund_kfjj=FundKFJJ(self.ui)
        #定投排行
        self.fund_dtph=FundDTPH(self.ui)
        #AI聊天
        self.fund_ai_chat=FundAiChat(self.ui)



    # """定投"""
    # def sub_page_dt(self,event):
    #     if self.dt_current_page>1:
    #         self.dt_current_page-=1
    #         self.load_dt_web(self.dt_jj_index,self.dt_current_page)
    #         self.update_dt_page()  # 更新dt页按钮
    #         self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
    #
    # def add_page_dt(self,event):
    #     if self.dt_current_page < self.dt_total_page:
    #         self.dt_current_page += 1
    #         self.load_dt_web(self.dt_jj_index, self.dt_current_page)
    #         self.update_dt_page()  # 更新dt页按钮
    #         self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
    #         self.kf
    #
    # def tail_page_dt(self,event):
    #     self.dt_current_page=self.dt_total_page
    #     self.load_dt_web(self.dt_jj_index, self.dt_total_page)
    #     self.update_dt_page()
    #     self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
    #
    # def first_page_dt(self,event):
    #     self.dt_current_page =1
    #     self.load_dt_web(self.dt_jj_index,1)
    #     self.update_dt_page()
    #     self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
    #
    # def goto_page_dt(self,event):
    #     if int(self.goto_Edit_dt.text())>self.dt_total_page or int(self.goto_Edit_dt)<0:
    #         self.goto_Edit_dt.setText("")
    #     else:
    #         self.dt_current_page=int(self.goto_Edit_dt.text())
    #         self.load_dt_web(self.dt_jj_index, self.dt_current_page)
    #         self.update_dt_page()
    #         self.current_page_lb_dt.setText("第{}页".format(str(self.dt_current_page)))
    #
    # def update_dt_page(self):
    #     if self.dt_total_page == 1:
    #         self.goto_widget_2.hide()
    #     else:
    #         self.goto_widget_2.show()
    #     # 对按钮显示判断
    #     if self.dt_current_page == 1 and self.dt_total_page == 1:
    #         self.isdisable_dt_page("before", True)
    #         self.isdisable_dt_page("next", True)
    #     elif self.dt_current_page == 1 and self.dt_current_page < self.dt_total_page:
    #         self.isdisable_dt_page("before", True)
    #         self.isdisable_dt_page("next", False)
    #     elif 1 < self.dt_current_page < self.dt_total_page:
    #         self.isdisable_dt_page("before", False)
    #         self.isdisable_dt_page("next", False)
    #     elif self.dt_current_page == self.dt_total_page:
    #         self.isdisable_dt_page("before", False)
    #         self.isdisable_dt_page("next", True)
    #
    # def isdisable_dt_page(self,page_name,status):
    #     if status:
    #         getattr(self, f"{page_name}_page_lb_dt").setStyleSheet("""
    #                          QLabel {
    #                         border: 1px solid gray; /* 边框宽度和颜色 */
    #                         border-radius: 10px;   /* 边框圆角 */
    #                         padding: 10px;        /* 内边距 */
    #                       color:   gray
    #                     }
    #                 """)
    #     else:
    #         getattr(self, f"{page_name}_page_lb_dt").setStyleSheet("""
    #                                       QLabel {
    #                     border: 1px solid #295792; /* 边框宽度和颜色 */
    #                     border-radius: 10px;   /* 边框圆角 */
    #                     padding: 10px;        /* 内边距 */
    #     			    color:   #295792}
    #                   QLabel:hover {
    #                  color: white;
    #                 background-color:#295792}
    #                                 }
    #                             """)
    #
    # def show_dt_widget(self):
    #     self.dt_current_page=1
    #     self.dt_list_Widget.itemClicked.connect(self.on_dtitem_clicked)
    #     self.top_dtph_widget.show()
    #     self.load_dt_web(0,1)
    #     self.dt_list_name=[]
    #     for i in range(self.dt_list_Widget.count()):
    #         item = self.dt_list_Widget.item(i)
    #         self.dt_list_name.append(item.text())  # 打印列表项的文本
    #
    # def on_dtitem_clicked(self,item):
    #     self.dt_jj_index=self.dt_list_name.index(item.text())
    #     self.load_dt_web(self.dt_jj_index,1)
    #     self.dt_current_page=1
    #
    # def load_dt_web(self,index,page):
    #     self.s = time.time()
    #     self.dt_loading_lb.setText("加载中...")
    #     # 创建并启动子线程
    #     self.worker_thread = dt_web(index=index, page=page)
    #     self.worker_thread.finished.connect(self.task_finished)
    #     self.worker_thread.start()
    #
    # def task_finished(self, web_data, page1):
    #     self.dt_total_page=page1
    #     self.e = time.time()
    #     self.textBrowser.setHtml(web_data)
    #     self.tail_page_lb_dt.setText(str(page1))
    #     self.dt_loading_lb.setText("本次加载共耗时:{:.2f}s".format(self.e-self.s))

    """top_切换widget"""
    def change_left_widget(self,event):
        if self.top_widget_index==1:
            # self.left_widget_change.setStyleSheet("""color: gray; }""")
            self.top_widget_index-=1
        # elif 0<self.top_widget_index<=3:
        #     self.left_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
        #     self.right_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.top_widget_index -= 1
        self.show_hide_top_widget(self.top_widget_index)

    def change_right_widget(self,event):
        print("向右切换")
        if self.top_widget_index==2:
            self.top_widget_index+=1
            # self.right_widget_change.setStyleSheet("""color: gray; }""")
        elif 0<=self.top_widget_index<3:
            # self.left_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            # self.right_widget_change.setStyleSheet("""QLabel:hover { color: #FF4400; }""")
            self.top_widget_index += 1
        self.show_hide_top_widget(self.top_widget_index)


    #top_widget
    def show_hide_top_widget(self,index):
        widget_list = ["tz_tools_widget", "tz_compute_widget", "zx_widget", "chatai_widget"]
        for i in range(4):
            if i == index:
                getattr(self.ui, f"{widget_list[index]}").show()
            else:
                getattr(self.ui, f"{widget_list[i]}").hide()
        match (index):
            case 0:
                print("主页")
                # self.close_calculator_widget()
            case 1:
                print("计算器")
                self.fund_calculator.open_calculator_widget()
            case 2:
                print("资讯")
                # self.close_calculator_widget()
                self.fund_information.open_infomation_widget(event=None)
            case 3:
                print("ai聊天")
                # self.close_calculator_widget()
                self.ui.chat_widget.show()
                # self.fund_ai_chat.o
        # print(self.top_widget_index)




    def handle_mouse_pressself(self,event,name,):
        self.change_table(name, event)

    def change_table(self,table_name,event):
        self.current_page=1
        self.current_page_lb.setText("1")
        self.btn_index=0
        if event.button() == Qt.MouseButton.LeftButton:
            self.index_table_status = True
            self.total_tb_widget.show()
        headers, cols, self.btn_name,self.blue_pos,self.gr_pos = table_data.get_table_data(table_name)  # 调用获取数据api 获取page_url_l ，headers，，col
        print(headers, cols, self.btn_name,self.blue_pos,self.gr_pos)
        data_l,len_data_l,self.total_page=table_data.return_index_tb(self.btn_index,self.current_page)

        data_l=self.is_limit( data_l,len_data_l)
        print(len(data_l))
        self.table_r_c = [20, 20, 20, 20,cols]
        #样式渲染的函数
        self.btn_conn()
        self.table_btn_i(data_l, self.total_page,len_data_l, cols, self.btn_index,self.blue_pos,self.gr_pos)#让每个标题的表的不同按钮绑定正确数据
        #渲染数据
        self.first_page_lb.setStyleSheet("""
                         QLabel {
                                border: 1px solid #295792; /* 边框宽度和颜色 */
                                border-radius: 10px;   /* 边框圆角 */
                                padding: 10px;        /* 内边距 */
                			  color:  white;
                			  background-color:#295792

                            }
                        """)
        self.tail_page_lb.setStyleSheet("""
                             QLabel {
                                border: 1px solid #295792; /* 边框宽度和颜色 */
                                border-radius: 10px;   /* 边框圆角 */
                                padding: 10px;        /* 内边距 */
                			  color:   #295792

                            }
                              QLabel:hover {
                             color: white; 
                            background-color:#295792}
                        """)
        for col, header in enumerate(headers): self.top_tableWidget.setHorizontalHeaderItem(col,QTableWidgetItem(header))
        def process_button(i):
            # 调用 table_btn_i 函数
            data,len_data_rr,page=table_data.return_index_tb(i-1,1)
            getattr(self, f"tb_btn_i_{i}").clicked.connect(
                partial(self.table_btn_i, self.is_limit(data,len_data_rr),page, len_data_l, self.table_r_c[-1], i - 1,
                        self.blue_pos, self.gr_pos)
            )
            self.current_page=1

        # # 使用多线程处理按钮逻辑
        with concurrent.futures.ThreadPoolExecutor() as executor:
            futures = [executor.submit(process_button, i) for i in range(1, len(self.btn_name) + 1)]
            for future in concurrent.futures.as_completed(futures):
                try:
                    future.result()  # 获取线程执行结果
                except Exception as e:
                    print(f"Error processing button: {e}")
        print("page:" + str(self.total_page))
        self.update_page()
    def is_limit(self,data, len_data_l):
        if len_data_l > 20:
            data_l = data[:20]
        else:
            data_l = data[:len_data_l]
        return data_l

    def sub_page(self, event):
        if self.current_page >1:
            self.current_page -= 1
            self.update_page()
            data_l, len_data_l, self.total_page = table_data.return_index_tb(self.btn_index,self.current_page)
            self.table_btn_i(self.is_limit(data_l,len_data_l),self.total_page, len_data_l, self.table_r_c[-1], self.btn_index,
                            self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))

    def add_page(self, event):
        if self.current_page <self.total_page:
            self.current_page += 1
            self.update_page()
            data_l, len_data_l, self.total_page = table_data.return_index_tb(self.btn_index,self.current_page)
            self.table_btn_i(self.is_limit(data_l, len_data_l), self.total_page, len_data_l, self.table_r_c[-1],
                             self.btn_index,
                             self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))
    def tail_page(self,event):
        print(self.total_page)
        self.current_page=self.total_page
        self.update_page()
        data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, self.total_page)
        self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                         self.btn_index,
                         self.blue_pos, self.gr_pos)
        self.current_page_lb.setText(str(self.current_page))
        self.tail_page_lb.setStyleSheet("""
         QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			  color:  white;
			  background-color:#295792

            }
        """)
        self.first_page_lb.setStyleSheet("""
             QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			  color:   #295792

            }
              QLabel:hover {
             color: white; 
            background-color:#295792}
        """)

    def first_page(self,event):
        self.current_page =1
        self.update_page()
        data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, 1)
        self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                         self.btn_index,
                         self.blue_pos, self.gr_pos)
        self.current_page_lb.setText(str(self.current_page))
        self.first_page_lb.setStyleSheet("""
                 QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
                        color:  white;
                        background-color:#295792
                    }
                """)
        self.tail_page_lb.setStyleSheet("""
                     QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
        			  color:   #295792

                    }
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                """)

    def goto_page(self,event):
        if int(self.goto_Edit.text()) >self.total_page or int(self.goto_Edit.text()) < 0:
            self.goto_Edit.setText("")
        else:
            self.current_page = int(self.goto_Edit.text())
            self.update_page()
            data_l, len_data_l, total_page = table_data.return_index_tb(self.btn_index, self.current_page)
            self.table_btn_i(self.is_limit(data_l, len_data_l), total_page, len_data_l, self.table_r_c[-1],
                             self.btn_index,
                             self.blue_pos, self.gr_pos)
            self.current_page_lb.setText(str(self.current_page))
            self.goto_Edit.setText("")

    def isdisable_page(self,page_name,status):
        if status:
            getattr(self,f"{page_name}_page_lb").setStyleSheet("""
                     QLabel {
                    border: 1px solid gray; /* 边框宽度和颜色 */
                    border-radius: 10px;   /* 边框圆角 */
                    padding: 10px;        /* 内边距 */
                  color:   gray
                }
            """)
        else:
            getattr(self, f"{page_name}_page_lb").setStyleSheet("""
                                  QLabel {
                border: 1px solid #295792; /* 边框宽度和颜色 */
                border-radius: 10px;   /* 边框圆角 */
                padding: 10px;        /* 内边距 */
			    color:   #295792}
              QLabel:hover {
             color: white; 
            background-color:#295792}
                            }
                        """)

    def update_page(self):
        print("pd")
        if self.total_page==1:
            self.goto_widget.hide()
        else:
            self.goto_widget.show()
        # 对按钮显示判断
        if self.current_page==1 and self.total_page==1:
            self.isdisable_page("before",True)
            self.isdisable_page("next",True)
        elif self.current_page==1 and self.current_page<self.total_page:
            self.isdisable_page("before", True)
            self.isdisable_page("next", False)
        elif 1<self .current_page<self.total_page:
            self.isdisable_page("before", False)
            self.isdisable_page("next", False)
        elif self.current_page==self.total_page:
            self.isdisable_page("before", False)
            self.isdisable_page("next", True)

    def table_btn_i(self,data,page,rows,cols,index,blue_cols,gr_cols):
        try:
            # self.current_page=1
            print("c_page"+str(self.current_page))
            self.total_page=page
            if index!=self.btn_index:
                self.current_page=1
                self.current_page_lb.setText("1")
            self.btn_index=index
            color=["#294EBC","#009900","#FF0018","black"]#蓝，绿，红
            #对表格按钮样式进行渲染
            print(rows,cols)
            self.top_tableWidget.setColumnCount(cols)
            self.top_tableWidget.setRowCount(rows)
            for row in range(rows):
                for col in range(cols):
                    item = QTableWidgetItem(f"{data[row][col]}")
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    item.setBackground(QColor('#FFFFFF')) if row%2 else item.setBackground(QColor('#F2F2F2'))
                    for i in blue_cols:
                        if col+1==i:
                            item.setForeground(QColor(color[0]))
                    for j in gr_cols:  #传入 -12 13.45  -12.0  |  -12% -
                        if col + 1 == j and isinstance(data[row][col], float):
                             if data[row][col]<0 :
                                 item.setForeground(QColor(color[1]))
                             elif data[row][col]==0:
                                 item.setForeground(QColor(color[-1]))
                             else :item.setForeground(QColor(color[2]))
                        elif col + 1 == j and isinstance(data[row][col], int):
                            item.setForeground(QColor(color[1])) if data[row][col]<0  else item.setForeground(QColor(color[2]))
                        elif col + 1 == j and isinstance(data[row][col], str) and data[row][col]=="-":
                            item.setForeground(QColor(color[-1]))
                        elif col + 1 == j and isinstance(data[row][col], str):
                            # print(data[row][col],type(data[row][col]))
                            da = float(data[row][col].replace("%",""))
                            if da < 0 :
                                item.setForeground(QColor(color[1]))
                            elif da==0:
                                item.setForeground(QColor(color[-1]))
                            else :item.setForeground(QColor(color[2]))

                    self.top_tableWidget.setItem(row, col, item)
            for i in range(1,5):
                getattr(self, f"tb_btn_i_{i}").setStyleSheet("background-color:none")
            print("kkk")
            getattr(self, f"tb_btn_i_{index+1}").setStyleSheet("""  
                        QPushButton {
                            color:white;  
                            border: 1; /* 移除边框 */  
                            background-color: qlineargradient(  
                                x1: 0, y1: 0,  
                                x2: 1, y2: 0,  
                                stop: 0 #FFB6C1, 
                                stop: 1 #8A2BE2 
                            );  
                            border-radius: 5px; /* 圆角 */  
                            padding:5px
                        }  
                    """)
            """调整列宽，使所有列平均占满表格宽度"""
            table_width = self.top_tableWidget.width()  # 获取表格宽度
            column_count = self.top_tableWidget.columnCount()  # 获取列数
            if column_count > 0:
                column_width = table_width // column_count  # 计算每列的宽度
                for col in range(column_count):
                    self.top_tableWidget.setColumnWidth(col, column_width)
            self.top_tableWidget.horizontalHeader().setStyleSheet("""
                                    QHeaderView::section {
                                        background-color: #EBF6FE;  /* 背景颜色 */
                                        color: black;             /* 字体颜色 */
                                        padding-left: 5px;
                                        padding-right: 5px;
                                    }
                                """)
            self.top_tableWidget.setStyleSheet("""
                                    QTableWidget {
                                        gridline-color:#B9D4E7;
                                        border: 1px solid #BBD4E8;  /* 边框颜色 */
                                    }
                                """)
            self.tail_page_lb.setText(str(page))
            self.update_page()
            start=(self.current_page-1)*20+1
            end=start+rows
            self.top_tableWidget.setVerticalHeaderLabels([str(i) for i in range(start,end)])
        except Exception as e:
            print(e)

    def btn_conn(self,):
        for i in range(1,5):
            btn = getattr(self, f"tb_btn_i_{i}")
            btn.show() if i-1<len(self.btn_name) else btn.hide()
        for i in range(0,len(self.btn_name)):
            getattr(self, f"tb_btn_i_{i + 1}").setText(self.btn_name[i])

    # def info_mouse_press_event(self, event):
    #     super(QLineEdit, self.info_search_textEdit).mousePressEvent(event)  # 调用父类方法，确保正常行为
    #     self.show_search_widget()
    #
    #
    # def toggle_left_hy_arrow(self,event):
    #     if self.left_hy_status==1:#当前跌幅榜
    #         self.left_bang_index += 1
    #         self.label_107.setPixmap(QPixmap(self.img_path+'/red_up.png').scaled(15, 25,))
    #         self.left_hy_status = 0
    #     elif self.left_hy_status==0:#当前涨幅榜
    #         self.left_bang_index -= 1
    #         self.label_107.setPixmap(QPixmap(self.img_path+'/green_down.png').scaled(15, 25, ))
    #         self.left_hy_status = 1
    #     self.l_center_lb.setText(self.left_table[self.left_bang_index])
    def toggle_kf_index_widget(self,event):
        if self.index_kf_status:
            self.kf_current_page = 1
            self.index_kf_status = False
            self.index_widget.show()
            self.index_top_widget.show()
            self.index_top2_widget.show()
            self.kfjj_widget.hide()
        else:
            self.show_kfjj_widget()
            # self.kfjj_widget.show()
            self.index_top_widget.hide()
            self.index_widget.hide()
            self.index_top2_widget.hide()
            self.index_kf_status = True
    def toggle_cal_widget(self,event):
        if self.kf_cal_status:
            self.kf_cal_status=False
            self.calendarWidget.hide()
        else:
            self.kf_cal_status = True
            self.calendarWidget.show()
    #
    # def load_info_detail(self,index):
    #     # self.info_index=10 * (self.load_new_n - 1) + index
    #     article_title = self.title_l[index]
    #     article_url = self.info_url[10 * (self.load_new_n - 1) + index]
    #     self.show_detail_widget(self.title_l[index], self.tip_l[index],
    #                             self.article_l[index])
    #     self.update_view_history(self.title_l[index],
    #                              self.info_url[10 * (self.load_new_n - 1) + index])
    #     self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
    #     self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
    #     self.check_collect(title=article_title, url=article_url)
    #     self.info_detail_status = True
    #
    #     self.update_info_list()
    #     self.info_detail_listWidget.itemClicked.connect(self.load_info_list_detail)
    #
    #
    # def toggle_info_detail(self,event,index):#[0-9]
    #     try:
    #         if self.info_detail_status:
    #             self.info_detail_widget.hide()
    #             self.info_detail_status=False
    #             self.load_view_history()
    #         else:
    #             self.info_detail_return_lb_2.hide()
    #             self.info_detail_return_lb_3.hide()
    #             self.info_detail_return_lb.show()
    #             self.info_detail_widget.show()
    #             self.load_info_detail(index)
    #             self.info_list_lb.setText("最新新闻")
    #     except Exception as e:
    #         print(e)
    #
    #
    # def update_collect_list(self):
    #     self.info_collect_item_list_name = []
    #     for i in range(self.info_collect_widget.count()):
    #         item = self.info_collect_widget.item(i)
    #         self.info_collect_item_list_name.append(item.text())
    #
    # def update_history_list(self):
    #     self.info_history_item_list_name = []
    #     for i in range(self.info_history_widget.count()):
    #         item = self.info_history_widget.item(i)
    #         self.info_history_item_list_name.append(item.text())
    #
    # def toggle_info_collect_list_detail(self,item):
    #     try:
    #             self.info_collect_item_index = self.info_collect_item_list_name.index(item.text())
    #             with open(r"api/widget/information/collect", "r", encoding="utf8") as f:
    #                 collect_list = f.readlines()[::-1]
    #             article_title = collect_list[self.info_collect_item_index].split("|")[0].strip()
    #             article_url = collect_list[self.info_collect_item_index].split("|")[1].replace("\n", "")
    #             a = []
    #             a.append(article_url)
    #             article_info = asyncio.run(url_infos(a).main())[0]
    #             article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #             article_content = article_info[2]
    #             self.info_detail_widget.show()
    #             self.info_detail_return_lb_2.show()
    #             self.info_detail_return_lb.hide()
    #             self.info_detail_return_lb_3.hide()
    #             self.show_detail_widget(article_title,article_tip,article_content)
    #             # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
    #             self.check_collect(title=article_title,url=article_url)
    #             self.info_detail_status_2 = True
    #             self.info_list_lb.setText("全部收藏")
    #             self.update_detail_collect_list()
    #             # self.update_detail_viewed_list()
    #             self.info_detail_listWidget.itemClicked.connect(self.load_collect_list_detail)
    #             self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
    #             self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def toggle_info_history_list_detail(self,item):
    #     try:
    #         self.info_history_item_index = self.info_history_item_list_name.index(item.text())
    #         with open(r"api/widget/information/view_history", "r", encoding="utf8") as f:
    #             collect_list = f.readlines()[::-1]
    #         article_title = collect_list[self.info_history_item_index].split("|")[0].strip()
    #         article_url = collect_list[self.info_history_item_index].split("|")[1].replace("\n", "")
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.info_detail_widget.show()
    #         self.info_detail_return_lb_3.show()
    #         self.info_detail_return_lb_2.hide()
    #         self.info_detail_return_lb.hide()
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         # self.update_view_history(article_title,self.info_url[10*self.load_new_n+self.info_index])
    #         self.check_collect(title=article_title,url=article_url)
    #         self.info_collect_lb_3.mousePressEvent = partial(self.open_url, url=article_url)
    #         self.info_collect_lb_2.mousePressEvent = partial(self.copy_to_clipboard, url=article_url)
    #         self.update_collect_list()
    #         self.info_detail_status_3 = True
    #         self.info_list_lb.setText("全部浏览历史")
    #         self.update_detail_viewed_list()
    #         self.info_detail_listWidget.itemClicked.connect(self.load_viewed_list_detail)
    #     except Exception as e:
    #         print(e)
    #
    # def load_info_list_detail(self,item):#从首页进入文章详情页面的
    #     try:
    #         self.info_item_index = self.info_title_l.index(item.text())
    #         article_title = self.info_title_l[self.info_item_index].strip()
    #         article_url = self.info_url[self.info_item_index].strip()
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         self.check_collect(title=article_title,url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def load_viewed_list_detail(self,item):#从首页进入文章详情页面的
    #     try:
    #         self.info_item_index = self.view_history_title.index(item.text())
    #         article_title = self.view_history_title[self.info_item_index].strip()
    #         article_url = self.view_history_url[self.info_item_index].strip()
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         self.check_collect(title=article_title,url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def load_collect_list_detail(self,item):#从首页进入文章详情页面的
    #     try:
    #         self.info_item_index = self.collect_title.index(item.text())
    #         article_title = self.collect_title[self.info_item_index].strip()
    #         article_url = self.collect_url[self.info_item_index].strip()
    #         a = []
    #         a.append(article_url)
    #         article_info = asyncio.run(url_infos(a).main())[0]
    #         article_tip = "发布时间： " + article_info[0] + "  来源： " + article_info[1]
    #         article_content = article_info[2]
    #         self.show_detail_widget(article_title,article_tip,article_content)
    #         self.check_collect(title=article_title,url=article_url)
    #     except Exception as e:
    #         print(e)
    #
    # def update_info_list(self):#刷新最新新闻列表项
    #     self.info_detail_listWidget.clear()
    #     for i in range(len(self.info_title_l)):
    #         self.info_detail_listWidget.addItem(self.info_title_l[i])
    #
    # def update_detail_viewed_list(self):#刷新最新新闻列表项
    #     self.info_detail_listWidget.clear()
    #     with open(r"api/widget/information\view_history", "r", encoding="utf8") as f:
    #         view_history = f.readlines()
    #     self.view_history_title=[i.split("|")[0] for i in view_history]
    #     self.view_history_url=[i.split("|")[1] for i in view_history]
    #     for i in view_history [::-1]:
    #         self.info_detail_listWidget.addItem(i.split("|")[0])
    #
    # def update_detail_collect_list(self):  # 刷新最新新闻列表项
    #     self.info_detail_listWidget.clear()
    #     with open(r"api/widget/information\collect", "r", encoding="utf8") as f:
    #         collect_ = f.readlines()
    #     self.collect_title = [i.split("|")[0] for i in collect_]
    #     self.collect_url = [i.split("|")[1] for i in collect_]
    #     for i in collect_[::-1]:
    #         self.info_detail_listWidget.addItem(i.split("|")[0])
    #
    # def hide_info_detail(self,event):
    #     self.info_detail_widget.hide()
    #     self.info_detail_status_2 = False
    #     self.load_view_history()
    #
    # def hide_info_detail_his(self,event):
    #     self.info_detail_widget.hide()
    #     self.info_detail_status_3 = False
    #     self.load_view_history()
    #
    # def info_move_to(self,status):
    #     if status:
    #         self.scrollArea_3.move(30, 210)
    #         self.info_load_lb.move(1370,890)
    #         self.info_up_lb.move(1365,830)
    #         self.info_up_img.move(1370,790)
    #         self.info_search_textEdit.move(380, 80)
    #         self.info_search_btn.move(900, 81)
    #         self.info_search_widget.move(380,121)
    #
    #     else:
    #         self.scrollArea_3.move(200, 210)
    #         self.info_load_lb.move(1540, 890)
    #         self.info_up_lb.move(1535, 830)
    #         self.info_up_img.move(1540, 790)
    #         self.info_search_textEdit.move(550,80)
    #         self.info_search_btn.move(1070,81)
    #         self.info_search_widget.move(550,121)
    #
    # def update_view_history(self,title,url):
    #     with open(r"api/widget/information\view_history", "r", encoding="utf8")as f:
    #         view_history=f.readlines()
    #     with open(r"api/widget/information\view_history", "a+",
    #               encoding="utf8") as f:
    #         data=title+"|"+url+"\n"
    #         if data not in set(view_history):
    #             f.write(title+"|"+url+"\n")
    #         f.close()
    #
    #     # self.load_view_history()
    #
    # def load_view_history(self):
    #     self.info_history_widget.clear()
    #     with open(r"api/widget/information\view_history", "r", encoding="utf8")as f:
    #         self.view_history=f.readlines()
    #         f.close()
    #     for i in self.view_history[::-1]:
    #         self.info_history_widget.addItem(i.split("|")[0])
    #     self.update_history_list()
    #
    # def toggle_info_history_list(self,event):
    #     if self.info_history_status:
    #         self.info_history_status = False
    #         self.info_history_widget.hide()
    #         # self.info_collect_widget.hide()
    #         self.info_move_to(False)
    #     else:
    #         self.info_collect_status = False
    #         self.info_history_status = True
    #         self.info_history_widget.show()
    #         self.info_collect_widget.hide()
    #         self.info_move_to(True)
    #         self.load_view_history()
    #
    # def toggle_info_collect_list(self, event):
    #     if self.info_collect_status:
    #         self.info_collect_status = False
    #         # self.info_history_widget.hide()
    #         self.info_collect_widget.hide()
    #         self.info_move_to(False)
    #     else:
    #         self.info_collect_status = True
    #         self.info_history_status = False
    #         self.info_collect_widget.show()
    #         self.info_history_widget.hide()
    #         self.info_move_to(True)
    #         self.load_collect()


    # toggle_list_news_widget = toggle_list_news_widget
    # toggle_table_index_widget = toggle_table_index_widget
    # toggle_dt_index_widget = toggle_dt_index_widget
    # toggle_send_stop = toggle_send_stop
    # thread_finished = thread_finished
    #
    # change_chat_model = change_chat_model
    # clear_context = clear_context
    # send_question = send_question
    # stop_response = stop_response
    # add_user_message = add_user_message
    # add_ai_message_stream = add_ai_message_stream
    # toggle_send_stop_img = toggle_send_stop_img
    #
