from functools import partial
from PyQt6.QtWidgets import QWidget, QVBoxLayout, QHBoxLayout, QPushButton
from app.modules.FundFilter.DataHandle_filter import DataHandle_filter
from app.modules.FundFilter.src import style

import sys
from PyQt6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QScrollArea,
    QGridLayout, QLabel, QListWidget
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QColor, QPalette

class ClickableLabel(QLabel):
    clicked = pyqtSignal()

    def __init__(self, text, parent=None):
        super().__init__(text, parent)
        self.setAlignment(Qt.AlignmentFlag.AlignCenter)
        # self.setWordWrap(True)
        self.setStyleSheet("""
             QLabel {
             padding: 4px;
                    border: 2px solid #D8D8D8;
                    border-radius: 4px;
                }
        """)
        self.original_palette = self.palette()
        self.is_highlighted = False

    def mousePressEvent(self, event):
        self.clicked.emit()
        super().mousePressEvent(event)

    def set_highlighted(self, highlighted):
        self.is_highlighted = highlighted
        if highlighted:
            palette = self.palette()
            palette.setColor(QPalette.ColorRole.Window, QColor(255, 255, 0))  # 黄色高亮
            palette.setColor(QPalette.ColorRole.WindowText, QColor(0, 0, 0))
            self.setPalette(palette)
            self.setStyleSheet("""
                QLabel {
                padding: 4px;
                    border: 0;
                    border-radius: 4px;
                    background-color: #1890FF;
                    color:white;
                }
            """)
        else:
            self.setPalette(self.original_palette)
            self.setStyleSheet("""
                QLabel {
                    padding: 4px;
                    border: 2px solid #D8D8D8;
                    border-radius: 4px;
                }
            """)
class filter_more:
    def __init__(self, ui):
        self.ui = ui
        self.init_filter_more()

    def init_filter_more(self):
        self.filter_more_list = []
        self.selected_sectors = []
        self.labels = []  # 存储所有QLabel的引用
        self.ui.filter_tp_tip.mousePressEvent = lambda event:self.ui.filter_more_widget_1.hide()

        self.filter_rt_total_list = []
        self.filter_rt_total_dict= {"cx": 0, "sz": 0, "zs":0, "ja": 0}
        self.ui.filter_rt_listWidget_1.itemClicked.connect(self.rt_cx_select)
        self.ui.filter_rt_listWidget_2.itemClicked.connect(self.rt_sz_select)
        self.ui.filter_rt_listWidget_3.itemClicked.connect(self.rt_zs_select)
        self.ui.filter_rt_listWidget_4.itemClicked.connect(self.rt_ja_select)

        self.filter_se=0
        self.filter_nx=0
        self.ui.filter_se_listWidget.itemClicked.connect(self.se_select)
        self.ui.filter_nx_listWidget.itemClicked.connect(self.nx_select)

        self.ui.filter_more_tip.mousePressEvent =self.hide_filter_more

        for i in range(1, 5):
            getattr(self.ui, f"filter_more_{i}").setStyleSheet(style.QLabel_ft_normal)
            getattr(self.ui, f"filter_more_{i}").mousePressEvent = partial(self.filter_more_clicked, index=i)
        for i in range(1, 5):
            getattr(self.ui, f"filter_more_widget_{i}").hide()

    def hide_filter_more(self,event):
        for i in range(1, 5):
            getattr(self.ui, f"filter_more_widget_{i}").hide()

    def filter_more_clicked(self,event,index):
        for i in range(1, 5):
            getattr(self.ui, f"filter_more_widget_{i}").hide()
        match (index):
            case 1:
                self.bk_list = list(DataHandle_filter.get_bk_list().keys())
                self.sectors = self.bk_list
                self.show_bk_widget()
            case 2:
                self.init_rt_widget()
            case 3:
                self.init_se_select()
            case 4:
                self.init_nx_select()

        getattr(self.ui, f"filter_more_widget_{index}").show()
        print(set(self.filter_more_list))
        for i in range(1, 5):
            if i in set(self.filter_more_list):
                getattr(self.ui, f"filter_more_{i}").setStyleSheet(style.QLabel_ft_selected)
            else:
                getattr(self.ui, f"filter_more_{i}").setStyleSheet(style.QLabel_ft_normal)

    """
    基金板块选择
    """
    def show_bk_widget(self):
        scroll_widget = QWidget()
        scroll_layout = QGridLayout(scroll_widget)
        scroll_layout.setSpacing(5)
        scroll_layout.setContentsMargins(5, 5, 5, 5)
        cols = 10
        rows = (len(self.sectors) + cols - 1) // cols

        # 添加QLabel到网格布局
        for i, sector in enumerate(self.sectors):
            row = i // cols
            col = i % cols
            label = ClickableLabel(sector)
            if sector in self.selected_sectors:
                label.set_highlighted(True)
            label.clicked.connect(self.on_label_clicked)
            scroll_layout.addWidget(label, row, col)
            self.labels.append(label)

        # 设置滚动区域的widget
        self.ui.filter_tp_ScrollArea.setWidget(scroll_widget)

    def on_label_clicked(self):
        try:
            label = self.ui.filter_more_widget_1.sender()
            sector = label.text()

            if label.is_highlighted:
                label.set_highlighted(False)
                if sector in self.selected_sectors:
                    self.selected_sectors.remove(sector)
            else:
                # 如果未高亮，则高亮并添加到选中列表
                label.set_highlighted(True)
                if sector not in self.selected_sectors:
                    self.selected_sectors.append(sector)
            print(self.selected_sectors)
            result = DataHandle_filter.return_all_filter_data(orign_text=self.ui.filter_all_lb.toPlainText(),new_data= self.selected_sectors,type= "板块")
            self.ui.filter_all_lb.setPlainText(result)
            self.update_bk_status()
        except Exception as e:
            print(f"on_label_clicked:{e}")

    def update_bk_status(self):
        if len(self.selected_sectors) > 0:
            self.ui.filter_more_1.setStyleSheet(style.QLabel_ft_selected)
            self.append_more_list(1)
        else:
            self.ui.filter_more_1.setStyleSheet(style.QLabel_ft_normal)
            self.remove_more_list(1)

    #基金业绩
    def init_rt_widget(self):
        try:
            for i in range(1,5):
                print(list(self.filter_rt_total_dict.keys())[i-1])
                getattr(self.ui,f"filter_rt_listWidget_{i}").setCurrentRow(self.filter_rt_total_dict[list(self.filter_rt_total_dict.keys())[i-1]])
        except Exception as e:
            print(f"init_rt_widget:{e}")

    def select_to_index(self,text,type):
        match (type):
            case "rt":
                l=["不限","★★★★★","★★★★","★★★","★★","★"]
            case "se":
                l=["不限","≤2亿元","≤10亿元","≤50亿元","≤100亿元",">100亿元",]
            case "nx":
                l=["不限","≤1年","≤2年","≤3年","≤5年","≤7年",">7年"]
        print(l.index(text))
        return l.index(text)

    def update_rt_select_style(self):
        if list(self.filter_rt_total_dict.values()).count(0)==4:
            self.ui.filter_more_2.setStyleSheet(style.QLabel_ft_normal)
            self.remove_more_list(2)
        else:
            self.append_more_list(2)
            self.ui.filter_more_2.setStyleSheet(style.QLabel_ft_selected)

    def rt_cx_select(self,item):
        try:
            text = item.text()
            self.filter_rt_total_dict["cx"] = self.select_to_index(text,"rt")
            self.update_rt_select_style()
            data=self.trans_rt_data()
            result = DataHandle_filter.return_all_filter_data(orign_text=self.ui.filter_all_lb.toPlainText(),new_data= data,type= "评级")
            self.ui.filter_all_lb.setPlainText(result)
        except Exception as e:
            print(f"rt_cx_select:{e}")
    def rt_sz_select(self,item):
        text = item.text()
        self.filter_rt_total_dict["sz"] = self.select_to_index(text,"rt")
        self.update_rt_select_style()
        data = self.trans_rt_data()
        result = DataHandle_filter.return_all_filter_data(orign_text=self.ui.filter_all_lb.toPlainText(), new_data=data,
                                                          type="评级")
        self.ui.filter_all_lb.setPlainText(result)

    def rt_zs_select(self,item):
        text = item.text()
        self.filter_rt_total_dict["zs"] = self.select_to_index(text,"rt")
        self.update_rt_select_style()
        data = self.trans_rt_data()
        result = DataHandle_filter.return_all_filter_data(orign_text=self.ui.filter_all_lb.toPlainText(), new_data=data,
                                                          type="评级")
        self.ui.filter_all_lb.setPlainText(result)

    def rt_ja_select(self,item):
        text = item.text()
        self.filter_rt_total_dict["ja"] = self.select_to_index(text,"rt")
        self.update_rt_select_style()
        data = self.trans_rt_data()
        result = DataHandle_filter.return_all_filter_data(orign_text=self.ui.filter_all_lb.toPlainText(), new_data=data,
                                                          type="评级")
        self.ui.filter_all_lb.setPlainText(result)

    def init_se_select(self):
        self.ui.filter_se_listWidget.setCurrentRow(self.filter_se)
    def init_nx_select(self):
        self.ui.filter_se_listWidget.setCurrentRow(self.filter_nx)

    def update_se_select_style(self):
        if self.filter_se==0:
            self.ui.filter_more_3.setStyleSheet(style.QLabel_ft_normal)
            self.remove_more_list(3)
        else:
            self.append_more_list(3)
            self.ui.filter_more_3.setStyleSheet(style.QLabel_ft_selected)

    def update_nx_select_style(self):
        if self.filter_nx==0:
            self.ui.filter_more_4.setStyleSheet(style.QLabel_ft_normal)

            self.remove_more_list(4)
        else:
            self.append_more_list(4)
            self.ui.filter_more_4.setStyleSheet(style.QLabel_ft_selected)

    def remove_more_list(self,index):
        if index in self.filter_more_list:
            self.filter_more_list.remove(index)

    def append_more_list(self,index):
        if index not in self.filter_more_list:
            self.filter_more_list.append(index)
    def se_select(self,item):
        text = item.text()
        self.filter_se = self.select_to_index(text,"se")
        self.update_se_select_style()
        result = DataHandle_filter.return_all_filter_data(orign_text=self.ui.filter_all_lb.toPlainText(), new_data=self.trans_se_data(),
                                                          type="规模")
        self.ui.filter_all_lb.setPlainText(result)

    def nx_select(self,item):
        try:
            text = item.text()
            self.filter_nx = self.select_to_index(text, "nx")
            self.update_nx_select_style()
            result = DataHandle_filter.return_all_filter_data(orign_text=self.ui.filter_all_lb.toPlainText(),
                                                              new_data=self.trans_nx_data(),
                                                              type="年限")
            self.ui.filter_all_lb.setPlainText(result)
        except Exception as e:
            print(f"nx_select:{e}")

    def return_more_total_data(self):
        try:
        #板块数据
            result_tp=DataHandle_filter.return_bk_code_list(self.selected_sectors)
            result_rt=self._rt_data()
            #基金规模
            result_se=self.filter_se
            #成立年限
            result_nx=self.filter_nx
            print(result_tp, result_rt, result_se, result_nx)
            return result_tp,result_rt,result_se,result_nx
        except Exception as e:
            print(f"return_more_total_data error:{e}")

    def _rt_data(self):
        # 机构评级
        result_rt = [[], []]
        for k in list(self.filter_rt_total_dict.keys()):
            if self.filter_rt_total_dict[k] == 0:
                continue
            else:
                result_rt[0].append(list(self.filter_rt_total_dict.keys()).index(k))
                result_rt[1].append(self.filter_rt_total_dict[k])
        return result_rt

    def trans_rt_data(self):
        pj_type = ["晨星评级", "上证评级", "招商评级", "济安评级"]
        star_list=["","五星","四星","三星","二星","一星"]
        pj,star = self._rt_data()
        result = [[pj_type[i] for i in pj],[star_list[j] for j in star]]
        return result

    def trans_se_data(self):
        l = ["", "≤2亿元", "≤10亿元", "≤50亿元", "≤100亿元", ">100亿元", ]
        result = l[self.filter_se]
        return result

    def trans_nx_data(self):
        l = ["", "≤1年", "≤2年", "≤3年", "≤5年", "≤7年", ">7年"]
        result = l[self.filter_nx]
        return result

    def clear_all_selections(self):
        self.filter_rt_total_list = []
        self.filter_rt_total_dict = {"cx": 0, "sz": 0, "zs": 0, "ja": 0}
        self.filter_more_list = []
        self.selected_sectors = []
        self.filter_se = 0
        self.filter_nx = 0
        self.update_rt_select_style()
        self.update_se_select_style()
        self.update_nx_select_style()
        self.update_bk_status()



















