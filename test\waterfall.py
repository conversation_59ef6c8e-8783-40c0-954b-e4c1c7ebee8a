import sys
from PyQt6.QtWidgets import (
    QApplication, QWidget, QVBoxLayout, QScrollArea, QLabel, QPushButton
)
from PyQt6.QtCore import Qt

# 自定义新闻 Widget
class NewsWidget(QWidget):
    def __init__(self, title, content, parent=None):
        super().__init__(parent)
        self.setFixedHeight(100)  # 设置固定高度
        layout = QVBoxLayout()
        self.title_label = QLabel(f"<b>{title}</b>")
        self.content_label = QLabel(content)
        layout.addWidget(self.title_label)
        layout.addWidget(self.content_label)
        self.setLayout(layout)
        self.setStyleSheet("background-color: #f0f0f0; border: 1px solid #ccc; padding: 10px;")

# 主窗口
class MainWindow(QWidget):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("无感瀑布流")
        self.setGeometry(100, 100, 400, 600)

        # 主布局
        self.main_layout = QVBoxLayout(self)

        # 滚动区域
        self.scroll_area = QScrollArea(self)
        self.scroll_area.setWidgetResizable(True)
        self.scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarPolicy.ScrollBarAlwaysOn)

        # 滚动区域的内容 Widget
        self.scroll_content = QWidget()
        self.scroll_layout = QVBoxLayout(self.scroll_content)
        self.scroll_layout.setAlignment(Qt.AlignmentFlag.AlignTop)
        self.scroll_area.setWidget(self.scroll_content)

        # 添加到主布局
        self.main_layout.addWidget(self.scroll_area)

        # 初始化新闻列表
        self.news_list = []
        self.load_news(5)  # 默认加载 5 条新闻

        # 监听滚动条事件
        self.scroll_area.verticalScrollBar().valueChanged.connect(self.on_scroll)

    def load_news(self, count):
        """加载指定数量的新闻"""
        for i in range(count):
            title = f"新闻标题 {len(self.news_list) + 1}"
            content = f"这是新闻内容 {len(self.news_list) + 1}，这是一条测试新闻。"
            news_widget = NewsWidget(title, content)
            self.scroll_layout.addWidget(news_widget)
            # self.news_list.append(news_widget)

    def on_scroll(self):
        """滚动条事件处理"""
        scroll_bar = self.scroll_area.verticalScrollBar()
        if scroll_bar.value() == scroll_bar.maximum():  # 滚动到底部
            self.load_news(5)  # 加载更多新闻

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec())