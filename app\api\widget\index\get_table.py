class left_table:
    def __init__(self):
        #获取并处理涨幅榜，跌幅榜数据
        self.page_url_l = ["https://fund.eastmoney.com/ZS_jzzzl.html#os_0;isall_0;ft_;pt_5",
                            "https://fund.eastmoney.com/GP_jzzzl.html#os_0;isall_0;ft_;pt_2",
                            "https://fund.eastmoney.com/HH_jzzzl.html#os_0;isall_0;ft_;pt_3"]
        headers = ['排名', '板块名称', '涨跌额', '涨跌幅', '上涨家数', '领涨股票']
        data_l = ['test1', 'test2', 'test3']
        cols = len(headers)
        left_t=[data_l[0], 10, cols, 0]
        # return v




