from app.api.other.wrapp import calculate_time
from app.modules.FundKFJJ.api.get_kfjj_data import kfjj_data
from app.modules.FundKFJJ.api.get_kfjj_data_2 import kfjj_data_params
from app.modules.FundKFJJ.api.kfjj_ import CustomDelegate
from main_w import Ui_MainWindow
import asyncio
import time
from datetime import datetime, date, timedelta
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl, QEvent, QEasingCurve, QPropertyAnimation
from PyQt6.QtGui import QPixmap, QDesktopServices, QStandardItemModel, QStandardItem
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit, QApplication, \
    QGraphicsDropShadowEffect

class FundKFJJ():
    def __init__(self, ui:Ui_MainWindow):
        self.ui = ui
        self.init_setup()

    def init_setup(self):
        self.img_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\img"
        # self.top_img_label.
        # self.ui.kf_sort_check_2.hide()
        self.index_kf_status=False
        self.kf_cal_status=False
        self.kf_sort = "desc"
        self.kf_jj_index=0#开放基金top索引
        self.kf_jj_sub_index=0#开放基金对应子索引
        self.ui.calendarWidget.clicked.connect(self.on_calendar_clicked)
        self.ui.kfjj_widget.hide()
        self.ui.kf_qdii_tab_widget.hide()
        self.ui.kf_zs_tab_widget.hide()
        self.ui.kf_zq_tab_widget.hide()
        self.ui.calendarWidget.hide()
        self.start_date_c=None
        self.end_date_c=None
        self.ui.top_img_1.mousePressEvent = self.toggle_kf_index_widget
        self.ui.top_img_label.mousePressEvent = self.toggle_kf_index_widget
        self.ui.kfjj_retuen_lb.mousePressEvent = self.toggle_kf_index_widget
        for i in range(1,8):
            getattr(self.ui, f"kf_top_lb_{i}").clicked.connect(partial(self.load_kfjj_widget, i - 1))
        self.ui.before_page_lb_kf.mousePressEvent = self.sub_page_kf
        self.ui.next_page_lb_kf.mousePressEvent = self.add_page_kf
        self.ui.tail_page_lb_kf.mousePressEvent = self.tail_page_kf
        self.ui.first_page_lb_kf.mousePressEvent = self.first_page_kf
        self.ui.goto_page_lb_kf.mousePressEvent = self.goto_page_kf
        self.ui.pushButton.clicked.connect(self.kf_query_cal)
        self.ui.kf_cal_img.setPixmap(QPixmap(self.img_path + r'\cal1.png').scaled(38, 42))
        self.ui.kf_cal_img.mousePressEvent=self.toggle_cal_widget
        
    def toggle_kf_index_widget(self,event):
        if self.index_kf_status:
            self.kf_current_page = 1
            self.index_kf_status = False
            self.ui.index_widget.show()
            self.ui.index_top_widget.show()
            self.ui.index_top2_widget.show()
            self.ui.kfjj_widget.hide()
        else:
            self.show_kfjj_widget()
            self.ui.kfjj_widget.show()
            self.ui.index_top_widget.hide()
            self.ui.index_widget.hide()
            self.ui.index_top2_widget.hide()
            self.index_kf_status = True
    def toggle_cal_widget(self,event):
        if self.kf_cal_status:
            self.kf_cal_status=False
            self.ui.calendarWidget.hide()
        else:
            self.kf_cal_status = True
            self.ui.calendarWidget.show()
        
    def on_calendar_clicked(self, date: QDate):
        if self.start_date_c is None:
            self.start_date_c = date
            self.ui.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}")
        # 如果起始日期已设置且结束日期未设置，则设置为当前点击的日期
        elif self.end_date_c is None:
            if date >= self.start_date_c:  # 验证结束日期必须大于或等于起始日期
                self.end_date_c = date
                self.ui.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}, 结束日期: {self.end_date_c.toString('yyyy-MM-dd')}")
            else:
                QMessageBox.warning(self.ui.kfjj_widget, "错误", "结束日期必须大于或等于起始日期！")
        # 如果起始日期和结束日期都已设置，则重置起始日期和结束日期
        else:
            self.start_date_c = date
            self.end_date_c = None
            self.ui.label_8.setText(f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}")

        
    def kf_query_cal(self, event):
        if self.start_date_c is not None and self.end_date_c is not None:
            print(
                f"起始日期: {self.start_date_c.toString('yyyy-MM-dd')}, 结束日期: {self.end_date_c.toString('yyyy-MM-dd')}")
            self.start_date = self.start_date_c.toString('yyyy-MM-dd')
            self.end_date = self.end_date_c.toString('yyyy-MM-dd')
            self.kf_current_page = 1
            self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
        else:
            print("请选择起始日期和结束日期！")


    def on_checkbox_state_changed(self, state):
        try:
            if state == Qt.CheckState.Checked.value:
                self.kf_sort = "asc"
            else:
                self.kf_sort = "desc"
            self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)

        except Exception as e:
            print(f"on_checkbox_state_changed  error:{e}")


    def sub_page_kf(self, event):
        if self.kf_current_page > 1:
            self.kf_current_page -= 1
            self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
            self.update_kf_page()
            self.ui.current_page_lb_dt.setText("第{}页".format(str(self.kf_current_page)))


    def add_page_kf(self, event):
        if self.kf_current_page < self.kf_total_page:
            self.kf_current_page += 1
            self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
            self.update_kf_page()
            self.ui.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def tail_page_kf(self, event):
        self.kf_current_page = self.kf_total_page
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
        self.update_kf_page()
        self.ui.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def first_page_kf(self, event):
        self.kf_current_page = 1
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                 end_date=self.end_date)
        self.update_kf_page()
        self.ui.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def goto_page_kf(self, event):
        if int(self.ui.goto_Edit_kf.text()) > self.kf_total_page or int(self.ui.goto_Edit_kf.text()) < 0:
            self.ui.goto_Edit_kf.setText("")
        else:
            self.kf_current_page = int(self.ui.goto_Edit_kf.text())
            self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                     page=self.kf_current_page,
                                     sort_dep=self.sort_dep_index, sort=self.kf_sort, start_date=self.start_date,
                                     end_date=self.end_date)
            self.update_kf_page()
            self.ui.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def update_kf_page(self):
        if self.kf_total_page == 1:
            self.ui.goto_widget_3.hide()
        else:
            self.ui.goto_widget_3.show()
        # 对按钮显示判断
        if self.kf_current_page == 1 and self.kf_total_page == 1:
            self.isdisable_kf_page("before", True)
            self.isdisable_kf_page("next", True)
        elif self.kf_current_page == 1 and self.kf_current_page < self.kf_total_page:
            self.isdisable_kf_page("before", True)
            self.isdisable_kf_page("next", False)
        elif 1 < self.kf_current_page < self.kf_total_page:
            self.isdisable_kf_page("before", False)
            self.isdisable_kf_page("next", False)
        elif self.kf_current_page == self.kf_total_page:
            self.isdisable_kf_page("before", False)
            self.isdisable_kf_page("next", True)
        self.ui.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def isdisable_kf_page(self, page_name, status):
        if status:
            getattr(self.ui, f"{page_name}_page_lb_kf").setStyleSheet("""
                             QLabel {
                            border: 1px solid gray; /* 边框宽度和颜色 */
                            border-radius: 10px;   /* 边框圆角 */
                            padding: 10px;        /* 内边距 */
                          color:   gray
                        }
                    """)
        else:
            getattr(self.ui, f"{page_name}_page_lb_kf").setStyleSheet("""
                                          QLabel {
                        border: 1px solid #295792; /* 边框宽度和颜色 */
                        border-radius: 10px;   /* 边框圆角 */
                        padding: 10px;        /* 内边距 */
                        color:   #295792}
                      QLabel:hover {
                     color: white; 
                    background-color:#295792}
                                    }
                                """)


    @calculate_time
    def show_kfjj_widget(self):
        # default
        today = date.today()
        last_year_today = today - timedelta(days=365)
        last_year_today = last_year_today.strftime("%Y-%m-%d")
        # self.ui.kf_sort_check.stateChanged.connect(self.on_checkbox_state_changed)
        self.kf_current_page = 1
        self.sort_dep_index = 11  # 近一年
        self.tab_index = 0
        self.start_date = last_year_today
        self.end_date = today.strftime("%Y-%m-%d")

        # 对第一个网页内容提取，渲染顶部按钮，以及页面
        # 对所有顶部按钮多线程加载绑定数据
        self.bl_kf_pos, self.gr_kf_pos, self.top_lb, self.tb_header_lb = kfjj_data.get_kj_type_info()
        data, data_info = kfjj_data.return_kf_data_1(self.kf_jj_index)
        self.kf_total_page = data_info["allPages"]
        kf_top_info = [data_info["allRecords"], data_info["zs_count"], data_info["gp_count"], data_info["hh_count"],
                       data_info["zq_count"], data_info["qdii_count"], data_info["fof_count"]]
        for i in range(1, 8):
            getattr(self.ui, f"kf_top_lb_{i}").setText(f"{self.top_lb[i - 1]}({str(kf_top_info[i - 1])})")
        self.ui.kfjj_widget.show()
        self.kfjj_tableview_style(self.kf_jj_index, data, data_info)
        self.ui.kf_loading_lb.setText("加载完成")
        self.ui.tableView.horizontalHeader().sectionClicked.connect(self.kf_header_clicked)  # 监听表头点击事件
        self.ui.label_8.setText(f"起始日期: {self.start_date}, 结束日期: {self.end_date}")


    def load_kfjj_widget(self, index1):
        s = time.time()
        data, data_info = kfjj_data.return_kf_data_1(index1)
        self.kfjj_tableview_style(index1, data, data_info)
        self.kf_jj_sub_index = 0
        self.ui.kf_loading_lb.setText("加载耗时:" + "{:.2f}s".format(time.time() - s))
        self.kf_current_page = 1
        self.ui.current_page_lb_kf.setText("第{}页".format(str(self.kf_current_page)))


    def kf_header_clicked(self, index):  # 排序依据
        self.sort_dep_index = index  # 近一年
        self.kf_current_page = 1
        self.load_kf_data_params(event=None, index=self.kf_jj_index, tab_index=self.kf_jj_sub_index,
                                 page=self.kf_current_page,
                                 sort_dep=index, sort=self.kf_sort, start_date=self.start_date, end_date=self.end_date)
        self.update_kf_page()


    def load_kf_data_params(self, event, index, tab_index, page, sort_dep, sort, start_date, end_date):
        self.st = time.time()
        self.ui.kf_loading_lb.setText("加载中...")
        self.kf_jj_sub_index = tab_index
        self.kf_jj_index = index
        self.kf_current_page = page
        self.sort_dep_index = sort_dep
        # self.ui.kf_sort_check.stateChanged.connect(self.on_checkbox_state_changed)
        print(self.kf_sort)
        # self.start_date=start_date
        # self.end_date=end_date
        # 创建并启动子线程
        self.worker_thread_kf = kfjj_data_params(index=index, tab_index=tab_index, page=page, sort_dep=sort_dep, sort=sort,
                                                 start_date=start_date, end_date=end_date)
        self.worker_thread_kf.finished.connect(self.task_finished_kf)
        self.worker_thread_kf.start()


    def load_kf_data_params_pre(self, event, index, tab_index, page, sort_dep, sort, start_date, end_date):
        self.kf_jj_sub_index = tab_index
        self.kf_current_page = 1
        self.load_kf_data_params(event=None, index=index, tab_index=tab_index, page=page, sort_dep=sort_dep, sort=sort,
                                 start_date=start_date, end_date=end_date)


    def task_finished_kf(self, data, data_info):
        self.kf_total_page = data_info["allPages"]
        self.et = time.time()
        self.kfjj_tableview_style(self.kf_jj_index, data, data_info)
        self.ui.kf_loading_lb.setText("本次加载共耗时:{:.2f}s".format(self.et - self.st))


    def select_kfjj_btn(self, index1):
        self.kf_jj_index = index1
        if index1 == 1:
            self.ui.kf_qdii_tab_widget.hide()
            self.ui.kf_zq_tab_widget.hide()
            self.ui.kf_zs_tab_widget.show()

            for i in range(1, 9):
                getattr(self.ui, f"kf_zs_tab_{i}").setStyleSheet("background-color:none")

            getattr(self.ui, f"kf_zs_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        elif index1 == 4:
            self.ui.kf_qdii_tab_widget.hide()
            self.ui.kf_zs_tab_widget.hide()
            self.ui.kf_zq_tab_widget.show()

            for i in range(1, 7):
                getattr(self.ui, f"kf_zq_tab_{i}").setStyleSheet("background-color:none")
            getattr(self.ui, f"kf_zq_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        elif index1 == 5:
            self.ui.kf_zs_tab_widget.hide()
            self.ui.kf_zq_tab_widget.hide()
            self.ui.kf_qdii_tab_widget.show()

            for i in range(1, 9):
                getattr(self.ui, f"kf_qdii_tab_{i}").setStyleSheet("background-color:none")
            getattr(self.ui, f"kf_qdii_tab_{self.kf_jj_sub_index + 1}").setStyleSheet("""
                           QLabel {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #6495ED, 
                        stop: 1 #00FA9A
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
                    """)
            self.update_kf_page()
        else:
            self.ui.kf_zs_tab_widget.hide()
            self.ui.kf_zq_tab_widget.hide()
            self.ui.kf_qdii_tab_widget.hide()
        for i in range(1, 8):
            getattr(self.ui, f"kf_top_lb_{i}").setStyleSheet("background-color:none")
        getattr(self.ui, f"kf_top_lb_{index1 + 1}").setStyleSheet("""
                QPushButton {
                    color:white;  
                    border: 1; /* 移除边框 */  
                    background-color: qlineargradient(  
                        x1: 0, y1: 0,  
                        x2: 1, y2: 0,  
                        stop: 0 #FFB6C1, 
                        stop: 1 #8A2BE2 
                    );  
                    border-radius: 5px; /* 圆角 */  
                    padding:5px
                }  
        """)
        for i in range(1, 9):
            getattr(self.ui, f"kf_zs_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=1,
                                                                      tab_index=i - 1, page=self.kf_current_page,
                                                                      sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                      start_date=self.start_date,
                                                                      end_date=self.end_date)
        for i in range(1, 7):
            getattr(self.ui, f"kf_zq_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=4,
                                                                      tab_index=i - 1, page=self.kf_current_page,
                                                                      sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                      start_date=self.start_date,
                                                                      end_date=self.end_date)
        for i in range(1, 9):
            getattr(self.ui, f"kf_qdii_tab_{i}").mousePressEvent = partial(self.load_kf_data_params_pre, index=5,
                                                                        tab_index=i - 1, page=self.kf_current_page,
                                                                        sort_dep=self.sort_dep_index, sort=self.kf_sort,
                                                                        start_date=self.start_date,
                                                                        end_date=self.end_date)
        self.update_kf_page()


    def kfjj_tableview_style(self, index, data, data_info):
        self.kf_total_page = data_info["allPages"]
        self.select_kfjj_btn(index)
        self.model = QStandardItemModel()
        self.model.setHorizontalHeaderLabels(self.tb_header_lb)
        for row in data:
            row_items = [QStandardItem(str(item)) for item in row]
            self.model.appendRow(row_items)
        self.ui.tableView.setModel(self.model)
        # self.tableView.setStyleSheet("QTableView::item { text-align: center; }")
        # 设置自定义委托，使文本水平居中
        # delegate = CenteredItemDelegate(self.tableView)
        # self.tableView.setItemDelegate(delegate)
        self.ui.tableView.setColumnWidth(0, 73)
        self.ui.tableView.setColumnWidth(1, 260)
        for i in range(4, 16):
            self.ui.tableView.setColumnWidth(i, 78)
        # 设置表头字段行的样式
        self.ui.tableView.horizontalHeader().setStyleSheet(
            "QHeaderView::section {"
            "   background-color: #FF81ED;"  # 背景颜色
            "   color: white;"  # 字体颜色
            "   padding: 5px;"  # 内边距
            "   font-weight: bold;"  # 字体加粗
            "}"
        )
        self.ui.tail_page_lb_kf.setText(str(self.kf_total_page))
        self.update_kf_page()
        delegate = CustomDelegate()
        self.ui.tableView.setItemDelegate(delegate)
