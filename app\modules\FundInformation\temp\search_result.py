import time
import json
import urllib.parse

import requests
from bs4 import BeautifulSoup
from PyQt6.QtCore import QThread, pyqtSignal

class get_search_title():

    def __init__(self, keyword, page):
        super().__init__()
        self.keyword = keyword
        self.page = page
        self.url = []
        self.mediaName = []
        self.title_or = []
        self.title = []
        self.content = []
        self.date = []
        
        # 生成动态时间戳和回调函数名
        timestamp = int(time.time() * 1000)
        self.callback = f"jQuery35106516115308856391_{timestamp}"
        
        self.header = {
          "Host": "search-api-web.eastmoney.com",
          "Connection": "keep-alive",
          "sec-ch-ua-platform": "\"Windows\"",
          "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 Edg/*********",
          "sec-ch-ua": "\"Not;A=Brand\";v=\"99\", \"Microsoft Edge\";v=\"139\", \"Chromium\";v=\"139\"",
          "sec-ch-ua-mobile": "?0",
          "Accept": "*/*",
          "Sec-Fetch-Site": "same-site",
          "Sec-Fetch-Mode": "no-cors",
          "Sec-Fetch-Dest": "script",
          "Referer": "https://so.eastmoney.com",
          "Accept-Encoding": "gzip, deflate, br, zstd",
          "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6",
          "Cookie": "_qimei_uuid42=1971f092f20100c8520e923a54ec5194e79fca7ae3; st_nvi=FC7DZFrbPA0oIgj5rXYEv82ef; _qimei_fingerprint=2cf2be01d9c2ffcda61387b8e25138d3; _qimei_i_3=2ddb57d3c45f5189c996aa63088773b3f7ebadf4415c02d0b7862c5c26c7293a323161943c89e2bd95b0; _qimei_h38=; nid=0be1cbd9749ac36e1ca59b9716dbc216; nid_create_time=1753926453005; gvi=IaOd6G-zLK4Ce1YihFYxU3fb5; gvi_create_time=1753926453006; qgqp_b_id=a907f2dbaff1a7c5ebe17be718de468c; EMFUND0=07-31%2016%3A04%3A23@%23%24%u534E%u590F%u5927%u76D8%u7CBE%u9009%u6DF7%u5408A@%23%24000011; EMFUND1=07-31%2012%3A42%3A56@%23%24%u534E%u590F%u503A%u5238A/B@%23%24001001; EMFUND2=08-03%2018%3A14%3A19@%23%24%u5FB7%u90A6%u7A33%u76C8%u589E%u957F%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408C@%23%24018463; EMFUND3=08-04%2012%3A14%3A30@%23%24%u5609%u5B9E%u4E92%u878D%u7CBE%u9009%u80A1%u7968A@%23%24006603; EMFUND4=08-04%2012%3A35%3A13@%23%24%u7533%u4E07%u83F1%u4FE1%u6D88%u8D39%u589E%u957F%u6DF7%u5408A@%23%24310388; EMFUND5=08-07%2023%3A04%3A55@%23%24%u4E2D%u4FE1%u5EFA%u6295%u5317%u4EA4%u6240%u7CBE%u9009%u4E24%u5E74%u5B9A%u5F00%u6DF7%u5408A@%23%24016303; EMFUND6=08-07%2023%3A10%3A30@%23%24%u65B0%u534E%u5229%u7387%u503A%u503A%u5238E@%23%24016295; EMFUND7=08-08%2011%3A27%3A07@%23%24%u5FB7%u90A6%u946B%u661F%u4EF7%u503C%u7075%u6D3B%u914D%u7F6E%u6DF7%u5408A@%23%24001412; EMFUND8=08-08%2011%3A31%3A38@%23%24%u534E%u590F%u5317%u4EA4%u6240%u7CBE%u9009%u4E22%u5E74%u5B9A%u5F00%u6DF7%u5408%u53D1%u8D77%u5F0F@%23%24014283; EMFUND9=08-08 11:34:38@#$%u6C47%u6DFB%u5BCC%u5065%u5EB7%u751F%u6D3B%u4E00%u5E74%u6301%u6709%u6DF7%u5408A@%23%24011826; st_si=62826458616042; fullscreengg=1; fullscreengg2=1; emshistory=%5B%22%E5%86%9B%E5%B7%A5%22%5D; _qimei_i_1=41bb2d84cb27; websitepoptg_api_time=1754973537603; st_asi=delete; st_pvi=27189901722403; st_sp=2025-03-03%2018%3A51%3A02; st_inirUrl=https%3A%2F%2Fcn.bing.com%2F; st_sn=35; st_psi=20250812124021670-118000300904-4051810155"
        }
        
        # 使用干净的base_url
        self.base_url = "https://search-api-web.eastmoney.com/search/jsonp"
        
        # 构建params参数
        param_data = {
            "uid": "",
            "keyword": keyword,  # 直接使用keyword，不需要URL编码
            "type": ["cmsArticleWebOld"],
            "client": "web",
            "clientType": "web", 
            "clientVersion": "curr",
            "param": {
                "cmsArticleWebOld": {
                    "searchScope": "default",
                    "sort": "default",
                    "pageIndex": self.page,
                    "pageSize": 10,
                    "preTag": "<em>",
                    "postTag": "</em>"
                }
            }
        }
        
        self.params = {
            "cb": self.callback,
            "param": json.dumps(param_data, ensure_ascii=False),
            "_": timestamp + 1
        }


    def re_cal_(self,str_,keyword,max_):
        re_str_l="<em>"+keyword+"</em>"
        str_=str_.replace(re_str_l,keyword)
        str_=self.max_len_(str_,max_)
        str_=str_.replace(keyword,re_str_l)
        return str_

    def max_len_(self,str_,max_,):
        len_=len(str_)
        if len_ > max_:
            data= str_[:max_]
        else:
            data = str_[:len_]
        return data

    def blue_style(self,keyword,title):
        print(title)
        red_style = f"""<span style="color:red"><strong>{keyword}</strong></span>"""
        count=title.count(red_style)
        blue_start = f"""<span style="color:#293598">"""
        blue_end = f"""</span>"""
        title_ = ""
        temp_l = []
        if count==0:
            return blue_start+title+blue_end
        else:
            l = title.split(red_style)
            print(l)
            for i in l:
                temp = blue_start + i + blue_end
                temp_l.append(temp)
            print(temp_l)
            for i in range(count):
                title_+=temp_l[i]
                title_+=red_style
            title_+=temp_l[-1]
            return title_


    def run(self):
        response = requests.get(url=self.base_url, headers=self.header, params=self.params,verify=False)
        data_text = response.text
        
        # 移除JSONP回调包装
        json_start = data_text.find('(') + 1
        json_end = data_text.rfind(')')
        json_str = data_text[json_start:json_end]
        
        data = json.loads(json_str)
        temp_title,temp_content,temp_date=[],[],[]
        print(data)

        for i in data["result"]["cmsArticleWebOld"]:
            temp_date.append(i["date"])
            self.url.append(i["url"])
            temp_title.append(" "+i["title"])
            temp_content.append(i["content"])
            self.mediaName.append(["mediaName"])
            self.title_or.append(i["title"].replace("<em>","").replace("</em>",""))
        #对标题限制
        temp_title=[self.re_cal_(i,self.keyword,33) for i in temp_title]
        temp_content=[self.re_cal_(i,self.keyword,86) for i in temp_content]

        red_style = f"""<span style="color:red"><strong>{self.keyword}</strong></span>"""

        gray_style_start = f"""<span style="color:gray">"""
        gray_style_end = f"""&nbsp;-&nbsp;</span>"""
        # 标题处理
        self.title= [i.replace(f"<em>{self.keyword}</em>", red_style) for i in temp_title]
        print(self.title)
        self.title= [self.blue_style(self.keyword,i) for i in self.title]

        content_ = [i.replace(f"<em>{self.keyword}</em>", red_style) for i in temp_content]
        self.date = [gray_style_start + i + gray_style_end for i in temp_date]
        for i in range(10):
            self.content.append(self.date[i] + content_[i])
        # self.finished.emit([self.date,self.url,self.title,self.content,self.mediaName,self.title_or])
get_search_title=get_search_title("军工",1)

#fix
get_search_title.run()
print(get_search_title.title)
