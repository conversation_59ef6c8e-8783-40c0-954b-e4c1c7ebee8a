from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor
from PyQt6.QtWidgets import Q<PERSON>eaderView, QTableWidgetItem, QWidget, QHBoxLayout, QPushButton, QLineEdit, QMessageBox

from app.modules.FundCompany_.api.get_company_data import get_company_data
from app.modules.FundCompany_.src import style
from main_w import Ui_MainWindow


class FundCompany_:
    def __init__(self, ui: Ui_MainWindow):
        self.ui = ui
        self.init_setup()

    def _get_ui(self) -> Ui_MainWindow:
        """获取 UI 实例，提供完整的代码提示"""
        return self.ui

    def init_setup(self):  # 初始化变量
        # 使用局部变量获得代码提示
        ui = self.ui  # type: Ui_MainWindow

        ui.company_widget.hide()
        self.company_status = False
        self.company_page_index=0

        ui.jj_cpmpany_lb.mousePressEvent = self.toggle_company_home
        ui.company_return_lb.mousePressEvent = self.toggle_company_home
        ui.company_search.mousePressEvent = self.search_company
        # self.init_pages_status()

    def search_company(self,event):
        if self.ui.company_lineEdit.text()!="":
            l=[]
            for i in self.company_data:
                if self.ui.company_lineEdit.text() in i[1]:
                    l.append(i)
            self.load_table_data(l)
        else:
            self.company_page_index=0
            self.load_table_data(self.company_data[self.company_page_index * 20:self.company_page_index * 20 + 20])
   

    def show_company_(self):
        self.ui.company_widget.show()
        self.load_company_list()


    def load_company_list(self):
        try:
            self.worker_thread=get_company_data()
            self.worker_thread.finished.connect(self.task_finished_company_data)
            self.worker_thread.start()
        except Exception as e:
            print(e)

    def task_finished_company_data(self,data):
        try:
            self.total_pages=len(data)//20+1
            self.company_data=data
            self.load_table_data(self.company_data[self.company_page_index*20:self.company_page_index*20+20])
            self.init_pages_status()

        except Exception as e:
            print(f"task_finished_company_data error: {e}")


    def toggle_company_home(self,event):
        print("ppp")
        if self.company_status:
            self.ui.company_widget.hide()
            self.company_status = False
        else:
            self.show_company_()
            self.company_status = True

    def load_table_data(self, data):
        headers = ["代码","基金公司","成立时间","天相评级","全部管理规模（亿元）","全部基金数","总经理","最新日期"]
        # data=data[self.company_page_index*20:self.company_page_index*20+20]
        try:
            self.ui.company_tableWidget.setColumnCount(len(headers))
            self.ui.company_tableWidget.setHorizontalHeaderLabels(headers)
            self.ui.company_tableWidget.horizontalHeader().setSectionResizeMode(QHeaderView.ResizeMode.Stretch)
            self.ui.company_tableWidget.horizontalHeader().setStyleSheet(style.QHeaderView_style)
            self.ui.company_tableWidget.setRowCount(len(data))
            for row_idx, row_data in enumerate(data):
                for col_idx, cell_data in enumerate(row_data):
                    item = QTableWidgetItem(str(cell_data))
                    item.setTextAlignment(Qt.AlignmentFlag.AlignCenter)
                    if col_idx in [0, 1, ]:
                        item.setForeground(QColor("#294EBC"))
                    self.ui.company_tableWidget.setItem(row_idx, col_idx, item)
            row_height = 40  # 设置行高为 30 像素
            for row in range(self.ui.company_tableWidget.rowCount()):
                self.ui.company_tableWidget.setRowHeight(row, row_height)
            start = (self.company_page_index) * 20 + 1
            end = start + len(data)
            self.ui.company_tableWidget.setVerticalHeaderLabels([str(i) for i in range(start, end)])
        except Exception as e:
            print(f"load_table_data error: {e}")

    def page_lb_style(self,page_name,status):
        if status:
            getattr(self.ui, f"{page_name}").setStyleSheet(style.QLabel_page_yes)
        else:
            getattr(self.ui, f"{page_name}").setStyleSheet(style.QLabel_page_no)

    def init_pages_status(self, ):
        self.ui.company_current_page.setText(f"{self.company_page_index+1}/{self.total_pages}")
        self.ui.company_pre_page.mousePressEvent = self.sub_page
        self.ui.company_after_page.mousePressEvent = self.add_page
        self.ui.company_goto_btn.mousePressEvent = self.goto_page
        self.update_page_status()

    def sub_page(self, event):
        if self.company_page_index > 0:
            self.company_page_index -= 1
            self.update_page_status()
            self.load_table_data(self.company_data[self.company_page_index*20:self.company_page_index*20+20])
            self.ui.company_current_page.setText(f"{self.company_page_index + 1}/{self.total_pages}")

    def add_page(self, event):
        if self.company_page_index < self.total_pages:
            self.company_page_index += 1
            self.update_page_status()
            self.load_table_data(self.company_data[self.company_page_index*20:self.company_page_index*20+20])
            self.ui.company_current_page.setText(f"{self.company_page_index + 1}/{self.total_pages}")
    #
    def goto_page(self, event):
        page_ = self.ui.company_lineEdit_1.text()
        if page_:
            page_ = int(page_)-1
            if page_ >0 and page_ <= self.total_pages:
                self.company_page_index = page_
                self.update_page_status()
                self.load_table_data(self.company_data[self.company_page_index*20:self.company_page_index*20+20])
                self.ui.company_lineEdit_1.clear()
                self.ui.company_current_page.setText(f"{self.company_page_index + 1}/{self.total_pages}")
            else:
                QMessageBox.information(self.ui.company_widget, "提示", "请输入正确的页码")
        else:
            QMessageBox.information(self.ui.company_widget, "提示", "请输入正确的页码")

    def update_page_status(self, ):
        if self.company_page_index > 0:
            self.page_lb_style("company_pre_page", True)
        else:
            self.page_lb_style("company_pre_page", False)
        if self.company_page_index < self.total_pages-1:
            self.page_lb_style("company_after_page", True)
        else:
            self.page_lb_style("company_after_page", False)


