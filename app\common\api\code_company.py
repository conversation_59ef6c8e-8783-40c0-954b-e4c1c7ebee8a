from pypinyin import pinyin, Style
class FundCompany():
    def __init__(self):
        self.file_path=r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\api\txt\fund_company.txt"
        self.company_list=self.get_company_list()
        self.company_code_name_dict=self.return_code_name_dict()

    def get_pinyin_initial(self,name):
        """获取拼音首字母并转为大写"""
        initials = pinyin(name, style=Style.FIRST_LETTER)
        return str(initials[0][0]).upper()

    def list_to_dict(self,data):
        """将列表转换为字典"""
        result = {}
        for code, name in data:
            initial = self.get_pinyin_initial(name)
            if initial not in result:
                result[initial] = []
            result[initial].append([code, name.replace("基金","")])
        return result

    def get_company_dict_capital(self,):
        """获取公司列表"""
        result_dict = self.list_to_dict(self.company_list)
        return {k: v for k, v in sorted(result_dict.items())}


    def get_company_list(self):
        with open(self.file_path, "r", encoding="utf-8") as file:
            data = file.read()
            return eval(data)

    def return_code_name_dict(self):
        di={}
        for i in self.company_list:
            di[i[1].replace("基金","")]=i[0]
        return di


FundCompany=FundCompany()
print(FundCompany.return_code_name_dict())

