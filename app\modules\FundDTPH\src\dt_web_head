<html>
        <style>
        /* 表格整体样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            font-family: Arial, sans-serif;
            margin-top: -10px;
        }

        /* 表头样式 */
        thead th {
            background-color: #f4f4f4;
            padding: 10px;
            text-align: left;
            border-bottom: 2px solid #ddd;
            vertical-align: middle;
        }
        /* 表体样式 */
        tbody td {
            padding: 10px;
            border-bottom: 1px solid #ddd;
        }

        /* 交替行背景色 */
        tbody tr:nth-child(odd) {
            background-color: #f9f9f9;
        }

        /* 鼠标悬停效果 */
        tbody tr:hover {
            background-color: #f1f1f1;
        }



        /* 正收益绿色显示 */
        .zhang {
            color: red;
        }

        /* 负收益红色显示 */
        .die {
            color: green;
        }

        /* 链接样式 */
        a {
            color: #007bff;
            text-decoration: none;
        }

        a:hover {
            text-decoration: underline;
        }
    </style>
    <body>