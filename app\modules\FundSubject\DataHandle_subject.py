import json

from PyQt6.QtGui import QColor


class DataHandle_subject():
    def __init__(self, ):
        pass

    def calculate_color(self, value,data):
        """根据涨幅计算颜色，并返回 (背景色, 字体色)"""
        if value > 0:
            # 红色渐变（涨幅越大越红）
            positive_values = [d[1] for d in data if d[1] > 0]
            value_max = max(positive_values) if positive_values else 1e-6
            ratio = min(1.0, value / value_max)
            # 从 #FDE1E1（浅红）到 #F54545（深红）
            red = int(253 - (253 - 245) * ratio)
            green = int(229 - (229 - 69) * ratio)
            blue = int(229 - (229 - 69) * ratio)
            bg_color = QColor(red, green, blue)

            # 判断是否需要调整字体颜色
            threshold_red = QColor(251, 180, 180)  # #FBB4B4
            if (red < threshold_red.red() and
                    green < threshold_red.green() and
                    blue < threshold_red.blue()):
                font_color = QColor(255, 255, 255)  # 白色
            else:
                font_color = QColor(210, 0, 0)  # 深红色 #D20000

        elif value < 0:
            # 绿色渐变（跌幅越大越绿）
            negative_values = [abs(d[1]) for d in data if d[1] < 0]
            value_max = max(negative_values) if negative_values else 1e-6
            ratio = min(1.0, abs(value) / value_max)
            # 从 #CFEACF（浅绿）到 #139814（深绿）
            red = int(207 - (207 - 19) * ratio)
            green = int(234 - (234 - 152) * ratio)
            blue = int(207 - (207 - 20) * ratio)
            bg_color = QColor(red, green, blue)

            # 判断是否需要调整字体颜色
            threshold_green = QColor(139, 204, 140)  # #8BCC8C
            if (red < threshold_green.red() and
                    green < threshold_green.green() and
                    blue < threshold_green.blue()):
                font_color = QColor(255, 255, 255)  # 白色
            else:
                font_color = QColor(0, 96, 56)  # 深绿色 #006038

        else:
            # 涨幅为0，使用灰色
            bg_color = QColor(243, 243, 243)  # #F3F3F3
            font_color = QColor(0, 0, 0)  # 黑色（默认）

        return bg_color, font_color

    def format_value(self, value):
        """将资金流入格式化为万/亿单位"""
        if abs(value) < 100000000:  # -1亿 < value < 1亿
            return f"{value / 10000:.2f}万"
        else:  # value <= -1亿 或 value >= 1亿
            return f"{value / 100000000:.2f}亿"

    def get_bk_code(self,bk_name):
        with open(r"D:\1resourse\project1\PycharmProjects\fund_app_ex\app\common\data\bk.txt", "r", encoding="utf-8")as f:
            d=f.read()
        bk_dict = {}
        data = json.loads(d)["Data"]
        for i in data:
            for j in data[i]:
                bk_dict[j["INDEXNAME"]] = j["INDEXCODE"]
        return bk_dict[bk_name]

DataHandle_subject=DataHandle_subject()
# DataHandle_subject.get_bk_code("电机")
