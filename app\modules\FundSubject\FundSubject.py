from functools import partial

from PyQt6.QtCore import Qt
from PyQt6.QtGui import QColor, QPalette
from PyQt6.QtWidgets import <PERSON><PERSON><PERSON><PERSON>ayout, QLabel, QWidget

from app.modules.FundSubject.DataHandle_subject import DataHandle_subject
from app.modules.FundSubject.FundSubSubject import FundSubSubject
from app.modules.FundSubject.api.get_subject_data import get_subject_data
from app.modules.FundSubject.src import style


class FundSubject:
    def __init__(self, ui):
        self.bg_color_list = []
        self.ui = ui
        self.init_setup()

    def init_setup(self):  # 初始化变量
        self.subject_type="0"
        self.subject_sort="syl"
        self.subject_time="D"
        self.subject_widget_status = False
        self.ui.fund_subject_widget.hide()
        self.ui.subject_sub_widget.hide()
        self.ui.subject_return_lb.mousePressEvent = self.toggle_widget_index
        self.ui.subject_jj_lb.mousePressEvent = self.show_subject_widget

    def show_subject_widget(self,event):
        try:
            self.ui.fund_subject_widget.show()
            self.subject_widget_status=True
            self.init_condition_lb()
            self.load_subject_data(self.subject_type,self.subject_sort,self.subject_time)
        except Exception as e:
            print(e)

    def load_subject_data(self,type,st,time):
        try:
            print(type,st,time)
            self.worker_thread=get_subject_data(type,st,time)
            self.worker_thread.finished.connect(self.task_finished_subject)
            self.worker_thread.start()
        except Exception as e:
            print(f"load_subject_data error{e}")

    def task_finished_subject(self,data):
        self.subject_data=data
        print(data)
        self.grid_ui_mb(data)


    def grid_ui_mb(self,data):
        try:
            # 创建一个 QWidget 来作为滚动区域的内容
            scroll_widget = QWidget()

            # 创建网格布局
            grid_layout = QGridLayout()
            grid_layout.setSpacing(2)
            if self.subject_sort == "syl":
                label_style = "<span style='font-size: 14px;'>{}</span><br><strong><span style='font-size: 15px;'>{:.2f}%</span></strong>"
            else:

                label_style = "<span style='font-size: 14px;'>{}</span><br><strong><span style='font-size: 15px;'>{}</span></strong>"

            # 填充网格布局
            for row_idx, (sector, value) in enumerate(data):
                row = row_idx // 10
                col = row_idx % 10
                if self.subject_sort != "syl":
                    format_value = DataHandle_subject.format_value(value)
                else:
                    format_value=value
                label = QLabel(label_style.format(sector, format_value))
                label.setAlignment(Qt.AlignmentFlag.AlignCenter)
                label.setWordWrap(True)

                bg_color, font_color = DataHandle_subject.calculate_color(value, data)
                self.bg_color_list.append(bg_color.name())

                # palette = label.palette()
                # palette.setColor(QPalette.ColorRole.Window, bg_color)
                # label.setAutoFillBackground(True)
                # label.setPalette(palette)

                label.setStyleSheet(f"color: {font_color.name()};padding:4px;background-color:{bg_color.name()}")
                label.mousePressEvent = lambda event, s=sector, v=value,: self.on_label_click(s, v,)
                grid_layout.addWidget(label, row, col)

            # 将网格布局设置为滚动区域内容的布局
            scroll_widget.setLayout(grid_layout)

            # 将滚动区域的内容设置为刚才创建的 widget
            self.ui.subject_scrollArea.setWidget(scroll_widget)
        except Exception as e:
            print(f"grid_ui_mb error{e}")

    def init_condition_lb(self):
        for i in range(1, 4):
            getattr(self.ui, f"subject_type_{i}").setStyleSheet(style.QLabel_lb_normal)
            getattr(self.ui, f"subject_type_{i}").mousePressEvent =partial(self.type_change,index=i)
        self.ui.subject_type_1.setStyleSheet(style.QLabel_lb_selected)
        for i in range(1, 3):
            getattr(self.ui, f"subject_sort_{i}").setStyleSheet(style.QLabel_lb_normal)
            getattr(self.ui, f"subject_sort_{i}").mousePressEvent =partial(self.sort_change,index=i)
        self.ui.subject_sort_1.setStyleSheet(style.QLabel_lb_selected)
        for i in range(1, 7):
            getattr(self.ui, f"subject_zf_time_{i}").setStyleSheet(style.QLabel_lb_normal)
            getattr(self.ui, f"subject_zf_time_{i}").mousePressEvent =partial(self.time_change,index=i)
        self.ui.subject_zf_time_1.setStyleSheet(style.QLabel_lb_selected)
        for i in range(1, 5):
            getattr(self.ui, f"subject_zj_time_{i}").setStyleSheet(style.QLabel_lb_normal)
            getattr(self.ui, f"subject_zj_time_{i}").mousePressEvent =partial(self.time_change,index=i)
        self.ui.subject_zj_time_1.setStyleSheet(style.QLabel_lb_selected)
        self.toggle_sort_widget()

    def type_change(self,event,index):
        try:
            tt = ["0", "001002", "001003"]
            self.subject_type=tt[index-1]
            for i in range(1, 4):
                getattr(self.ui, f"subject_type_{i}").setStyleSheet(style.QLabel_lb_normal)
            getattr(self.ui, f"subject_type_{index}").setStyleSheet(style.QLabel_lb_selected)
            self.load_subject_data(self.subject_type, self.subject_sort, self.subject_time)
        except Exception as e:
            print(f"type_change error{e}")

    def toggle_sort_widget(self,):
        if self.subject_sort=="syl":
            self.ui.subject_zj_widget.hide()
            self.ui.subject_zf_widget.show()
        else:
            self.ui.subject_zf_widget.hide()
            self.ui.subject_zj_widget.show()

    def sort_change(self,event,index):
        try:
            if index==1:
                self.subject_sort="syl"
                self.ui.subject_zf_time_1.setStyleSheet(style.QLabel_lb_selected)
                self.subject_time="D"
            else:
                self.subject_sort="zjlr"
                self.ui.subject_zj_time_1.setStyleSheet(style.QLabel_lb_selected)
                self.subject_time = "FLOW"
            for i in range(1, 3):
                getattr(self.ui, f"subject_sort_{i}").setStyleSheet(style.QLabel_lb_normal)
            getattr(self.ui, f"subject_sort_{index}").setStyleSheet(style.QLabel_lb_selected)
            self.load_subject_data(self.subject_type, self.subject_sort, self.subject_time)
            self.toggle_sort_widget()
        except Exception as e:
            print(f"sort_change error:{e}")


    def time_change(self,event,index):
        try:
            match (self.subject_sort):
                case "syl":
                    st=["D","W","M","Q","Y","SY"]
                    self.subject_time=st[index-1]
                    for i in range(1, 7):
                        getattr(self.ui, f"subject_zf_time_{i}").setStyleSheet(style.QLabel_lb_normal)
                    getattr(self.ui, f"subject_zf_time_{index}").setStyleSheet(style.QLabel_lb_selected)
                case "zjlr":
                    st=["FLOW","FLOW_W","FLOW_M","FLOW_Q"]
                    self.subject_time=st[index-1]
                    for i in range(1, 5):
                        getattr(self.ui, f"subject_zj_time_{i}").setStyleSheet(style.QLabel_lb_normal)
                    getattr(self.ui, f"subject_zj_time_{index}").setStyleSheet(style.QLabel_lb_selected)
            print(self.subject_time)
            self.load_subject_data(self.subject_type, self.subject_sort, self.subject_time)
        except Exception as e:
            print(f"time_change error:{e}")
    def on_label_click(self, sector, value, ):
        """当点击 QLabel 时，输出对应的 sector 和 value"""
        try:
            self.subject_name=sector
            l=[]
            for i in self.subject_data:
                l.append(i[0])
            self.subject_code=DataHandle_subject.get_bk_code(self.subject_name)
            self.subject_sub=FundSubSubject(self.ui,self.subject_name,self.subject_code,self.subject_data,self.bg_color_list[l.index(self.subject_name)])
        except Exception as e:
            print(f"on_label_click error:{e}")


    def toggle_widget_index(self, event):
        if  self.subject_widget_status:
            self.ui.fund_subject_widget.hide()
            self.subject_widget_status = False
        else:
            self.show_subject_widget(event)
            self.ui.fund_subject_widget.show()
            self.subject_widget_status = True



