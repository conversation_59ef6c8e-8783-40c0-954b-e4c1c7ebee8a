import asyncio
from datetime import datetime
from functools import partial

from PyQt6.QtCore import Qt, QDate, QUrl
from PyQt6.QtGui import QPixmap
from PyQt6.QtWidgets import QTableWidgetItem, QMessageBox, QListWidgetItem, QWidget, QLineEdit

from app.modules.FundCalculator.common_ui import common_
from main_w import Ui_MainWindow


class Calculator_dk:
    def __init__(self, ui:Ui_MainWindow,):
        self.ui = ui
        self.common_ui = common_(self.ui)
        self.init_setup()

    def init_setup(self):
        self.ui.calculator_type_lb.setText("贷款类计算器")
        self.sub_listWidget_list = ["个人贷款计算器", ]
    def show_dk_grdk_widget(self):
        try:
            self.ui.plainTextEdit_2.hide()
            self.common_ui.update_result_lb(["每月支付本息：", "累计支付利息：", "累计还款总额："])
            self.common_ui.clear_result(["元", "元", "元"])
            self.ui.dk_grdk_input_3.currentTextChanged.connect(self.grdk_combox_select)
            self.ui.calculator_result_btn.mousePressEvent = self.return_result_dk_grdk
            self.ui.dk_grdk_input_4.setText("6.15")

            def clear_dk_grdk(event):
                self.common_ui.clear_result(["元", "元", "元"])
                self.ui.dk_grdk_input_1.setText("")
                self.ui.dk_grdk_input_2.setText("")
                self.ui.dk_grdk_input_4.setText("6.15")
                self.ui.plainTextEdit_2.hide()

            self.ui.calculator_reset_btn.mousePressEvent = clear_dk_grdk
        except Exception as e:
            print(e)


    def return_result_dk_grdk(self, event):
        try:
            result = []
            money = self.ui.dk_grdk_input_1.text()
            year_ = self.ui.dk_grdk_input_2.text()
            rate = self.ui.dk_grdk_input_4.text()
            if self.ui.dk_grdk_input_3.currentIndex() == 0:
                self.repayment_type = "bx"
            if self.common_ui.check_calculator_num(money) and self.common_ui.check_calculator_num(year_) and self.common_ui.check_calculator_num(
                    rate):
                """
                    计算等额本息还款的相关信息
                    :param P: 贷款本金
                    :param i: 年利率
                    :param n: 还款总月数
                    :return: 每月还款额，累计支付利息，累计还款总额
                    """
                if self.repayment_type == "bx":
                    self.ui.plainTextEdit_2.hide()
                    # 计算月利率
                    r = float(rate) / 1200
                    n = int(year_) * 12
                    # 计算每月还款额
                    A = float(money) * r * (1 + r) ** n / ((1 + r) ** n - 1)
                    # 计算累计还款总额
                    result3 = A * n
                    # 计算累计支付利息
                    result2 = result3 - float(money)
                    result1 = A
                    result = [result1, result2, result3]
                    for i in range(1, 4):
                        getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
                else:
                    """
                        计算等额本金还款的每月支付本息、累计支付利息和累计还款总额
                        :param P: 贷款本金
                        :param annual_interest_rate: 年利率
                        :param years: 还款年限
                        """
                    self.ui.plainTextEdit_2.show()
                    str1 = ""
                    # 计算月利率
                    monthly_interest_rate = float(rate) / 1200
                    # 计算还款总月数
                    total_months = int(year_) * 12
                    # 计算每月偿还本金
                    monthly_principal = float(money) / total_months
                    total_interest_paid = 0
                    total_repayment = 0

                    # 遍历每个月
                    for month in range(1, total_months + 1):
                        # 计算当月利息
                        monthly_interest = (float(money) - monthly_principal * (month - 1)) * monthly_interest_rate
                        # 计算当月还款额
                        monthly_repayment = monthly_principal + monthly_interest
                        # 累计支付利息和累计还款总额
                        total_interest_paid += monthly_interest
                        total_repayment += monthly_repayment
                        # 输出当月信息
                        str1 += f"{month}月:{monthly_repayment:.2f}元，累计支付利息:{total_interest_paid:.2f}元.\n"
                    result1 = str1
                    result2 = total_interest_paid
                    result3 = total_repayment
                    result = [result1, result2, result3]
                    self.ui.plainTextEdit_2.setPlainText(result1)


                    for i in range(2, 4):
                        getattr(self.ui, f"ck_result_{i}").setText("{:.2f}".format(result[i - 1]))
        except Exception as e:
            print(e)

    def show_dk_widget(self):
            self.ui.dk_total_widget.show()
            self.show_dk_grdk_widget()
            self.common_ui.clear_result(["元", "元", "元"])

    def grdk_combox_select(self, text):
        match (text):
            case "等额本息还款":
                self.repayment_type = "bx"
            case "等额本金还款":
                self.repayment_type = "bj"